---
description: 
globs: 
alwaysApply: false
---
---
description: 该规则解释了 React 组件模式、hooks 使用方法和最佳实践。
globs: **/*.jsx,**/*.tsx
alwaysApply: false
---

# React 规则

## 组件结构
- 优先使用函数组件而非类组件
- 保持组件小巧且专注
- 将可复用逻辑提取到自定义 hook 中
- 使用组合而非继承
- 使用 TypeScript 实现适当的 prop 类型
- 将大型组件拆分为更小、更专注的组件

## Hooks
- 遵循 Hooks 的规则
- 使用自定义 hooks 实现可复用逻辑
- 保持 hooks 专注且简单
- 在 useEffect 中使用适当的依赖数组
- 在需要时在 useEffect 中实现清理功能
- 避免嵌套 hooks

## 状态管理
- 使用 useState 管理组件本地状态
- 使用 useReducer 处理复杂状态逻辑
- 使用 Context API 共享状态
- 将状态尽可能靠近使用它的地方
- 通过适当的状态管理避免 prop drilling
- 仅在必要时使用状态管理库

## 性能
- 实现适当的记忆化(useMemo, useCallback)
- 对开销大的组件使用 React.memo
- 避免不必要的重新渲染
- 实现适当的懒加载
- 在列表中使用适当的 key 属性
- 分析并优化渲染性能

## 表单
- 对表单输入使用受控组件
- 实现适当的表单验证
- 正确处理表单提交状态
- 显示适当的加载和错误状态
- 对复杂表单使用表单库
- 为表单实现适当的可访问性

## 错误处理
- 实现 Error Boundaries
- 正确处理异步错误
- 显示用户友好的错误信息
- 实现适当的备用 UI
- 适当记录错误
- 优雅处理边缘情况

## 测试
- 为组件编写单元测试
- 为复杂流程实现集成测试
- 使用 React Testing Library
- 测试用户交互
- 测试错误场景
- 实现适当的模拟数据

## 可访问性
- 使用语义化 HTML 元素
- 实现适当的 ARIA 属性
- 确保键盘导航
- 使用屏幕阅读器测试
- 管理焦点
- 为图片提供适当的 alt 文本

## 代码组织
- 将相关组件组织在一起
- 使用适当的文件命名约定
- 实现适当的目录结构
- 保持样式靠近组件
- 使用适当的导入/导出
- 记录复杂的组件逻辑
