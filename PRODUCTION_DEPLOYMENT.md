# SDC工具系统生产部署指南

## 📋 部署前检查清单

### 1. 环境变量配置

#### 必需的环境变量
```bash
# 数据库配置
DATABASE_URL=****************************************/database_name

# Redis配置
REDIS_URL=redis://host:6379

# OSS配置（生产环境必须配置真实bucket）
OSS_REGION=cn-hangzhou
OSS_BUCKET_USER_INPUT=logiccore-prod-user-input
OSS_BUCKET_JOB_RESULTS=logiccore-prod-job-results
OSS_BUCKET_JOB_LOGS=logiccore-prod-job-logs

# 阿里云凭证
ALIYUN_ACCESS_KEY_ID=your_production_access_key
ALIYUN_ACCESS_KEY_SECRET=your_production_secret_key

# 临时目录配置
TEMP_JOBS_DIR=/data/logiccore_jobs

# Worker配置
WORKER_ID=worker-prod-01
ECS_INSTANCE_ID=ecs-prod-instance
ECS_TOTAL_CPU=16
ECS_TOTAL_MEMORY_GB=64
JOB_CPU_REQUEST=4
JOB_MEMORY_REQUEST_GB=8
```

### 2. 目录结构准备

#### ECS服务器目录结构
```
/opt/logiccore/
├── backend/                 # 后端应用
├── frontend/               # 前端应用
├── stuff/
│   ├── tool_template/      # 工具模板文件
│   │   ├── sdcgen/        # SDC工具模板
│   │   ├── clkgen/        # 时钟生成工具模板
│   │   └── memgen/        # 内存数据生成工具模板
│   └── docker/            # Docker镜像文件
├── logs/                  # 应用日志
└── data/
    └── logiccore_jobs/    # 临时任务目录
```

#### 创建必要目录
```bash
sudo mkdir -p /opt/logiccore/{backend,frontend,stuff/tool_template,logs,data/logiccore_jobs}
sudo mkdir -p /opt/logiccore/stuff/tool_template/{sdcgen,clkgen,memgen}
sudo chown -R logiccore:logiccore /opt/logiccore
sudo chmod -R 755 /opt/logiccore
```

### 3. OSS Bucket配置

#### 创建生产环境Bucket
```bash
# 用户输入文件存储
logiccore-prod-user-input

# 任务结果存储  
logiccore-prod-job-results

# 任务日志存储
logiccore-prod-job-logs
```

#### Bucket权限配置
- **读写权限**：仅限应用服务账号
- **生命周期管理**：
  - 用户输入文件：30天后删除
  - 任务结果：90天后转为低频存储
  - 任务日志：180天后删除

### 4. 数据库配置

#### PostgreSQL配置
```sql
-- 创建生产数据库
CREATE DATABASE logiccore_prod;

-- 创建应用用户
CREATE USER logiccore_app WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE logiccore_prod TO logiccore_app;

-- 运行数据库迁移
npx prisma migrate deploy
```

#### Redis配置
```bash
# Redis配置文件 /etc/redis/redis.conf
maxmemory 4gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 5. Docker配置

#### 构建SDC工具镜像
```bash
# 构建SDC工具Docker镜像
cd /opt/logiccore
docker build -f stuff/docker_sdc_generator_Dockerfile -t logiccore/sdc-generator:latest .

# 推送到ACR
docker tag logiccore/sdc-generator:latest your-acr-registry/logiccore/sdc-generator:latest
docker push your-acr-registry/logiccore/sdc-generator:latest
```

#### Docker安全配置
```bash
# 配置Docker daemon
cat > /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 64000,
      "Soft": 64000
    }
  }
}
EOF

systemctl restart docker
```

### 6. 系统服务配置

#### 后端服务 (systemd)
```bash
# /etc/systemd/system/logiccore-backend.service
[Unit]
Description=LogicCore Backend Service
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=logiccore
WorkingDirectory=/opt/logiccore/backend
Environment=NODE_ENV=production
EnvironmentFile=/opt/logiccore/backend/.env
ExecStart=/usr/bin/node dist/index.js
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### Worker服务 (systemd)
```bash
# /etc/systemd/system/logiccore-worker.service
[Unit]
Description=LogicCore Task Worker
After=network.target postgresql.service redis.service docker.service

[Service]
Type=simple
User=logiccore
WorkingDirectory=/opt/logiccore/backend
Environment=NODE_ENV=production
EnvironmentFile=/opt/logiccore/backend/.env
ExecStart=/usr/bin/python3 src/workers/toolWorker.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 7. 监控和日志

#### 日志配置
```bash
# 配置logrotate
cat > /etc/logrotate.d/logiccore << EOF
/opt/logiccore/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 logiccore logiccore
    postrotate
        systemctl reload logiccore-backend
        systemctl reload logiccore-worker
    endscript
}
EOF
```

#### 监控脚本
```bash
#!/bin/bash
# /opt/logiccore/scripts/health_check.sh

# 检查服务状态
systemctl is-active --quiet logiccore-backend || echo "Backend service down"
systemctl is-active --quiet logiccore-worker || echo "Worker service down"

# 检查磁盘空间
DISK_USAGE=$(df /data/logiccore_jobs | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage high: ${DISK_USAGE}%"
fi

# 检查Redis连接
redis-cli ping > /dev/null || echo "Redis connection failed"

# 检查数据库连接
psql $DATABASE_URL -c "SELECT 1;" > /dev/null || echo "Database connection failed"
```

### 8. 安全配置

#### 防火墙配置
```bash
# 只开放必要端口
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8080/tcp  # Backend API (内网)
ufw enable
```

#### SSL证书配置
```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
```

### 9. 部署步骤

#### 1. 准备环境
```bash
# 安装依赖
sudo apt update
sudo apt install -y nodejs npm python3 python3-pip postgresql redis-server docker.io nginx

# 安装应用依赖
cd /opt/logiccore/backend
npm install --production
pip3 install -r requirements.txt
```

#### 2. 配置应用
```bash
# 复制配置文件
cp .env.production .env

# 运行数据库迁移
npx prisma migrate deploy

# 构建前端
cd /opt/logiccore/frontend
npm run build
```

#### 3. 启动服务
```bash
# 启用并启动服务
systemctl enable logiccore-backend
systemctl enable logiccore-worker
systemctl start logiccore-backend
systemctl start logiccore-worker

# 检查服务状态
systemctl status logiccore-backend
systemctl status logiccore-worker
```

### 10. 验证部署

#### 运行系统测试
```bash
cd /opt/logiccore
node test_complete_sdc_system.js
```

#### 检查关键功能
- [ ] Template下载正常
- [ ] 任务提交成功
- [ ] OSS文件上传下载正常
- [ ] Docker容器执行正常
- [ ] 数据库记录正确
- [ ] 临时目录清理正常

## 🚨 故障排除

### 常见问题

1. **OSS权限错误**
   - 检查AccessKey权限
   - 验证Bucket策略配置

2. **Docker容器启动失败**
   - 检查镜像是否存在
   - 验证容器资源限制

3. **临时目录空间不足**
   - 清理旧的临时文件
   - 增加磁盘空间

4. **数据库连接超时**
   - 检查连接池配置
   - 验证网络连通性

### 日志查看
```bash
# 查看应用日志
journalctl -u logiccore-backend -f
journalctl -u logiccore-worker -f

# 查看Docker容器日志
docker logs <container_id>

# 查看系统资源
htop
df -h
```

## 📞 支持联系

如有部署问题，请联系技术支持团队。
