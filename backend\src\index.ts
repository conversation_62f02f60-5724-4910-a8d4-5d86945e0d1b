import 'express-async-errors'; // Must be imported first
// 环境变量加载 - 必须在所有其他模块之前引入
import './envLoader';
// 环境变量验证 - 必须在envLoader之后引入
import { env } from './config/env-validation';
import express from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import authRoutes from './routes/auth.routes';
import orderRoutes from './routes/order.routes';
import subscriptionRoutes from './routes/subscription.routes';
import userRoutes from './routes/user.routes';
import paymentRoutes from './routes/payment.routes';
import planRoutes from './routes/plan.routes';
import taskRoutes from './routes/task.routes';
import adminRoutes from './routes/admin.routes';
import templateRoutes from './routes/template.routes';
import { errorHandler } from './middleware/errorHandler';
import { initializeDb } from './config/database';
import logger, { requestLogger } from './config/logger';

const app = express();
const PORT = env.PORT;

async function startServer() {
  // --- Service Initialization ---
  
  /*
  try {
    await initializeWechatPayPlatformCert();
    console.log('✅ WeChat Pay SDK initialized successfully.');
  } catch(error) {
    console.error('❌ Failed to initialize WeChat Pay SDK:', error);
  }
  */

  // --- Essential Middleware ---
  
  // Security headers
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // Rate limiting
  const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
  });

  const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 auth requests per windowMs
    message: 'Too many authentication attempts, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
  });

  app.use(generalLimiter);

  // Request logging middleware
  app.use(requestLogger);

  app.use(cors({
    origin: env.FRONTEND_URL,
    credentials: true,
  }));
  app.use(express.json({ limit: '10mb' }));
  app.use(cookieParser());
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // --- API Routes ---
  const apiV1Router = express.Router();
  
  apiV1Router.use('/auth', authLimiter, authRoutes);
  apiV1Router.use('/users', userRoutes);
  apiV1Router.use('/plans', planRoutes);
  apiV1Router.use('/orders', orderRoutes);
  apiV1Router.use('/payment', paymentRoutes);
  apiV1Router.use('/subscriptions', subscriptionRoutes);
  apiV1Router.use('/tasks', taskRoutes);
  apiV1Router.use('/admin', adminRoutes);
  apiV1Router.use('/templates', templateRoutes);

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });

  app.get('/api', (req, res) => res.send('ChipCore API is running!'));
  app.use('/api/v1', apiV1Router);

  // Initialize database and other services
  initializeDb();

  // --- Global Error Handler ---
  // This must be the last middleware
  app.use(errorHandler);

  // --- Start Listening ---
  app.listen(PORT, () => {
    logger.info(`🚀 Backend server is running at http://localhost:${PORT}`);
  });
}

startServer().catch(error => {
  logger.error({ error }, '❌ Failed to start server');
  process.exit(1);
});