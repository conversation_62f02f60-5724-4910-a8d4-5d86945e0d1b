"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Check, X, Loader2 } from "lucide-react";
import { motion } from "framer-motion";
import axios from 'axios';
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth.context";

// This should ideally match the structure of the data coming from the backend
interface PlanFeature {
  text: string;
  included: boolean;
}

interface Plan {
  id: string;
  name: string;
  priceMonthly: number;
  priceAnnually: number;
  features: PlanFeature[];
  description: string;
  buttonText: string;
  buttonVariant: "default" | "outline";
  popular: boolean;
}

const planMetadata: { [key: string]: Omit<Partial<Plan>, 'id' | 'name' | 'priceMonthly' | 'priceAnnually'> } = {
  "Professional": {
    description: "适合专业团队和企业用户",
    buttonText: "立即升级",
    buttonVariant: "default",
    popular: true,
    features: [
      { text: "无限制工具使用", included: true },
      { text: "高级工具功能", included: true },
      { text: "优先技术支持", included: true },
      { text: "专业资讯和报告", included: true },
      { text: "API接口访问", included: true },
    ]
  },
  "Free": {
    description: "适合个人用户和小型项目",
    buttonText: "开始使用",
    buttonVariant: "outline",
    popular: false,
    features: [
        { text: "每月100次工具使用", included: true },
        { text: "基础技术支持", included: true },
        { text: "社区资讯访问", included: true },
        { text: "高级工具功能", included: false },
        { text: "优先技术支持", included: false },
    ]
  },
};

// --- Fallback Data ---
const defaultPlans: Plan[] = [
  {
    id: "cmcu3yg4m0001bcs1lpzzofy5",
    name: "Free",
    priceMonthly: 0,
    priceAnnually: 0,
    description: "适合个人用户和小型项目",
    buttonText: "开始使用",
    buttonVariant: "outline",
    popular: false,
    features: planMetadata["Free"].features || [],
  },
  {
    id: "cmcu3yg3w0000bcs1eqdlxngz",
    name: "Professional",
    priceMonthly: 0.01,
    priceAnnually: 0.01,
    description: "适合专业团队和企业用户",
    buttonText: "立即升级",
    buttonVariant: "default",
    popular: true,
    features: planMetadata["Professional"].features || [],
  },
];
// --- End Fallback Data ---

export default function MembershipPlans() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annually">("monthly");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/plans');
        
        if (response.data && response.data.length > 0) {
          const fetchedPlans = response.data
            .filter((plan: any) => plan.active)
            .map((plan: any): Plan => ({
              id: plan.id,
              name: plan.name,
              priceMonthly: plan.priceMonthly,
              priceAnnually: plan.priceAnnually,
              description: planMetadata[plan.name]?.description || '',
              buttonText: planMetadata[plan.name]?.buttonText || '选择方案',
              buttonVariant: planMetadata[plan.name]?.buttonVariant || 'default',
              popular: planMetadata[plan.name]?.popular || false,
              features: planMetadata[plan.name]?.features || [],
            }));
          setPlans(fetchedPlans);
        } else {
          // If API returns no plans, use fallback data
          setPlans(defaultPlans);
        }
        setError(null);
      } catch (err) {
        // If API fails, also use fallback data
        setPlans(defaultPlans);
        setError("无法连接到服务器，显示默认方案。");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  const handleCtaClick = (plan: Plan) => {
    // 检查用户是否已登录
    if (!isAuthenticated) {
      navigate('/auth/register');
      return;
    }

    if (plan.name === "Free") {
      // 已登录用户点击Free计划，跳转到工具集页面
      navigate('/tools');
      return;
    }

    // 已登录用户点击Professional计划，跳转到支付页面
    navigate(`/order/checkout?planId=${plan.id}&cycle=${billingCycle}&planName=${plan.name}`);
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
        </div>
      );
    }
  
    if (error && plans.length === 0) {
      return (
        <div className="text-center text-red-500 font-semibold">
          <p>{error}</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {plans.map((plan, index) => {
          const price = billingCycle === 'annually' ? plan.priceAnnually : plan.priceMonthly;
          const period = billingCycle === 'annually' ? '/年' : '/月';

          return (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className={`shadow-lg border-2 p-8 relative flex flex-col h-full ${
                plan.popular ? 'border-blue-500 shadow-xl' : 'border-gray-100'
              }`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="gradient-bg-blue text-white px-4 py-1">推荐</Badge>
                  </div>
                )}
                
                <CardContent className="p-0 flex flex-col flex-grow">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                    <div className={`text-4xl font-bold mb-2 ${plan.popular ? 'gradient-text-blue' : 'text-gray-900'}`}>
                      ¥{price}
                      <span className="text-lg font-normal text-gray-600">{period}</span>
                    </div>
                    <p className="text-gray-600">{plan.description}</p>
                  </div>
                  
                  <ul className="space-y-4 mb-8 flex-grow">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        {feature.included ? (
                          <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        ) : (
                          <X className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
                        )}
                        <span className={feature.included ? 'text-gray-700' : 'text-gray-400'}>
                          {feature.text}
                        </span>
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    onClick={() => handleCtaClick(plan)}
                    variant={plan.buttonVariant} 
                    className={`w-full py-3 ${
                      plan.popular 
                        ? 'gradient-bg-blue text-white hover:opacity-90' 
                        : 'border-2 border-gray-300 hover:border-blue-500 hover:text-blue-600'
                    }`}
                  >
                    {plan.buttonText}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>
    );
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">会员计划</h2>
          <p className="text-xl text-gray-600 mb-8">选择适合您的使用方案</p>
          <div className="flex items-center justify-center space-x-4">
            <Label htmlFor="billing-cycle" className={billingCycle === 'monthly' ? 'font-bold' : 'text-gray-500'}>
              月度
            </Label>
            <Switch
              id="billing-cycle"
              checked={billingCycle === "annually"}
              onCheckedChange={(checked) => setBillingCycle(checked ? "annually" : "monthly")}
              aria-label="切换计费周期"
            />
            <Label htmlFor="billing-cycle" className={billingCycle === 'annually' ? 'font-bold' : 'text-gray-500'}>
              年度 (享八折优惠)
            </Label>
          </div>
        </motion.div>
        
        {renderContent()}
      </div>
    </section>
  );
}
