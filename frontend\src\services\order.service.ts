import axios from 'axios';

interface CreateOrderPayload {
  planId: string;
  billingCycle: string;
  paymentMethod: 'ALIPAY' | 'WECHAT';
}

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080/api/v1',
  withCredentials: true, // 发送cookies用于认证
  headers: {
    'Content-Type': 'application/json',
  },
});

export const createOrder = async (payload: CreateOrderPayload) => {
  try {
    console.log('🔄 创建订单请求:', payload);

    const response = await api.post('/orders', {
      planId: payload.planId,
      billingCycle: payload.billingCycle.toUpperCase(),
      paymentMethod: payload.paymentMethod,
    });

    console.log('✅ 订单创建成功:', response.data);
    return response;
  } catch (error: any) {
    console.error('❌ 订单创建失败:', error.response?.data || error.message);
    throw error;
  }
};

// 查询订单状态
export const getOrderStatus = async (orderId: string) => {
  try {
    const response = await api.get(`/orders/${orderId}`);
    return response.data;
  } catch (error: any) {
    console.error('❌ 查询订单状态失败:', error.response?.data || error.message);
    throw error;
  }
};

export const getMyOrders = async () => {
  await sleep(1000);

  // Mock a list of orders
  const mockResponse = {
    data: [
      {
        orderNo: 'MOCK_1672531200000',
        planName: '专业版 - 年度',
        amount: 1299.00,
        status: 'PAID',
        createdAt: new Date('2023-01-01T12:00:00Z').toISOString(),
      },
      {
        orderNo: 'MOCK_1669852800000',
        planName: '专业版 - 月度',
        amount: 129.00,
        status: 'PAID',
        createdAt: new Date('2022-12-01T10:00:00Z').toISOString(),
      },
      {
        orderNo: 'MOCK_1667260800000',
        planName: '免费版',
        amount: 0.00,
        status: 'PAID',
        createdAt: new Date('2022-11-01T08:00:00Z').toISOString(),
      },
    ],
  };

  return mockResponse;
};

export const getOrderDetails = async (orderNo: string) => {
  await sleep(1000);

  // 获取订单详情

  // Mock a detailed order object. In a real app, this would come from the backend.
  // We can return a static object since we're just mocking.
  const mockOrder = {
    orderNo,
    status: 'PAID',
    createdAt: new Date('2023-01-01T12:00:00Z').toISOString(),
    updatedAt: new Date('2023-01-01T12:05:00Z').toISOString(),
    amount: 1299.00,
    plan: {
      name: '专业版 - 年度',
      description: '解锁所有高级功能，享受全年无限制访问。',
    },
    payment: {
      method: 'WECHAT_PAY',
      transactionId: 'MOCK_TRANSACTION_123456789',
      paidAt: new Date('2023-01-01T12:05:00Z').toISOString(),
    },
    user: {
      email: '<EMAIL>',
      name: '测试用户',
    },
    billingAddress: {
      name: '测试用户',
      address: '中国北京市海淀区中关村大街1号',
      phone: '13800138000',
    },
  };

  return { data: mockOrder };
}; 