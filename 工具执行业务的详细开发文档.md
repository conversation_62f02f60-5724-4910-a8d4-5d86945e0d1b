## 单台ECS实例芯片工具执行业务开发文档

本开发文档详细阐述利用单台资源丰富的ECS实例作为Docker宿主机，并采用Prisma ORM与PostgreSQL交互，实现芯片工具在线执行业务的各个开发步骤和细节。

### 1. 前端（React SPA）开发步骤

前端主要负责用户界面展示、参数输入、文件上传、任务状态实时更新以及结果下载。

#### 1.1 工具执行页面设计与开发
每个工具都有独立的页面布局，目前是需要开发三个工具页面，分别是SDC高效生成、clk电路自动生成和memory数据生成，

- **页面布局：**
    - **核心区域：** 工具参数输入表单、文件上传区、任务提交按钮、任务进度/状态显示区、结果下载区、日志显示区。
- **工具参数输入表单：**
    - 根据不同工具的参数需求，动态生成或使用预定义的表单组件。
    - 使用 **React Hook Form** 或 **Formik** 等库进行表单管理和验证，提供友好的用户输入校验提示。
    - 示例：文本输入框、下拉选择、数字输入、布尔开关等。
- **文件上传组件：**
    - 使用 `input type="file"` 元素，配合 `FormData` 对象进行文件封装。
    - 可以集成第三方库如 `react-dropzone` 提供拖拽上传功能，并显示上传进度。
    - **重要：** 前端上传的文件大小限制应与后端和OSS的限制匹配。
- **任务提交按钮：**
    - 点击后收集所有表单数据和文件。
    - 在提交过程中显示加载状态，防止重复提交。
- **任务进度与状态显示：**
    - 使用 UI 组件库（如 Ant Design 的 `Progress` 组件）展示任务进度条。
    - 显示任务当前状态文本（“排队中”、“运行中”、“完成”、“失败”）。
- **结果下载与日志显示：**
    - 任务完成后，根据后端提供的预签名URL生成下载链接。
    - 可以提供一个简单的文本区域或模态框，用于显示核心日志片段，或提供完整日志文件的下载链接。

#### 1.2 API 调用与数据交互
- **API设计**：
    - `POST /api/tasks`: 提交新任务。
        - 请求体：`{ toolId: string, parameters: object, files: [fileData] }`
        - 认证与授权中间件：验证用户token，检查会员权限。
        - 文件处理：使用 `multer` 等库处理文件上传，并将其上传到OSS。
        - 数据库操作：插入任务记录到PostgreSQL。
        - Redis操作：RPUSH任务ID到队列。
        - 响应：`{ taskId: string, message: "Task submitted successfully" }`
    - `GET /api/tasks/:taskId/status`: 查询任务状态。
        - 请求体：无
        - 认证与授权中间件：验证用户token，确保用户只能查询自己的任务。
        - 数据库操作：查询PostgreSQL。
        - 响应：`{ status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED", progress: number, message: string, resultUrl?: string, logUrl?: string }`
    - `GET /api/tasks/:taskId/download_url`: 获取结果或日志的预签名URL。
        - 请求体：`{ type: "result" | "log" }`
        - 认证与授权中间件：验证用户token，确保用户只能下载自己的任务结果。
        - OSS操作：使用阿里云SDK生成OSS文件的预签名URL，设置过期时间。
        - 响应：`{ url: string, expiresIn: number }`
- **API 客户端：** 使用 `axios` 或内置 `fetch` API 管理与后端API的通信。

- **提交任务：**
    - 将表单数据和文件打包成 `FormData` 对象。
    - 发送 `POST` 请求到 `/api/tasks`。
    - 请求头需包含用户认证 Token。
    - **示例代码片段 (React Component):**
    
    JavaScript
    
    ```
    import React, { useState } from 'react';
    import axios from 'axios';
    
    function ToolExecutionPage() {
        const [toolParams, setToolParams] = useState({});
        const [inputFile, setInputFile] = useState(null);
        const [taskId, setTaskId] = useState(null);
        const [taskStatus, setTaskStatus] = useState('PENDING');
        const [progress, setProgress] = useState(0);
        const [resultUrl, setResultUrl] = useState(null);
        const [logUrl, setLogUrl] = useState(null);
        const [errorMessage, setErrorMessage] = useState(null);
    
        const handleSubmit = async (e) => {
            e.preventDefault();
            setErrorMessage(null); // Clear previous errors
            setTaskStatus('SUBMITTING');
    
            const formData = new FormData();
            formData.append('toolId', 'your_tool_id'); // Replace with actual tool ID
            formData.append('parameters', JSON.stringify(toolParams));
            if (inputFile) {
                formData.append('inputFile', inputFile);
            }
    
            try {
                // Assuming user's auth token is stored and added to headers by an interceptor or manually
                const response = await axios.post('/api/tasks', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        // 'Authorization': `Bearer ${userAuthToken}` // Ensure token is sent
                    },
                });
                setTaskId(response.data.taskId);
                setTaskStatus('PENDING');
                // Start polling for status
                startPolling(response.data.taskId);
            } catch (error) {
                console.error('Task submission failed:', error);
                setErrorMessage(error.response?.data?.message || 'Failed to submit task.');
                setTaskStatus('FAILED');
            }
        };
    
        const startPolling = (currentTaskId) => {
            const interval = setInterval(async () => {
                try {
                    const response = await axios.get(`/api/tasks/${currentTaskId}/status`, {
                        // headers: { 'Authorization': `Bearer ${userAuthToken}` }
                    });
                    const { status, progress, resultUrl, logUrl, errorMessage } = response.data;
                    setTaskStatus(status);
                    setProgress(progress || 0); // Progress might not be available for PENDING/COMPLETED
                    setResultUrl(resultUrl);
                    setLogUrl(logUrl);
                    setErrorMessage(errorMessage);
    
                    if (status === 'COMPLETED' || status === 'FAILED') {
                        clearInterval(interval); // Stop polling
                        if (status === 'FAILED') {
                             setErrorMessage(errorMessage || 'Task failed without specific message.');
                        }
                    }
                } catch (error) {
                    console.error('Error polling task status:', error);
                    setErrorMessage(error.response?.data?.message || 'Failed to fetch task status.');
                    clearInterval(interval); // Stop polling on error
                    setTaskStatus('FAILED');
                }
            }, 3000); // Poll every 3 seconds
        };
    
        const handleDownload = async (type) => {
            if (!taskId) return;
            try {
                const response = await axios.get(`/api/tasks/<span class="math-inline">\{taskId\}/download\_url?type\=</span>{type}`, {
                    // headers: { 'Authorization': `Bearer ${userAuthToken}` }
                });
                window.open(response.data.url, '_blank'); // Open in new tab
            } catch (error) {
                console.error(`Failed to get download URL for ${type}:`, error);
                setErrorMessage(`Failed to get download URL for ${type}.`);
            }
        };
    
        // Render form, progress bar, download links based on state
        return (
            <div>
                <h2>芯片工具执行</h2>
                <form onSubmit={handleSubmit}>
                    {/* Tool specific parameters */}
                    <label>Tool Param 1:</label>
                    <input type="text" onChange={(e) => setToolParams({...toolParams, param1: e.target.value})} />
                    <br />
                    <label>Input File:</label>
                    <input type="file" onChange={(e) => setInputFile(e.target.files[0])} />
                    <br />
                    <button type="submit">提交任务</button>
                </form>
    
                {taskId && (
                    <div>
                        <h3>任务状态: {taskStatus} (ID: {taskId})</h3>
                        {taskStatus === 'RUNNING' && <progress value={progress} max="100"></progress>}
                        {errorMessage && <p style={{ color: 'red' }}>错误: {errorMessage}</p>}
                        {taskStatus === 'COMPLETED' && (
                            <div>
                                <button onClick={() => handleDownload('result')}>下载结果</button>
                                <button onClick={() => handleDownload('log')}>下载日志</button>
                            </div>
                        )}
                        {taskStatus === 'FAILED' && (
                            <div>
                                <p>任务执行失败。</p>
                                <button onClick={() => handleDownload('log')}>查看日志</button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        );
    }
    
    export default ToolExecutionPage;
    ```
    
- **实时状态更新：**
    
    <!-- - **短轮询：** MVP 阶段建议采用短轮询（如每 2-5 秒）`GET /api/tasks/:taskId/status` 接口来更新任务状态。 -->
    - 采用 WebSocket 进行更实时的推送。
- **结果下载：**
    
    - 当任务状态为 `COMPLETED` 时，从后端获取预签名URL，然后直接通过该URL从OSS下载文件。这减少了后端服务器的负载。

### 2. 后端（Node.js/Express.js）开发步骤

后端是整个业务逻辑的核心，处理API请求、与数据库交互、与OSS和Redis交互、并协调Python Worker。

#### 2.1 数据库设计与Prisma Schema

- **表设计**：
    - `users`：用户基本信息。
    - `subscriptions`：会员订阅信息。
    - `tools`：工具元数据（ID, 名称, Docker镜像路径, 默认参数等）。
    - `tasks`：**核心任务表**。
        - `id` (PK, UUID)
        - `user_id` (FK to users.id)
        - `tool_id` (FK to tools.id)
        - `status` (ENUM: 'PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED')
        - `created_at` (TIMESTAMP)
        - `started_at` (TIMESTAMP, 可空)
        - `finished_at` (TIMESTAMP, 可空)
        - `input_oss_path` (TEXT, 存储OSS完整路径或相对路径)
        - `output_oss_path` (TEXT, 可空)
        - `log_oss_path` (TEXT, 可空)
        - `parameters` (JSONB, 存储用户提交的工具参数)
        - `error_message` (TEXT, 可空，存储失败原因)
        - `worker_id` (TEXT, 可空，记录执行此任务的Worker ID，方便调试)
        - `ecs_instance_id` (TEXT, 可空，记录执行此任务的ECS实例ID)

使用 **Prisma ORM** 定义数据库模型，重点需要增加下面参考里的工具和任务相关的数据模型，保留之前已有的数据模型，并进行迁移。

- **`schema.prisma` 定义参考：**
    
    Code snippet
    
    ```
    // schema.prisma
    
    generator client {
      provider = "prisma-client-js"
    }
    
    datasource db {
      provider = "postgresql"
      url      = env("DATABASE_URL")
    }
    
    // 用户模型 (假设已存在或简化)
    model User {
      id           String    @id @default(uuid())
      email        String    @unique
      passwordHash String
      // ... 其他用户相关字段
      tasks        Task[]
      subscriptions Subscription[] // 关联订阅信息
    }
    
    // 会员订阅模型 (假设已存在或简化)
    model Subscription {
      id         String   @id @default(uuid())
      userId     String
      user       User     @relation(fields: [userId], references: [id])
      plan       String   // e.g., "FREE", "BASIC", "PREMIUM"
      expiresAt  DateTime
      isActive   Boolean
      // ... 其他订阅相关字段
    }
    
    // 工具元数据模型
    model Tool {
      id              String @id @default(uuid())
      name            String @unique
      dockerImage     String // ACR路径，例如: registry.cn-hangzhou.aliyuncs.com/your-namespace/tool-a:v1.0
      description     String?
      // ... 其他工具配置，例如默认参数结构
      tasks           Task[]
    }
    
    // 任务模型
    model Task {
      id             String    @id @default(uuid()) // 任务ID
      userId         String
      user           User      @relation(fields: [userId], references: [id])
      toolId         String
      tool           Tool      @relation(fields: [toolId], references: [id])
      status         TaskStatus @default(PENDING) // 任务状态
      createdAt      DateTime  @default(now())
      startedAt      DateTime?
      finishedAt     DateTime?
      inputOssPath   String    // OSS输入文件路径 (e.g., your-app-user-input/{userId}/{taskId}/)
      outputOssPath  String?   // OSS输出文件路径
      logOssPath     String?   // OSS日志文件路径
      parameters     Json      // 用户提交的工具参数 (JSONB)
      errorMessage   String?   // 任务失败时的错误信息
      workerId       String?   // 哪个Worker处理了此任务
      ecsInstanceId  String?   // 在哪个ECS实例上运行的
    }
    
    // 任务状态枚举
    enum TaskStatus {
      PENDING      // 等待调度
      RUNNING      // 正在运行
      COMPLETED    // 完成
      FAILED       // 失败
      CANCELLED    // 已取消
    }
    ```
    
- **运行 Prisma 命令：**
    
    - `npx prisma migrate dev --name init_tool_execution_schema`：创建数据库迁移文件并执行，同步数据库结构。
    - `npx prisma generate`：生成 Prisma Client，用于 Node.js 代码中与数据库交互。

#### 2.2 后端 API 路由和控制器

- **文件上传中间件：** 使用 `multer` 处理多部分表单数据（文件上传）。
    
- **路由定义：**
    
    JavaScript
    
    ```
    // src/routes/taskRoutes.js
    const express = require('express');
    const multer = require('multer');
    const { isAuthenticated } = require('../middlewares/auth'); // 你的认证中间件
    const { checkSubscription } = require('../middlewares/subscription'); // 你的订阅检查中间件
    const taskController = require('../controllers/taskController');
    
    const router = express.Router();
    const upload = multer(); // 不存储到磁盘，直接处理文件流
    
    // 提交新任务
    router.post('/tasks', isAuthenticated, checkSubscription, upload.single('inputFile'), taskController.submitTask);
    
    // 查询任务状态
    router.get('/tasks/:taskId/status', isAuthenticated, taskController.getTaskStatus);
    
    // 获取下载URL
    router.get('/tasks/:taskId/download_url', isAuthenticated, taskController.getDownloadUrl);
    
    module.exports = router;
    ```
    
- **控制器 (`taskController.js`) 核心逻辑：**
    
    JavaScript
    
    ```
    // src/controllers/taskController.js
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    const Redis = require('ioredis');
    const redis = new Redis(process.env.REDIS_URL); // Redis 连接
    const OSS = require('ali-oss'); // 阿里云 OSS SDK
    const { getAliyunSTSClient } = require('../utils/aliyunSTS'); // 用于获取STS凭证的工具函数
    
    // OSS 配置
    const ossConfig = {
        region: process.env.OSS_REGION,
        bucketUserInput: process.env.OSS_BUCKET_USER_INPUT,
        bucketJobResults: process.env.OSS_BUCKET_JOB_RESULTS,
        bucketJobLogs: process.env.OSS_BUCKET_JOB_LOGS,
        accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID, // 建议使用RAM角色
        accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET, // 建议使用RAM角色
    };
    
    // 提交任务
    exports.submitTask = async (req, res) => {
        const userId = req.user.id; // 从认证中间件获取的用户ID
        const { toolId, parameters } = req.body;
        const inputFile = req.file; // Multer 解析的文件
    
        try {
            // 1. 验证工具是否存在
            const tool = await prisma.tool.findUnique({ where: { id: toolId } });
            if (!tool) {
                return res.status(404).json({ message: 'Tool not found.' });
            }
    
            // 2. 将输入文件上传到OSS
            let inputOssPath = null;
            const taskId = crypto.randomUUID(); // 生成一个唯一的任务ID
    
            if (inputFile) {
                const client = new OSS({
                    region: ossConfig.region,
                    accessKeyId: ossConfig.accessKeyId,
                    accessKeySecret: ossConfig.accessKeySecret,
                    bucket: ossConfig.bucketUserInput,
                });
                const objectName = `<span class="math-inline">\{userId\}/</span>{taskId}/${inputFile.originalname}`;
                await client.put(objectName, inputFile.buffer);
                inputOssPath = objectName;
            }
    
            // 3. 在PostgreSQL中创建任务记录
            const task = await prisma.task.create({
                data: {
                    id: taskId,
                    userId: userId,
                    toolId: toolId,
                    status: 'PENDING',
                    inputOssPath: inputOssPath,
                    parameters: JSON.parse(parameters || '{}'), // 确保 parameters 是合法的 JSON
                },
            });
    
            // 4. 将任务ID推送到Redis任务队列
            await redis.rpush('task_queue', task.id);
    
            res.status(202).json({ message: 'Task submitted successfully.', taskId: task.id });
    
        } catch (error) {
            console.error('Error submitting task:', error);
            res.status(500).json({ message: 'Failed to submit task.', error: error.message });
        }
    };
    
    // 查询任务状态
    exports.getTaskStatus = async (req, res) => {
        const { taskId } = req.params;
        const userId = req.user.id;
    
        try {
            const task = await prisma.task.findUnique({
                where: { id: taskId },
                select: {
                    status: true,
                    outputOssPath: true,
                    logOssPath: true,
                    errorMessage: true,
                    // progress 可以由Worker更新到DB，或通过其他机制获取
                },
            });
    
            if (!task || task.userId !== userId) { // 确保用户只能查询自己的任务
                return res.status(404).json({ message: 'Task not found or unauthorized.' });
            }
    
            // 可以在此处添加逻辑来计算进度条百分比，例如根据日志文件大小、或Worker定期更新的进度字段
            let progress = 0;
            if (task.status === 'RUNNING') {
                // 示例：可以根据某些内部逻辑或 Worker 定期更新的 'progress' 字段来设置
                // For MVP, a simple hardcoded example or just rely on status change
                progress = Math.random() * 80 + 10; // Placeholder for RUNNING state
            } else if (task.status === 'COMPLETED') {
                progress = 100;
            } else if (task.status === 'FAILED') {
                progress = 100; // Even failed tasks are "done" for progress display
            }
    
    
            res.status(200).json({
                status: task.status,
                progress: progress,
                resultUrl: task.outputOssPath ? `/api/tasks/${taskId}/download_url?type=result` : null, // 提供下载接口而不是直接OSS路径
                logUrl: task.logOssPath ? `/api/tasks/${taskId}/download_url?type=log` : null,
                errorMessage: task.errorMessage,
            });
    
        } catch (error) {
            console.error('Error getting task status:', error);
            res.status(500).json({ message: 'Failed to get task status.', error: error.message });
        }
    };
    
    // 获取下载URL（预签名URL）
    exports.getDownloadUrl = async (req, res) => {
        const { taskId } = req.params;
        const { type } = req.query; // 'result' or 'log'
        const userId = req.user.id;
    
        try {
            const task = await prisma.task.findUnique({
                where: { id: taskId },
                select: { userId: true, outputOssPath: true, logOssPath: true, status: true },
            });
    
            if (!task || task.userId !== userId || task.status === 'PENDING' || task.status === 'RUNNING') {
                return res.status(404).json({ message: 'Task not found, unauthorized, or not yet completed.' });
            }
    
            let ossObjectName;
            let bucketName;
    
            if (type === 'result' && task.outputOssPath) {
                ossObjectName = task.outputOssPath;
                bucketName = ossConfig.bucketJobResults;
            } else if (type === 'log' && task.logOssPath) {
                ossObjectName = task.logOssPath;
                bucketName = ossConfig.bucketJobLogs;
            } else {
                return res.status(400).json({ message: 'Invalid download type or file not available.' });
            }
    
            // 获取OSS客户端，使用RAM角色凭证，或者您后端的AK/SK
            const client = new OSS({
                region: ossConfig.region,
                accessKeyId: ossConfig.accessKeyId,
                accessKeySecret: ossConfig.accessKeySecret,
                bucket: bucketName,
            });
    
            // 生成预签名URL，设置过期时间 (例如 5分钟)
            const signedUrl = client.signatureUrl(ossObjectName, { expires: 300 });
    
            res.status(200).json({ url: signedUrl, expiresIn: 300 });
    
        } catch (error) {
            console.error('Error generating download URL:', error);
            res.status(500).json({ message: 'Failed to generate download URL.', error: error.message });
        }
    };
    ```
    

#### 2.3 认证与权限控制（已存在但需强调）

- **认证中间件 (`isAuthenticated`)：** 确保用户已登录。
- **会员订阅中间件 (`checkSubscription`)：** 验证用户是否具有足够的会员等级来使用特定工具或提交任务。这可能涉及查询 `User` 和 `Subscription` 表。
- **数据隔离：** 在所有数据库查询和OSS操作中，务必使用 `userId` 过滤数据，确保用户只能访问自己的任务和文件。例如：`prisma.task.findUnique({ where: { id: taskId, userId: userId } })`。

### 3. Python Worker 开发步骤

Python Worker 是任务执行的协调者，持续从Redis队列拉取任务，并控制单台ECS实例上的Docker容器。

#### 3.1 Worker 核心逻辑

- **安装依赖：** `redis-py`, `docker`, `aliyun-python-sdk-core`, `aliyun-python-sdk-sts`, `oss2`。
    
- **主循环：**
    
    - 持续从 Redis 队列 `BLPOP`（阻塞式左弹出）任务 ID。
    - 获取任务 ID 后，从 PostgreSQL 查询任务详情。
    - **资源检查与调度：** 在单台ECS实例模式下，Worker需要维护一个当前实例的资源使用状态（例如，一个字典或对象 `{ 'cpu_used': 0, 'memory_used': 0 }`）。在启动新容器前，检查 `cpu_used + new_task_cpu_needed <= total_cpu` 和 `memory_used + new_task_mem_needed <= total_memory`。
        - 如果资源不足，将任务 `LPUSH` 回队列，然后短暂休眠后重试。
    - **获取STS临时凭证：**
        - 从阿里云 STS 服务获取 ACR 镜像拉取凭证。
        - 从阿里云 STS 服务获取 OSS 读写凭证（用户输入、输出、日志）。
    - **Docker 容器管理：**
        - 使用 `docker-py` 库。
        - `client.images.pull()`：拉取 ACR 镜像（带认证）。
        - `client.containers.run()`：启动容器，传入 OSS 凭证环境变量和资源限制。
            - **`--cpus=2`**: 限制容器使用的CPU核心数。
            - **`-m 16g`**: 限制容器使用的内存。
            - **`environment`**: 传递 OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET, OSS_SECURITY_TOKEN。
            - **`volumes`**: 挂载本地目录到容器内部，用于输入输出和日志。
    - **任务状态更新：** 及时更新 PostgreSQL 中的任务状态。
    - **容器清理：** 任务完成后，销毁 Docker 容器。
- **示例代码片段 (Python Worker):**
    
    Python
    
    ```
    # worker.py
    import os
    import json
    import time
    import redis
    import docker
    from aliyunsdkcore.client import AcsClient
    from aliyunsdksts.request.v20150401 import AssumeRoleRequest
    import oss2
    from sqlalchemy import create_engine, Column, String, Text, DateTime, Enum, JSON
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.ext.declarative import declarative_base
    from datetime import datetime
    import uuid
    
    # 配置
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://user:password@localhost:5432/mydb')
    TASK_QUEUE_NAME = 'task_queue'
    ECS_TOTAL_CPU = 8 # 假设单台ECS总CPU
    ECS_TOTAL_MEMORY_GB = 64 # 假设单台ECS总内存
    JOB_CPU_REQUEST = 2 # 每个Job分配的CPU
    JOB_MEMORY_REQUEST_GB = 16 # 每个Job分配的内存
    
    # 阿里云OSS配置
    OSS_REGION = os.getenv('OSS_REGION', 'cn-hangzhou')
    OSS_BUCKET_USER_INPUT = os.getenv('OSS_BUCKET_USER_INPUT', 'your-app-user-input')
    OSS_BUCKET_JOB_RESULTS = os.getenv('OSS_BUCKET_JOB_RESULTS', 'your-app-job-results')
    OSS_BUCKET_JOB_LOGS = os.getenv('OSS_BUCKET_JOB_LOGS', 'your-app-job-logs')
    
    # 阿里云RAM/STS 配置
    ALIYUN_RAM_ROLE_ARN = os.getenv('ALIYUN_RAM_ROLE_ARN', 'acs:ram::xxxxxxxxxxxx:role/YourWorkerRole') # Worker角色ARN
    ALIYUN_STS_REGION = os.getenv('ALIYUN_STS_REGION', 'cn-hangzhou')
    ALIYUN_ACCESS_KEY_ID = os.getenv('ALIYUN_ACCESS_KEY_ID') # 用于STS请求的AK
    ALIYUN_ACCESS_KEY_SECRET = os.getenv('ALIYUN_ACCESS_KEY_SECRET') # 用于STS请求的SK
    
    # 初始化 Redis 和 Docker 客户端
    redis_client = redis.from_url(REDIS_URL)
    docker_client = docker.from_env() # 连接到本地Docker Daemon
    
    # SQLAlchemy setup (Prisma 在 Python 中没有直接的 ORM 客户端，所以这里用 SQLAlchemy 模拟)
    # 在实际项目中，Python Worker 可能通过 HTTP API 调用 Node.js 后端来更新任务状态，
    # 或者直接使用 psycopg2 与 PostgreSQL 交互。这里为了简化直接使用 SQLAlchemy 模拟与 DB 交互。
    Base = declarative_base()
    engine = create_engine(DATABASE_URL)
    
    # 简化 Task 模型（与 Prisma Schema 保持一致）
    class Task(Base):
        __tablename__ = 'Task' # 注意与 Prisma 定义的表名一致
        id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
        userId = Column(String)
        toolId = Column(String)
        status = Column(Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='TaskStatus'))
        createdAt = Column(DateTime, default=datetime.now)
        startedAt = Column(DateTime)
        finishedAt = Column(DateTime)
        inputOssPath = Column(String)
        outputOssPath = Column(String)
        logOssPath = Column(String)
        parameters = Column(JSON)
        errorMessage = Column(Text)
        workerId = Column(String)
        ecsInstanceId = Column(String)
    
    class Tool(Base):
        __tablename__ = 'Tool'
        id = Column(String, primary_key=True)
        dockerImage = Column(String)
        # ... other tool fields
    
    Session = sessionmaker(bind=engine)
    
    # 单台ECS资源管理状态
    ecs_current_cpu_used = 0
    ecs_current_memory_used_gb = 0
    # 为简单起见，这里直接使用全局变量，实际生产中需要更复杂的线程安全或进程间通信机制
    # 或者将这些状态持久化到 Redis/DB，由 Worker 集群共同维护
    
    # 获取阿里云STS临时凭证
    def get_sts_token(role_arn, role_session_name):
        clt = AcsClient(ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET, ALIYUN_STS_REGION)
        request = AssumeRoleRequest.AssumeRoleRequest()
        request.set_RoleArn(role_arn)
        request.set_RoleSessionName(role_session_name)
        request.set_DurationSeconds(3600) # 凭证有效期，单位秒
    
        response = json.loads(clt.do_action_with_exception(request))
        credentials = response['Credentials']
        return credentials['AccessKeyId'], credentials['AccessKeySecret'], credentials['SecurityToken']
    
    def get_oss_client_with_sts(ak_id, ak_secret, sts_token, bucket_name):
        return oss2.Bucket(oss2.Auth(ak_id, ak_secret, sts_token), f'http://{OSS_REGION}.aliyuncs.com', bucket_name)
    
    def worker_loop():
        global ecs_current_cpu_used, ecs_current_memory_used_gb
        while True:
            # 1. 从Redis队列获取任务ID
            print("Waiting for task...")
            # BLPOP 阻塞式获取，0 表示无限等待
            _, task_id_bytes = redis_client.blpop(TASK_QUEUE_NAME, timeout=0)
            task_id = task_id_bytes.decode('utf-8')
            print(f"Picked up task: {task_id}")
    
            session = Session()
            try:
                # 2. 查询任务详情和工具信息
                task = session.query(Task).filter_by(id=task_id).first()
                if not task:
                    print(f"Task {task_id} not found in DB, skipping.")
                    continue
    
                tool = session.query(Tool).filter_by(id=task.toolId).first()
                if not tool:
                    print(f"Tool {task.toolId} for task {task_id} not found, skipping.")
                    task.status = 'FAILED'
                    task.errorMessage = 'Tool configuration not found.'
                    task.finishedAt = datetime.now()
                    session.commit()
                    continue
    
                # 3. 资源检查 (单台ECS模式)
                # 假设每个任务固定消耗 JOB_CPU_REQUEST 和 JOB_MEMORY_REQUEST_GB
                if ecs_current_cpu_used + JOB_CPU_REQUEST > ECS_TOTAL_CPU or \
                   ecs_current_memory_used_gb + JOB_MEMORY_REQUEST_GB > ECS_TOTAL_MEMORY_GB:
                    print(f"Not enough resources for task {task_id}. Current CPU: {ecs_current_cpu_used}/{ECS_TOTAL_CPU}, Mem: {ecs_current_memory_used_gb}/{ECS_TOTAL_MEMORY_GB}. Re-queueing.")
                    redis_client.lpush(TASK_QUEUE_NAME, task_id) # 放回队列头部
                    time.sleep(5) # 等待一段时间再重试
                    continue
    
                # 分配资源
                ecs_current_cpu_used += JOB_CPU_REQUEST
                ecs_current_memory_used_gb += JOB_MEMORY_REQUEST_GB
                print(f"Resources allocated for task {task_id}. Current CPU used: {ecs_current_cpu_used}, Mem used: {ecs_current_memory_used_gb}GB.")
    
                # 4. 更新任务状态为 RUNNING
                task.status = 'RUNNING'
                task.startedAt = datetime.now()
                task.workerId = os.getenv('WORKER_ID', 'worker-01') # 可设置Worker ID
                task.ecsInstanceId = os.getenv('ECS_INSTANCE_ID', 'ecs-single-instance') # 可设置ECS ID
                session.commit()
    
                # 5. 获取OSS和ACR的临时凭证
                # 注意：这里简化为一次性获取Worker角色的凭证，生产中更安全的是为每个容器单独生成受限凭证
                # 或者由Worker为容器生成一个临时的OSS STS凭证并传入环境变量
                # 示例为OSS访问生成临时凭证（用于工具容器）
                sts_ak, sts_sk, sts_token = get_sts_token(ALIYUN_RAM_ROLE_ARN, f"job_session_{task.id}")
    
                # 6. 设置Docker容器环境变量和卷
                container_name = f"tool-job-{task_id}"
                # 假设工具需要输入/输出/日志目录
                local_input_dir = f"/tmp/tool_inputs/{task_id}"
                local_output_dir = f"/tmp/tool_outputs/{task_id}"
                local_log_dir = f"/tmp/tool_logs/{task_id}"
                os.makedirs(local_input_dir, exist_ok=True)
                os.makedirs(local_output_dir, exist_ok=True)
                os.makedirs(local_log_dir, exist_ok=True)
    
                # 将OSS输入文件下载到本地
                if task.inputOssPath:
                    input_oss_client = get_oss_client_with_sts(sts_ak, sts_sk, sts_token, OSS_BUCKET_USER_INPUT)
                    local_input_filepath = os.path.join(local_input_dir, os.path.basename(task.inputOssPath))
                    input_oss_client.get_object_to_file(task.inputOssPath, local_input_filepath)
                    print(f"Input file downloaded to {local_input_filepath}")
    
    
                container_env = {
                    'OSS_ACCESS_KEY_ID': sts_ak,
                    'OSS_ACCESS_KEY_SECRET': sts_sk,
                    'OSS_SECURITY_TOKEN': sts_token,
                    'OSS_REGION': OSS_REGION,
                    'OSS_BUCKET_INPUT': OSS_BUCKET_USER_INPUT, # 容器内部可能需要知道的桶名
                    'OSS_BUCKET_OUTPUT': OSS_BUCKET_JOB_RESULTS,
                    'OSS_BUCKET_LOGS': OSS_BUCKET_JOB_LOGS,
                    'JOB_INPUT_PATH': f'/data/input/{os.path.basename(task.inputOssPath)}' if task.inputOssPath else '', # 容器内输入文件路径
                    'JOB_OUTPUT_DIR': '/data/output', # 容器内输出目录
                    'JOB_LOG_DIR': '/data/logs',     # 容器内日志目录
                    'JOB_PARAMETERS': json.dumps(task.parameters), # 将参数传递给容器
                    'TASK_ID': task_id,
                    'USER_ID': task.userId,
                }
                # 容器挂载卷
                volumes = {
                    local_input_dir: {'bind': '/data/input', 'mode': 'ro'},
                    local_output_dir: {'bind': '/data/output', 'mode': 'rw'},
                    local_log_dir: {'bind': '/data/logs', 'mode': 'rw'},
                }
    
                # 7. 启动Docker容器并等待其完成
                print(f"Starting container {container_name} with image {tool.dockerImage}...")
                container = docker_client.containers.run(
                    tool.dockerImage,
                    detach=True,
                    name=container_name,
                    environment=container_env,
                    volumes=volumes,
                    remove=True, # 容器停止后自动删除
                    cpus=JOB_CPU_REQUEST,
                    mem_limit=f"{JOB_MEMORY_REQUEST_GB}g",
                    # 可以在这里传递命令参数给工具，如果工具需要
                    # command=[tool.entrypoint_script, '--param', 'value']
                )
    
                # 阻塞直到容器停止
                result = container.wait()
                exit_code = result['StatusCode']
                print(f"Container {container_name} finished with exit code {exit_code}")
    
                # 8. 处理容器输出和日志，上传到OSS
                task_succeeded = exit_code == 0
                if task_succeeded:
                    # 遍历本地输出目录并上传到OSS
                    output_oss_client = get_oss_client_with_sts(sts_ak, sts_sk, sts_token, OSS_BUCKET_JOB_RESULTS)
                    uploaded_output_paths = []
                    for root, _, files in os.walk(local_output_dir):
                        for file_name in files:
                            local_file_path = os.path.join(root, file_name)
                            relative_path = os.path.relpath(local_file_path, local_output_dir)
                            oss_object_name = f"{task.userId}/{task_id}/output/{relative_path}"
                            output_oss_client.put_object_from_file(oss_object_name, local_file_path)
                            uploaded_output_paths.append(oss_object_name)
                    task.outputOssPath = uploaded_output_paths[0] if uploaded_output_paths else None # 记录主输出文件路径
    
                    # 上传日志
                    log_oss_client = get_oss_client_with_sts(sts_ak, sts_sk, sts_token, OSS_BUCKET_JOB_LOGS)
                    log_file_name = f"{task_id}.log"
                    local_log_filepath = os.path.join(local_log_dir, log_file_name)
                    # 假设工具将日志输出到 /data/logs/tool.log 或 stdout/stderr
                    # 如果工具输出到stdout/stderr，可以通过 container.logs() 获取
                    container_logs = container.logs().decode('utf-8')
                    with open(local_log_filepath, 'w') as f:
                        f.write(container_logs)
                    oss_log_object_name = f"{task.userId}/{task_id}/{log_file_name}"
                    log_oss_client.put_object_from_file(oss_log_object_name, local_log_filepath)
                    task.logOssPath = oss_log_object_name
    
                    task.status = 'COMPLETED'
                else:
                    task.status = 'FAILED'
                    # 获取容器日志作为错误信息
                    error_logs = container.logs().decode('utf-8')
                    task.errorMessage = error_logs[-1000:] # 截取最后1000字符作为错误信息
    
                    # 即使失败也尝试上传日志
                    log_oss_client = get_oss_client_with_sts(sts_ak, sts_sk, sts_token, OSS_BUCKET_JOB_LOGS)
                    log_file_name = f"{task_id}.log"
                    local_log_filepath = os.path.join(local_log_dir, log_file_name)
                    container_logs = container.logs().decode('utf-8')
                    with open(local_log_filepath, 'w') as f:
                        f.write(container_logs)
                    oss_log_object_name = f"{task.userId}/{task_id}/{log_file_name}"
                    log_oss_client.put_object_from_file(oss_log_object_name, local_log_filepath)
                    task.logOssPath = oss_log_object_name
    
                task.finishedAt = datetime.now()
                session.commit()
                print(f"Task {task_id} completed with status: {task.status}")
    
            except docker.errors.ContainerError as e:
                print(f"Container error for task {task_id}: {e}")
                if task:
                    task.status = 'FAILED'
                    task.errorMessage = f"Container execution error: {e.stderr.decode('utf-8') if e.stderr else str(e)}"
                    task.finishedAt = datetime.now()
                    session.commit()
            except Exception as e:
                print(f"An unexpected error occurred for task {task_id}: {e}")
                if task:
                    task.status = 'FAILED'
                    task.errorMessage = f"Worker internal error: {str(e)}"
                    task.finishedAt = datetime.now()
                    session.commit()
            finally:
                session.close()
                # 释放资源
                ecs_current_cpu_used -= JOB_CPU_REQUEST
                ecs_current_memory_used_gb -= JOB_MEMORY_REQUEST_GB
                print(f"Resources released for task {task_id}. Current CPU used: {ecs_current_cpu_used}, Mem used: {ecs_current_memory_used_gb}GB.")
                # 清理本地临时文件
                import shutil
                if os.path.exists(local_input_dir): shutil.rmtree(local_input_dir)
                if os.path.exists(local_output_dir): shutil.rmtree(local_output_dir)
                if os.path.exists(local_log_dir): shutil.rmtree(local_log_dir)
    
    if __name__ == '__main__':
        # Base.metadata.create_all(engine) # 如果是首次运行，可以创建表
        worker_loop()
    ```
    

#### 3.2 阿里云资源与安全配置（Worker相关）

- **RAM 角色与策略：** 为 Python Worker 所在 ECS 实例（或直接为Worker程序创建RAM AccessKey）配置一个 RAM 角色。
    - **策略示例 (JSON)：**
        
        JSON
        
        ```
        {
            "Version": "1",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Action": [
                        "sts:AssumeRole" // 允许请求STS临时凭证
                    ],
                    "Resource": [
                        "acs:ram::*:role/YourToolExecutionRoleForContainer" // 允许AssumeToolExecutionRole，这个角色将被容器使用
                    ]
                },
                {
                    "Effect": "Allow",
                    "Action": [
                        "cr:PullRepository" // 允许从ACR拉取镜像
                    ],
                    "Resource": [
                        "acs:cr:*:*:repository/*" // 限制到您的ACR仓库
                    ]
                },
                {
                    "Effect": "Allow",
                    "Action": [
                        "oss:GetObject",
                        "oss:PutObject",
                        "oss:ListObjects"
                    ],
                    "Resource": [
                        "acs:oss:*:*:your-app-user-input/*",
                        "acs:oss:*:*:your-app-job-results/*",
                        "acs:oss:*:*:your-app-job-logs/*"
                    ]
                }
            ]
        }
        ```
        
    - **注意：** 生产中，给 Worker 的 STS `AssumeRole` 权限中的 `Resource` 应精确到要 Assume 的容器使用的 RAM 角色。
    - **`YourToolExecutionRoleForContainer` RAM 角色：** 为工具容器单独定义一个 RAM 角色，这个角色只拥有对 `your-app-user-input/{userId}/{taskId}/*` 的读权限，以及对 `your-app-job-results/{userId}/{taskId}/*` 和 `your-app-job-logs/{userId}/{taskId}/*` 的写权限。Worker 通过 STS 将此角色的临时凭证传递给容器。
- **Docker Daemon 远程访问（如果 Worker 和 Docker Daemon 不在同一进程）：**
    - 确保 Docker Daemon 开启了远程 API 访问（通常通过 TCP 端口，如 2375 或 2376）。
    - **安全提示：** 必须配置 TLS 证书进行加密和认证，并严格限制安全组访问来源 IP。MVP 阶段如果 Worker 和 Docker Daemon 在同一台 ECS 上，可以直接使用 Unix Socket，安全性更高。

### 4. 工具 Docker 镜像构建（ACR）

每个芯片工具都应该封装成一个独立的 Docker 镜像。

- **Dockerfile 示例：**
    
    Dockerfile
    
    ```
    # Dockerfile for a sample chip tool
    # 基础镜像，根据工具依赖选择，例如 Ubuntu, CentOS, Alpine 或特定Python/Java运行时
    FROM ubuntu:20.04
    
    # 安装工具依赖
    RUN apt-get update && apt-get install -y \
        build-essential \
        python3 \
        python3-pip \
        # ... 其他工具需要的库和依赖
        && rm -rf /var/lib/apt/lists/*
    
    # 安装 Python OSS SDK
    RUN pip3 install oss2
    
    # 设置工作目录
    WORKDIR /app
    
    # 复制工具脚本和执行文件
    COPY tool_script.py /app/tool_script.py
    COPY entrypoint.sh /app/entrypoint.sh
    
    # 赋予执行权限
    RUN chmod +x /app/entrypoint.sh
    
    # 定义容器启动时的入口点
    ENTRYPOINT ["/app/entrypoint.sh"]
    
    # 暴露端口 (如果工具是服务型，通常离线工具不需要)
    # EXPOSE 8080
    
    # 默认命令 (工具运行时执行的逻辑)
    # CMD ["--help"] # 例如，显示帮助信息
    
    # entrypoint.sh 示例
    # #!/bin/bash
    #
    # # 接收环境变量中的 OSS 凭证
    # export ALIBABA_CLOUD_ACCESS_KEY_ID=$OSS_ACCESS_KEY_ID
    # export ALIBABA_CLOUD_ACCESS_KEY_SECRET=$OSS_ACCESS_KEY_SECRET
    # export ALIBABA_CLOUD_SECURITY_TOKEN=$OSS_SECURITY_TOKEN
    # export ALIBABA_CLOUD_OSS_ENDPOINT="http://${OSS_REGION}.aliyuncs.com" # OSS 内网Endpoint
    #
    # # 解析从环境变量传入的参数
    # PARAMS=$(echo $JOB_PARAMETERS | jq -r '.') # 如果参数是JSON字符串，可能需要jq
    # INPUT_FILE=$JOB_INPUT_PATH
    # OUTPUT_DIR=$JOB_OUTPUT_DIR
    # LOG_DIR=$JOB_LOG_DIR
    # TASK_ID=$TASK_ID
    # USER_ID=$USER_ID
    #
    # # 下载输入文件 (如果工具脚本不自行处理)
    # if [ -n "$INPUT_FILE" ]; then
    #   python3 -c "import oss2; import os; \
    #     bucket = oss2.Bucket(oss2.Auth(os.environ['OSS_ACCESS_KEY_ID'], os.environ['OSS_ACCESS_KEY_SECRET'], os.environ['OSS_SECURITY_TOKEN']), \
    #     os.environ['ALIBABA_CLOUD_OSS_ENDPOINT'], os.environ['OSS_BUCKET_INPUT']); \
    #     bucket.get_object_to_file('${USER_ID}/${TASK_ID}/' + os.path.basename('${INPUT_FILE}'), '${INPUT_FILE}')"
    # fi
    #
    # # 执行工具主逻辑
    # # 假设 tool_script.py 接收输入文件路径、输出目录和参数
    # python3 /app/tool_script.py --input $INPUT_FILE --output $OUTPUT_DIR --log $LOG_DIR --params "$PARAMS"
    #
    # # 检查工具执行状态并上传日志/结果
    # TOOL_EXIT_CODE=$?
    #
    # # 上传日志 (例如，工具将所有日志输出到 /data/logs/tool.log)
    # python3 -c "import oss2; import os; \
    #   bucket = oss2.Bucket(oss2.Auth(os.environ['OSS_ACCESS_KEY_ID'], os.environ['OSS_ACCESS_KEY_SECRET'], os.environ['OSS_SECURITY_TOKEN']), \
    #   os.environ['ALIBABA_CLOUD_OSS_ENDPOINT'], os.environ['OSS_BUCKET_LOGS']); \
    #   bucket.put_object_from_file('${USER_ID}/${TASK_ID}/${TASK_ID}.log', '${LOG_DIR}/tool.log')"
    #
    # # 上传结果
    # if [ $TOOL_EXIT_CODE -eq 0 ]; then
    #   # 遍历输出目录并上传所有结果文件
    #   find "$OUTPUT_DIR" -type f -print0 | while IFS= read -r -d $'\0' file; do
    #     RELATIVE_PATH=$(echo "$file" | sed "s|^${OUTPUT_DIR}/||")
    #     python3 -c "import oss2; import os; \
    #       bucket = oss2.Bucket(oss2.Auth(os.environ['OSS_ACCESS_KEY_ID'], os.environ['OSS_ACCESS_KEY_SECRET'], os.environ['OSS_SECURITY_TOKEN']), \
    #       os.environ['ALIBABA_CLOUD_OSS_ENDPOINT'], os.environ['OSS_BUCKET_OUTPUT']); \
    #       bucket.put_object_from_file('${USER_ID}/${TASK_ID}/output/' + '$RELATIVE_PATH', '$file')"
    #   done
    # fi
    #
    # exit $TOOL_EXIT_CODE
    ```
    
- **推送到 ACR：** 使用 `docker build` 和 `docker push` 将镜像推送到您的阿里云 ACR 仓库。
    

### 5. 阿里云资源配置（MVP 阶段）

- **ECS 实例：**
    - 购买一台高性能 ECS 实例（如 8vCPU/64GB）。
    - 选择 Ubuntu/CentOS 等操作系统，安装 Docker Daemon。
    - 配置 ECS 实例的安全组，允许：
        - 您的开发机器 SSH 访问（端口 22）。
        - 后端 API 服务到 PostgreSQL (5432) 和 Redis (6379) 的访问。
        - Python Worker 到 Docker Daemon API 的访问（如果通过 TCP，需开放端口并限制来源）。
        - **注意：** 如果 Docker Daemon 仅监听 Unix Socket 且 Python Worker 在同一台 ECS 上，则无需开放 Docker Daemon 端口。
- **PostgreSQL：**
    - 前期部署到ECS上，后期再考虑购买阿里云 RDS PostgreSQL 实例。
    - 配置白名单，只允许您的后端 ECS 实例和 Python Worker ECS 实例访问。
- **Redis：**
    - 前期部署到ECS上，后期再考虑购买阿里云 Redis 实例。
    - 配置白名单，只允许您的后端 ECS 实例和 Python Worker ECS 实例访问。
- **OSS：**
    - 创建 4 个 OSS Bucket：`your-app-user-input`, `your-app-tool-scripts`, `your-app-job-results`, `your-app-job-logs`。
    - Bucket 权限设置为私有，所有访问通过 RAM 角色和 STS 临时凭证进行。
- **ACR：**
    - 创建您的私有 ACR 仓库，用于存储工具 Docker 镜像。
- **RAM/STS：**
    - 如前所述，为 Python Worker 及其Assume Role的容器分别创建 RAM 角色和策略。

### 6. 生产部署与测试（MVP 阶段）

尽管是 MVP 阶段，也应进行基本的部署和测试。

#### 6.1 部署

1. **代码部署：**
    - 将前端代码部署到 Nginx/CDN 或阿里云 OSS 静态网站托管。
    - 将后端 Node.js 应用部署到 ECS 实例（可以和 Python Worker 在同一台，也可以单独一台小ECS），通过 PM2 或 Systemd 管理进程。
    - 将 Python Worker 部署到您的主 ECS 实例上，通过 Systemd 管理进程，确保其开机自启动和持续运行。
2. **环境变量配置：** 确保所有服务（前端、后端、Worker）都正确配置了数据库连接字符串、Redis URL、OSS/RAM凭证等环境变量。
3. **域名与 HTTPS：** 配置域名并启用 HTTPS，确保通信安全。

#### 6.2 测试

- **功能测试：**
    - **端到端流程：** 从用户注册、登录、提交任务、查看进度、到最终下载结果和日志，完整走一遍流程。
    - **不同工具测试：** 如果有多个工具，分别测试其参数输入、执行和结果是否符合预期。
    - **错误场景：**
        - 上传无效文件、参数格式错误。
        - 工具执行失败（模拟工具返回非零退出码）。
        - 网络中断、ECS 实例负载过高导致任务排队或失败。
- **性能测试（基本）：**
    - **并发任务：** 逐步增加并发任务提交量（例如，同时提交 1、2、3、4 个任务），观察单台 ECS 实例的 CPU、内存使用率。
    - **队列行为：** 观察 Redis 任务队列长度变化，确认任务是否能被 Worker 及时消费。
    - **长任务执行：** 测试长时间运行的工具，观察其稳定性。
- **安全测试（基础）：**
    - **权限验证：** 验证非登录用户、未订阅用户是否能提交任务。
    - **数据隔离：** 确保用户 A 无法下载用户 B 的任务结果。
    - **STS 凭证时效性：** 验证过期凭证是否无法访问 OSS。
- **稳定性测试：**
    - 让系统运行一段时间，观察是否有内存泄漏、进程崩溃等问题。
    - 检查日志系统是否正常记录。
- **故障模拟：**
    - 停止 Python Worker 进程，看任务是否堆积。
    - 重启 ECS 实例，看服务是否能自动恢复。

---

### 7. MVP阶段总结与后续展望

在MVP阶段，采用单台ECS实例的优势在于**快速启动和降低初期复杂性**。您可以将精力集中在核心业务逻辑的开发上，并快速验证市场需求。

然而，这种模式的局限性在于：

- **单点故障：** ECS 实例或其上任何关键服务（Docker Daemon, Worker）宕机，整个工具执行服务将中断。
- **扩展性限制：** 最大并发任务数受限于单台 ECS 实例的物理资源上限。
- **资源利用率波动：** 在低负载时，大量资源空闲；在高负载时，可能出现排队严重或资源争抢。

**后续优化方向：**

- **高可用性：** 引入多台 ECS 实例，并使用阿里云弹性伸缩 (ASG) 和负载均衡 (SLB) 自动管理 Worker 和 Docker Host 集群。
- **更强壮的调度：** Python Worker 集群化，并实现更智能的调度策略（如 Bin-Packing）。
- **监控与告警：** 完善 CloudMonitor 和 SLS 告警，实时掌握系统健康状况。
- **容器编排：** 考虑未来迁移到 Kubernetes (ACK)，获得更强大的容器管理和自动化运维能力。

通过上述详细步骤，您应该能够顺利地在MVP阶段实现您的芯片工具在线执行业务。