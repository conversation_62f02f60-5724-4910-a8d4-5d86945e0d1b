# 技术架构规则

## 前端架构规则

1. **组件设计**：
   - 采用React函数式组件和Hooks
   - 组件应遵循单一职责原则
   - 使用TypeScript进行类型定义和静态类型检查
   - 将大型组件拆分为可复用的小组件
   - 使用Tailwind CSS进行样式管理，遵循设计系统

2. **状态管理**：
   - 对于简单组件，使用React Hooks (useState, useReducer)
   - 使用Context API实现跨组件状态共享
   - 遵循单向数据流原则
   - 使用不可变数据操作模式

3. **路由**：
   - 使用React Router管理应用路由
   - 实现路由级别的代码分割
   - 保护需要认证的路由
   - 维护清晰的路由架构和命名

4. **API通信**：
   - 使用Axios进行HTTP请求
   - 集中管理API请求配置
   - 实现请求拦截器处理认证和错误
   - 使用自定义hooks封装数据获取逻辑

5. **实时通信**：
   - 使用Socket.IO客户端实现实时通信
   - 妥善管理Socket连接的生命周期
   - 实现重连机制和错误处理

6. **表单处理**：
   - 使用Formik和Yup进行表单管理和验证
   - 创建可复用的表单组件
   - 实现一致的错误展示和处理

7. **性能优化**：
   - 使用React.memo避免不必要的重渲染
   - 实现虚拟滚动处理长列表
   - 使用useMemo和useCallback优化性能
   - 实现合理的组件分割和懒加载策略

## 后端架构规则

1. **API设计**：
   - 遵循RESTful API设计原则
   - 使用一致的URL结构和命名
   - 实现API版本控制
   - 提供适当的错误响应和状态码

2. **服务层设计**：
   - 在控制器和数据访问层之间使用服务层
   - 实现业务逻辑在服务层
   - 保持服务之间的低耦合
   - 使用依赖注入模式

3. **数据库访问**：
   - 使用Prisma ORM进行数据库操作
   - 创建和维护清晰的数据库模式
   - 实现数据库迁移和版本控制
   - 优化查询性能

4. **认证与授权**：
   - 使用JWT进行无状态认证
   - 实现基于角色的访问控制
   - 正确处理令牌过期和刷新
   - 保护敏感API端点

5. **错误处理**：
   - 使用全局错误处理中间件
   - 实现结构化的错误响应
   - 记录错误并监控异常情况
   - 优雅处理各种错误场景

6. **缓存策略**：
   - 适当使用数据缓存提高性能
   - 实现缓存失效策略
   - 针对高频读取操作优化缓存
   - 使用分布式缓存处理多实例部署

7. **搜索功能**：
   - 使用Elasticsearch实现高级搜索
   - 优化搜索索引和查询
   - 实现搜索结果的权限过滤
   - 提供相关性排序和搜索建议

## 数据库设计规则

1. **模式设计**：
   - 使用规范化设计减少数据冗余
   - 使用合适的数据类型和约束
   - 实现合理的索引策略
   - 使用外键维护数据完整性

2. **命名约定**：
   - 表名使用复数形式，采用蛇形命名法
   - 字段名采用蛇形命名法
   - 主键统一命名为id
   - 外键命名为entity_id格式

3. **查询优化**：
   - 避免N+1查询问题
   - 使用适当的索引提高查询性能
   - 优化连接查询
   - 使用分页减少数据加载量

4. **数据迁移**：
   - 所有模式变更通过迁移脚本实现
   - 迁移脚本应是可回滚的
   - 维护数据迁移的版本历史
   - 在应用部署过程中自动执行迁移

## DevOps与部署规则

1. **容器化**：
   - 使用Docker容器化应用服务
   - 维护最小和安全的基础镜像
   - 使用多阶段构建优化镜像大小
   - 避免在容器中存储持久数据

2. **环境配置**：
   - 使用环境变量进行配置管理
   - 区分开发、测试和生产环境配置
   - 敏感信息不应硬编码在代码中
   - 使用.env文件进行本地开发配置

3. **监控与日志**：
   - 实现结构化日志记录
   - 集中收集和分析日志
   - 设置关键指标的监控
   - 配置合适的告警机制

4. **备份策略**：
   - 定期备份数据库
   - 实现自动备份机制
   - 测试备份恢复流程
   - 存储备份在安全的异地位置

5. **持续集成与部署**：
   - 实现自动化测试流程
   - 配置CI/CD管道
   - 实施蓝绿部署或金丝雀发布
   - 自动化部署和回滚机制