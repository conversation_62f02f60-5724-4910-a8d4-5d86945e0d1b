{"name": "logiccore-backend", "version": "1.0.0", "description": "Backend service for LogicCore", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch ./src/index.ts", "dev:worker": "tsx src/workers/toolWorker.ts", "build": "tsc", "start": "node --require ./dist/envLoader.js ./dist/index.js", "check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/utils/seedData.ts", "db:seed:admin": "tsx src/utils/seedAdmin.ts", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.18.0", "@wecom/crypto": "^1.0.1", "ali-oss": "^6.23.0", "alipay-sdk": "latest", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "decimal.js": "^10.4.3", "dotenv": "^16.4.5", "express": "^4.19.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.4.1", "express-session": "^1.18.1", "helmet": "^8.0.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "memorystore": "^1.6.7", "multer": "^2.0.1", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-local": "^1.0.0", "pino": "^9.5.0", "pino-pretty": "^11.3.0", "redis": "^4.7.1", "uuid": "^11.1.0", "wechatpay-node-v3": "^2.2.1", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/connect-pg-simple": "^7.0.3", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.12", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "esbuild": "^0.25.0", "prisma": "^5.18.0", "tsx": "^4.16.2", "typescript": "^5.5.4"}}