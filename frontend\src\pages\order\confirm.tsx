import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Check, ShoppingCart } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

const OrderConfirmPage: React.FC = () => {
  const navigate = useNavigate();

  // Mock data - in a real app, this would come from state or an API call
  const orderDetails = {
    planName: '专业版 (Pro)',
    billingCycle: '年度订阅',
    price: '¥1,299.00',
    features: [
      '无限制工具使用次数',
      '访问所有高级功能',
      '并行任务处理',
      '优先技术支持',
    ],
  };

  const handleConfirm = () => {
    // In a real app, this would likely navigate to the checkout page with an order ID
    // For now, it will navigate to the generic checkout page.
    navigate('/checkout');
  };

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-12 flex items-center justify-center">
      <motion.div
        className="w-full max-w-lg"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-3">
              <ShoppingCart className="h-6 w-6 gradient-text-orange" />
              <CardTitle className="text-2xl">确认您的订单</CardTitle>
            </div>
            <CardDescription>请在支付前核对您的订阅计划详情。</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 border rounded-lg">
              <h3 className="text-lg font-semibold">{orderDetails.planName}</h3>
              <p className="text-sm text-gray-500">{orderDetails.billingCycle}</p>
              <Separator className="my-4" />
              <ul className="space-y-2 text-sm">
                {orderDetails.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <Check className="h-4 w-4 mr-2 text-green-500" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              <Separator className="my-4" />
              <div className="text-right">
                <p className="text-sm text-gray-600">总计</p>
                <p className="text-2xl font-bold">{orderDetails.price}</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" asChild>
              <Link to="/membership">
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回
              </Link>
            </Button>
            <Button className="gradient-bg-orange" onClick={handleConfirm}>
              确认并支付
            </Button>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default OrderConfirmPage; 