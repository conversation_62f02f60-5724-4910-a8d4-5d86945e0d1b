# 后续开发、集成与测试任务计划 (post_task.md)

## 1. 概述

本文档基于《系统改进与优化计划 (improve.md)》的发现，并结合初始的《需求分析与设计 (online_req.md)》和《开发指南 (online_dev.md)》，为项目下一阶段的工作制定一个清晰、可执行的任务计划。

计划将分为三个主要阶段：
1.  **第一阶段：安全与核心重构** - 修复所有已发现的严重和高危问题，夯实系统基础。
2.  **第二阶段：功能实现与工具集成** - 完成缺失的核心功能，并将真实的工具脚本集成到执行框架中。
3.  **第三阶段：全面测试与上线准备** - 进行端到端的功能测试、性能测试、安全渗透测试，并完善文档，为最终上线做准备。

---

## 2. 第一阶段：安全与核心重构 (预计时间: 3-5天)

此阶段的目标是解决`improve.md`中列出的所有P1和P2级问题，确保系统在进入功能开发前是安全、稳定和高效的。

-   **任务 1.1: 修复微信支付签名漏洞 (P1)**
    -   **负责人**: 后端开发
    -   **内容**: 严格按照 `improve.md` 中 `Issue #13` 的修复计划，在 `wechat.controller.ts` 中实现并强制执行签名验证。
    -   **验收标准**: 伪造的支付回调请求被正确拒绝；合法的支付回调能被成功处理。

-   **任务 1.2: 实施Prisma与Redis客户端单例模式 (P2)**
    -   **负责人**: 后端开发
    -   **内容**: 根据 `improve.md` 中 `Issue #11 & #12` 的计划，重构数据库和Redis的连接管理，确保全局只有一个实例，并在应用启动时初始化。
    -   **验收标准**: 移除所有服务中的重复连接代码，应用正常启动并能持续响应数据库和缓存请求。

-   **任务 1.3: 重构认证体系以使用HttpOnly Cookie (P2)**
    -   **负责人**: 前后端开发
    *   **内容**: 根据 `improve.md` 中 `Issue #10` 的计划，将JWT传输方式从`localStorage`切换到`httpOnly` cookie。
    -   **验收标准**: 登录后，JWT通过cookie下发；前端应用能通过`withCredentials`自动发送认证信息；登出功能正常工作。

-   **任务 1.4: 增加全局错误处理器与日志记录 (P2)**
    -   **负责人**: 后端开发
    -   **内容**: 根据 `improve.md` 中 `Issue #6` 的计划，在Express中添加全局错误处理中间件，并集成一个日志库（如Winston）。
    -   **验收标准**: 所有API在出错时返回统一结构的JSON响应；服务器端错误被详细记录到日志文件或服务中。

-   **任务 1.5: 强化生产环境配置安全 (P2)**
    -   **负责人**: 后端开发/运维
    -   **内容**: 解决 `improve.md` 中 `Issue #1` 和 `Issue #3`。为生产环境准备独立的、通过环境变量注入的 `JWT_SECRET`，并配置严格的CORS策略。
    -   **验收标准**: 生产部署脚本中包含环境变量的配置；CORS只对指定的前端URL开放。

-   **任务 1.6: 清理重复代码与类型安全 (P3 & P4)**
    -   **负责人**: 后端开发
    -   **内容**: 解决 `improve.md` 中 `Issue #7` 和 `Issue #8`。删除冗余的认证文件，并为JWT Payload定义明确的TypeScript接口。
    -   **验收标准**: 项目结构更清晰；代码类型检查通过。

---

## 3. 第二阶段：功能实现与工具集成 (预计时间: 5-7天)

此阶段的重点是将系统功能补充完整，并用真实逻辑替换掉模拟实现。

-   **任务 2.1: 实现工具执行沙箱 (P1)**
    -   **负责人**: 后端开发/DevOps
    -   **内容**: 严格按照 `improve.md` 中 `Issue #14` 的修复计划，为每个工具创建安全的Dockerfile，并重构`toolWorker.ts`以通过`docker run`来执行任务。这是本阶段的 **最核心任务**。
    -   **验收标准**: 提交一个工具任务后，后端能成功启动一个隔离的Docker容器来执行该任务，并能捕获其输出。

-   **任务 2.2: 开发真实工具脚本**
    -   **负责人**: 算法/工具开发
    -   **内容**:
        -   开发`sdc-generator.py`, `clk-tree-generator.py`, `memory-generator.py`等真实脚本。
        -   脚本应能接收配置文件或命令行参数作为输入，并将结果（log, report, sdc, v, hex等）输出到指定目录。
    -   **验收标准**: 每个脚本都能在本地独立运行，并产生预期的输出文件。

-   **任务 2.3: 集成真实工具脚本与数据持久化**
    -   **负责人**: 后端开发
    -   **内容**:
        -   将开发好的Python脚本集成到`toolWorker`的Docker执行流程中。
        -   实现`toolWorker`中将工具产出物（日志、报告等）上传到私有OSS Bucket的逻辑。
        -   用真实的OSS预签名URL替换掉数据库`ToolRun`表中的模拟URL。
    -   **验收标准**: 运行一个工具任务后，相关的结果文件会出现在OSS中，数据库中记录了正确的、可访问的预签名URL。

-   **任务 2.4: 实现缺失的API (Tools, Feedback, News) (P3)**
    -   **负责人**: 后端开发
    -   **内容**: 根据 `improve.md` 中 `Issue #5` 的计划，完成`Tools`, `Feedback`, `News`模块的后端CRUD API。
    -   **验收标准**: Postman或单元测试能成功调用所有新实现的API端点。

-   **任务 2.5: 开发前端对应页面**
    -   **负责人**: 前端开发
    -   **内容**:
        -   开发"技术指南"、"最新动态"等静态或动态内容页面。
        -   开发"问题反馈"组件。
        -   开发工具结果展示页面，能够正确处理并展示从OSS获取的日志和报告。
    -   **验收标准**: 用户可在前端界面上使用所有新功能。

---

## 4. 第三阶段：全面测试与上线准备 (预计时间: 3-5天)

-   **任务 3.1: 端到端功能测试**
    -   **负责人**: 测试/全体开发
    -   **内容**: 模拟真实用户，走通所有核心业务流程：
        -   注册 -> 邮件验证 -> 登录
        -   选择免费版 -> 使用工具（有次数限制）
        -   选择付费版 -> 支付（支付宝/微信） -> 成为付费会员
        -   付费会员 -> 无限制使用工具
        -   查看、下载工具运行结果
        -   提交反馈
        -   密码重置
    -   **验收标准**: 所有业务流程通畅，无阻塞性BUG。

-   **任务 3.2: 性能与压力测试**
    -   **负责人**: 后端开发/测试
    -   **内容**:
        -   使用`k6`, `JMeter`等工具测试核心API（登录、创建订单、提交任务）的响应时间和吞吐量。
        -   模拟多个用户同时提交工具执行任务，观察任务队列和worker的处理能力。
    -   **验收标准**: 核心API在预期负载下响应时间<200ms；任务队列无明显积压。

-   **任务 3.3: 安全审计与渗透测试**
    -   **负责人**: 安全工程师/开发
    -   **内容**:
        -   验证`improve.md`中所有安全问题是否已修复。
        -   主动测试是否存在SQL注入、XSS、CSRF、命令注入等常见Web漏洞。
        -   检查所有权限控制点，确保用户A无法访问用户B的数据。
        -   验证支付逻辑，尝试伪造支付。
    -   **验收标准**: 未发现新的高危安全漏洞。

-   **任务 3.4: 完善部署文档与CI/CD流程**
    -   **负责人**: DevOps/开发
    -   **内容**:
        -   编写详细的生产环境部署手册。
        -   完善CI/CD流水线，实现自动化构建、测试和部署。
    -   **验收标准**: 代码提交后能自动触发流水线，并成功部署到预发（Staging）环境。 