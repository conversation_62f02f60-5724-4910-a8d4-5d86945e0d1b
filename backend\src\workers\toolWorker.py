# worker.py
import os
import json
import time
import redis
import docker
import shutil
import logging
from sqlalchemy import create_engine, Column, String, Text, DateTime, Enum, JSON
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timezone
from dotenv import load_dotenv

# --- Load Environment Variables ---
# Load from backend/.env
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

# --- SDK Imports ---
from aliyunsdkcore.client import AcsClient
from aliyunsdksts.request.v20150401 import AssumeRoleRequest
from aliyunsdkcr.request.v20170324 import GetAuthorizationTokenRequest
import oss2

# --- Logging Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Configuration ---
REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://user:password@localhost:5432/mydb')
TASK_QUEUE_NAME = os.getenv('TASK_QUEUE_NAME', 'task_queue')
ECS_TOTAL_CPU = int(os.getenv('ECS_TOTAL_CPU', 8))
ECS_TOTAL_MEMORY_GB = int(os.getenv('ECS_TOTAL_MEMORY_GB', 64))
JOB_CPU_REQUEST = int(os.getenv('JOB_CPU_REQUEST', 2))
JOB_MEMORY_REQUEST_GB = int(os.getenv('JOB_MEMORY_REQUEST_GB', 16))

OSS_REGION = os.getenv('OSS_REGION')
OSS_BUCKET_USER_INPUT = os.getenv('OSS_BUCKET_USER_INPUT')
OSS_BUCKET_JOB_RESULTS = os.getenv('OSS_BUCKET_JOB_RESULTS')
OSS_BUCKET_JOB_LOGS = os.getenv('OSS_BUCKET_JOB_LOGS')

ALIYUN_RAM_ROLE_ARN = os.getenv('ALIYUN_RAM_ROLE_ARN')
ALIYUN_STS_REGION = os.getenv('ALIYUN_STS_REGION')
# Credentials for the worker itself to call STS and other services
ALIYUN_ACCESS_KEY_ID = os.getenv('ALIYUN_ACCESS_KEY_ID')
ALIYUN_ACCESS_KEY_SECRET = os.getenv('ALIYUN_ACCESS_KEY_SECRET')
ACR_REGION = os.getenv('ACR_REGION', os.getenv('OSS_REGION')) # Default ACR region to OSS region

# --- Clients Initialization ---
try:
    redis_client = redis.from_url(REDIS_URL)
    docker_client = docker.from_env()
    # Client for calling STS and other services with worker's own credentials
    core_client = AcsClient(ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET, ALIYUN_STS_REGION)
    logging.info("Redis, Docker, and Aliyun clients initialized successfully.")
except Exception as e:
    logging.critical(f"Failed to initialize clients: {e}")
    exit(1)

# --- Database Model Setup (SQLAlchemy) ---
Base = declarative_base()
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)

class Task(Base):
    __tablename__ = 'Task'
    id = Column(String, primary_key=True)
    status = Column(Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='TaskStatus'), nullable=False)
    createdAt = Column('created_at', DateTime, default=lambda: datetime.now(timezone.utc))
    startedAt = Column('started_at', DateTime)
    finishedAt = Column('finished_at', DateTime)
    inputOssPath = Column('input_oss_path', String)
    outputOssPath = Column('output_oss_path', String)
    logOssPath = Column('log_oss_path', String)
    parameters = Column(JSON)
    errorMessage = Column('error_message', Text)
    workerId = Column('worker_id', String)
    ecsInstanceId = Column('ecs_instance_id', String)
    userId = Column('user_id', String, nullable=False)
    toolId = Column('tool_id', String, nullable=False)

class Tool(Base):
    __tablename__ = 'Tool'
    id = Column(String, primary_key=True)
    dockerImage = Column('docker_image', String, nullable=False)

# --- Resource Management ---
resource_manager = {'cpu_used': 0, 'memory_used_gb': 0}

# --- Aliyun Helper Functions ---
def get_sts_credentials_for_task(task_id, user_id):
    """
    Assumes a RAM role to get temporary, scoped-down credentials for OSS operations.
    Implements principle of least privilege with strict resource access control.
    """
    policy = {
        "Statement": [
            {
                "Action": ["oss:GetObject"],
                "Effect": "Allow",
                "Resource": [f"acs:oss:*:*:{OSS_BUCKET_USER_INPUT}/{user_id}/{task_id}/*"],
                "Condition": {
                    "StringEquals": {
                        "oss:x-oss-request-id": f"task-{task_id}"
                    }
                }
            },
            {
                "Action": ["oss:PutObject"],
                "Effect": "Allow",
                "Resource": [
                    f"acs:oss:*:*:{OSS_BUCKET_JOB_RESULTS}/{user_id}/{task_id}/*",
                    f"acs:oss:*:*:{OSS_BUCKET_JOB_LOGS}/{user_id}/{task_id}/*"
                ],
                "Condition": {
                    "StringEquals": {
                        "oss:x-oss-request-id": f"task-{task_id}"
                    }
                }
            },
            {
                "Action": ["oss:ListBucket"],
                "Effect": "Deny",
                "Resource": "*"
            }
        ],
        "Version": "1"
    }
    request = AssumeRoleRequest.AssumeRoleRequest()
    request.set_RoleArn(ALIYUN_RAM_ROLE_ARN)
    request.set_RoleSessionName(f"tool-session-{task_id}")
    request.set_Policy(json.dumps(policy))
    request.set_DurationSeconds(1800)  # 30 minutes validity - reduced for security

    try:
        response = core_client.do_action_with_exception(request)
        creds = json.loads(response)['Credentials']
        
        # Log STS credential issuance for security audit
        logging.info(f"STS credentials issued for task {task_id}, user {user_id}, expires at {creds['Expiration']}")
        
        return creds['AccessKeyId'], creds['AccessKeySecret'], creds['SecurityToken']
    except Exception as e:
        logging.error(f"Failed to assume role for OSS task {task_id}: {e}")
        # Log security event for monitoring
        logging.warning(f"Security Alert: STS credential request failed for task {task_id}, user {user_id}")
        raise

def get_acr_login_info():
    """
    Gets a temporary authorization token for logging into Aliyun Container Registry (ACR).
    """
    request = GetAuthorizationTokenRequest.GetAuthorizationTokenRequest()
    request.set_accept_format('json')
    try:
        # The client for ACR must be initialized for the specific ACR region
        acr_client = AcsClient(ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET, ACR_REGION)
        response = acr_client.do_action_with_exception(request)
        data = json.loads(response)
        return data['authorizationToken'], data['tempUserName']
    except Exception as e:
        logging.error(f"Failed to get ACR authorization token: {e}")
        raise

def get_oss_bucket(access_key_id, access_key_secret, security_token, bucket_name):
    """Initializes an OSS2 Bucket object with STS credentials."""
    auth = oss2.StsAuth(access_key_id, access_key_secret, security_token)
    endpoint = f'http://{OSS_REGION}.aliyuncs.com'
    return oss2.Bucket(auth, endpoint, bucket_name)

# --- Main Worker Logic ---
def process_task(task_id):
    """Processes a single task."""
    session = Session()
    task = None
    local_base = f"/tmp/tasks/{task_id}"
    try:
        logging.info(f"Processing task: {task_id}")
        task = session.query(Task).filter_by(id=task_id).first()
        if not task:
            logging.error(f"Task {task_id} not found in DB.")
            return

        tool = session.query(Tool).filter_by(id=task.toolId).first()
        if not tool:
            raise ValueError(f"Tool {task.toolId} not found.")

        # --- Securely Pull Docker Image from private ACR ---
        logging.info("Getting ACR credentials...")
        acr_token, acr_username = get_acr_login_info()
        registry_url = tool.dockerImage.split('/')[0]
        logging.info(f"Pulling image {tool.dockerImage} from registry {registry_url}...")
        docker_client.images.pull(
            repository=tool.dockerImage,
            auth_config={'username': acr_username, 'password': acr_token}
        )
        logging.info("Image pulled successfully.")

        task.status = 'RUNNING'
        task.startedAt = datetime.now(timezone.utc)
        task.workerId = os.getenv('WORKER_ID', 'worker-01')
        task.ecsInstanceId = os.getenv('ECS_INSTANCE_ID', 'ecs-single-instance')
        session.commit()

        sts_ak, sts_sk, sts_token = get_sts_credentials_for_task(task.id, task.userId)

        container_name = f"tool-job-{task.id}"
        local_input_dir = os.path.join(local_base, "input")
        local_output_dir = os.path.join(local_base, "output")
        local_log_dir = os.path.join(local_base, "logs")
        for d in [local_input_dir, local_output_dir, local_log_dir]:
            os.makedirs(d, exist_ok=True)

        if task.inputOssPath:
            input_bucket = get_oss_bucket(sts_ak, sts_sk, sts_token, OSS_BUCKET_USER_INPUT)
            local_input_filepath = os.path.join(local_input_dir, os.path.basename(task.inputOssPath))
            input_bucket.get_object_to_file(task.inputOssPath, local_input_filepath)
        
        env_vars = {
            'OSS_ACCESS_KEY_ID': sts_ak, 'OSS_ACCESS_KEY_SECRET': sts_sk, 'OSS_SECURITY_TOKEN': sts_token,
            'OSS_REGION': OSS_REGION, 'TASK_ID': task.id, 'USER_ID': task.userId,
            'JOB_PARAMETERS': json.dumps(task.parameters),
            'JOB_OUTPUT_DIR': '/data/output', 'JOB_LOG_DIR': '/data/logs',
            'OSS_BUCKET_OUTPUT': OSS_BUCKET_JOB_RESULTS, 'OSS_BUCKET_LOGS': OSS_BUCKET_JOB_LOGS
        }
        volumes = {
            local_input_dir: {'bind': '/data/input', 'mode': 'ro'},
            local_output_dir: {'bind': '/data/output', 'mode': 'rw'},
            local_log_dir: {'bind': '/data/logs', 'mode': 'rw'},
        }

        logging.info(f"Starting container {container_name} with image {tool.dockerImage}...")
        
        # Log container security configuration for audit
        logging.info(f"Container security: network_mode=none, cap_drop=ALL, memory_limit={JOB_MEMORY_REQUEST_GB}g, cpu_limit={JOB_CPU_REQUEST}")
        
        container = docker_client.containers.run(
            tool.dockerImage,
            detach=True,
            name=container_name,
            environment=env_vars,
            volumes=volumes,
            remove=True,
            cpus=JOB_CPU_REQUEST,
            mem_limit=f"{JOB_MEMORY_REQUEST_GB}g",
            network_mode='none',  # Disable networking for security
            cap_drop=['ALL'],  # Drop all Linux capabilities
            read_only=True,  # Make root filesystem read-only for additional security
            tmpfs={'/tmp': 'rw,noexec,nosuid,size=100m'},  # Secure temporary filesystem
            security_opt=['no-new-privileges:true']  # Prevent privilege escalation
        )
        result = container.wait()
        exit_code = result['StatusCode']
        logs = container.logs().decode('utf-8')
        logging.info(f"Container {container_name} finished with exit code {exit_code}")

        if exit_code == 0:
            task.status = 'COMPLETED'
            output_bucket = get_oss_bucket(sts_ak, sts_sk, sts_token, OSS_BUCKET_JOB_RESULTS)
            output_files = os.listdir(local_output_dir)
            if output_files:
                main_output_file = output_files[0]
                oss_object_name = f"{task.userId}/{task.id}/output/{main_output_file}"
                output_bucket.put_object_from_file(oss_object_name, os.path.join(local_output_dir, main_output_file))
                task.outputOssPath = oss_object_name
        else:
            task.status = 'FAILED'
            task.errorMessage = logs[-2000:]

        log_file_name = f"{task.id}.log"
        local_log_filepath = os.path.join(local_log_dir, log_file_name)
        with open(local_log_filepath, 'w') as f: f.write(logs)
        
        log_bucket = get_oss_bucket(sts_ak, sts_sk, sts_token, OSS_BUCKET_JOB_LOGS)
        oss_log_object_name = f"{task.userId}/{task.id}/{log_file_name}"
        log_bucket.put_object_from_file(oss_log_object_name, local_log_filepath)
        task.logOssPath = oss_log_object_name

        task.finishedAt = datetime.now(timezone.utc)
        session.commit()
        logging.info(f"Task {task.id} finished with status: {task.status}")
    except Exception as e:
        logging.error(f"Error processing task {task_id}: {e}", exc_info=True)
        if task and session.is_active:
            task.status = 'FAILED'
            task.errorMessage = str(e)
            task.finishedAt = datetime.now(timezone.utc)
            session.commit()
    finally:
        if session.is_active: session.close()
        if os.path.exists(local_base): shutil.rmtree(local_base)

def worker_loop():
    """Main loop to poll Redis and process tasks."""
    while True:
        try:
            logging.info("Worker waiting for new task...")
            if resource_manager['cpu_used'] >= ECS_TOTAL_CPU or resource_manager['memory_used_gb'] >= ECS_TOTAL_MEMORY_GB:
                logging.warning("Not enough resources to pick a new task. Waiting...")
                time.sleep(10)
                continue

            _, task_id_bytes = redis_client.blpop(TASK_QUEUE_NAME, timeout=0)
            task_id = task_id_bytes.decode('utf-8')
            
            if resource_manager['cpu_used'] + JOB_CPU_REQUEST > ECS_TOTAL_CPU or resource_manager['memory_used_gb'] + JOB_MEMORY_REQUEST_GB > ECS_TOTAL_MEMORY_GB:
                logging.info(f"Not enough resources for task {task_id}. Re-queueing.")
                redis_client.lpush(TASK_QUEUE_NAME, task_id)
                time.sleep(5)
                continue
            
            resource_manager['cpu_used'] += JOB_CPU_REQUEST
            resource_manager['memory_used_gb'] += JOB_MEMORY_REQUEST_GB
            logging.info(f"Resources allocated for task {task_id}. Used CPU: {resource_manager['cpu_used']}/{ECS_TOTAL_CPU}, Used Mem: {resource_manager['memory_used_gb']}/{ECS_TOTAL_MEMORY_GB}GB.")

            try:
                process_task(task_id)
            finally:
                resource_manager['cpu_used'] -= JOB_CPU_REQUEST
                resource_manager['memory_used_gb'] -= JOB_MEMORY_REQUEST_GB
                logging.info(f"Resources released for task {task_id}. Used CPU: {resource_manager['cpu_used']}, Used Mem: {resource_manager['memory_used_gb']}GB.")
        except Exception as e:
            logging.critical(f"An unexpected error occurred in worker loop: {e}", exc_info=True)
            time.sleep(10)

if __name__ == '__main__':
    logging.info("Starting Python Task Worker...")
    worker_loop() 