# 页面功能检查与后续开发计划 (Page Check 1.0)

本文档旨在对项目前端所有核心页面的当前状态进行全面审查，并基于已完成的后端API和业务逻辑，制定详细的后续开发与优化计划。

## 1. 新增与修改页面功能审查

以下是对近期新增和修改的页面进行的详细检查，确认其功能完整性、后端依赖满足情况和潜在问题。

### 1.1. 认证流程页面

-   **`forgot-password.tsx`**:
    -   **功能**: 已实现。提供表单让用户输入邮箱以请求密码重置链接。
    -   **后端依赖**: `POST /api/v1/auth/request-password-reset`，已实现。
    -   **状态**: ✅ **完成**。

-   **`reset-password.tsx`**:
    -   **功能**: 已实现。用户通过邮件链接访问，可输入新密码进行重置。能从URL中正确解析`token`。
    -   **后端依赖**: `POST /api/v1/auth/reset-password`，已实现。
    -   **状态**: ✅ **完成**。

### 1.2. 订单与支付流程页面

-   **`order/confirm.tsx`**:
    -   **功能**: **占位页面**。目前是静态UI，缺少从上一步（如`PricingPage`）动态获取所选`plan`并展示订单详情的逻辑。
    -   **后端依赖**: 需要`GET /api/v1/plans/:id`（或从列表页传递）获取计划详情，并调用`POST /api/v1/orders`创建订单。
    -   **状态**: ⚠️ **功能待实现**。

-   **`order/checkout.tsx`**:
    -   **功能**: **核心支付页面**。能够根据订单信息生成支付二维码，并轮询检查支付状态。
    -   **后端依赖**:
        -   `POST /api/v1/orders`：用于创建订单并获取支付二维码URL，已实现。
        -   `GET /api/v1/orders/:orderId/status`：用于轮询订单状态，当前路由为`GET /api/v1/orders/:orderId`返回整个订单，需要调整或前端适配。
    -   **状态**: ✅ **基本完成**，但轮询逻辑需要与后端API微调。

-   **`payment/result.tsx`**:
    -   **功能**: 已实现。能根据URL参数（`status`, `orderNo`）显示支付成功或失败的友好提示。
    -   **后端依赖**: 无直接后端API依赖，状态由`checkout`页面跳转时传递。
    -   **状态**: ✅ **完成**。

### 1.3. 用户中心页面

-   **`profile.tsx`**:
    -   **功能**: 已实现。能够展示用户基本信息和当前的订阅状态（计划、有效期）。
    -   **后端依赖**:
        -   `GET /api/v1/users/me`：获取用户信息，已实现。
        -   `GET /api/v1/subscriptions/me`：获取订阅详情，已实现。
    -   **状态**: ✅ **完成**。

-   **`order/history.tsx`**:
    -   **功能**: 已实现。能分页展示当前用户的订单历史列表。
    -   **后端依赖**: `GET /api/v1/orders` (带分页参数)，已实现。
    -   **状态**: ✅ **完成**。

-   **`order/details.tsx`**:
    -   **功能**: 已实现。能展示单个订单的详细信息。
    -   **后端依赖**: `GET /api/v1/orders/:orderId`，已实现。
    -   **状态**: ✅ **完成**。

### 1.4. 工具与内容页面

-   **`tools/index.tsx`**:
    -   **功能**: 已重构。从动态加载改为静态展示工具列表，UI/UX大幅优化，交互更友好。
    -   **后端依赖**: 无。
    -   **状态**: ✅ **完成**。

-   **`tools/SdcGeneratorPage.tsx`**:
    -   **功能**: 已重构。UI和交互逻辑优化，表单分组更清晰。
    -   **后端依赖**: `POST /api/v1/tasks`，已实现。
    -   **状态**: ✅ **完成**。

-   **`contact.tsx`**:
    -   **功能**: **占位页面**。目前是纯静态UI，提交表单无实际功能。
    -   **后端依赖**: 需要一个`POST /api/v1/feedback`之类的接口来接收用户反馈。
    -   **状态**: ⚠️ **功能待实现**。

## 2. 后续页面开发与优化计划

### 2.1. 必须新增或实现功能的页面

1.  **`order/confirm.tsx` (订单确认页)**
    -   **详细任务**:
        -   实现逻辑以接收从`PricingPage`传递过来的`planId`。
        -   调用API获取该`plan`的详细信息（价格、名称）。
        -   允许用户选择**支付周期**（`BillingCycle.MONTHLY` / `ANNUALLY`）。
        -   用户确认后，调用`orderService.createOrder`创建订单，并将返回的`orderId`传递给`checkout`页面。

2.  **`contact.tsx` (联系我们/反馈页)**
    -   **详细任务**:
        -   **后端**: 创建`feedback.controller.ts`和`feedback.routes.ts`，并实现一个`POST /api/v1/feedback`的API端点，将接收到的内容存入`Feedback`表。
        -   **前端**: 在`contact.tsx`中实现表单提交逻辑，调用上述API，并在提交后使用`Toast`组件给予用户成功提示。

3.  **后台管理系统 (Admin Panel)**
    -   **描述**: 这是目前项目**完全缺失**但至关重要的模块。需要一套全新的、受管理员角色保护的前端页面和后端API。
    -   **页面清单**:
        -   `/admin/dashboard`: 核心业务指标监控。
        -   `/admin/users`: 用户管理。
        -   `/admin/plans`: 会员计划管理。
        -   `/admin/tools`: EDA工具管理。
        -   `/admin/tasks`: 系统任务监控。

### 2.2. 需要根据业务逻辑优化的页面

1.  **`order/checkout.tsx` (支付页)**
    -   **优化点**: 当前使用HTTP轮询检查订单状态，这会给服务器带来不必要的周期性压力。
    -   **建议**:
        -   **后端**: 引入`WebSocket` (`socket.io`或`ws`库)。当支付回调成功处理订单后，通过WebSocket向指定的前端客户端（基于`userId`或`orderId`）推送订单状态更新消息。
        -   **前端**: 在页面加载时建立WebSocket连接，监听来自服务器的状态更新事件，替代轮询逻辑。这将实现真正的实时更新，并极大提升性能和用户体验。

2.  **`tools/*.tsx` (所有工具执行页)**
    -   **优化点**:
        -   **权限检查**: 当前提交任务时，后端未检查用户的订阅状态和配额。
        -   **状态查询**: 与支付页类似，任务状态也通过轮询获取。
    -   **建议**:
        -   **后端**:
            -   创建`subscription.middleware.ts`。在`POST /api/v1/tasks`路由上使用该中间件。它会检查用户的`Subscription`是否`ACTIVE`，并从`Plan.features`中读取配额，与用户的`Task`历史记录进行比较，超出则拒绝。
            -   同样使用WebSocket技术，在`toolWorker`完成任务并更新数据库状态后，向前端实时推送`COMPLETED`或`FAILED`状态。
        -   **前端**:
            -   在提交任务前，可以先调用`GET /api/v1/subscriptions/me`检查用户状态，如果配额不足，可直接在UI上提示，避免不必要的后端请求。
            -   改造状态显示逻辑，以接收来自WebSocket的实时更新。

3.  **`profile.tsx` (个人资料页)**
    -   **优化点**: 页面功能比较单一，可以进一步扩展。
    -   **建议**:
        -   增加**修改个人信息**的功能，如昵称、头像。需要实现`PATCH /api/v1/users/me`接口。
        -   增加**取消订阅**的按钮，调用`DELETE /api/v1/subscriptions/me`接口，并在成功后更新UI状态。
        -   增加**修改密码**的入口，引导用户至一个专用的修改密码表单页面。

## 3. 总结

经过本次代码审查与功能梳理，项目在用户支付、订单管理和核心工具使用方面的前端框架已基本搭建完成，后端也提供了必要的API支持。

**后续工作的优先级应为：**
1.  **打通核心业务流**: 优先完成`order/confirm.tsx`和`contact.tsx`的逻辑开发，确保用户可以完整地走通"选择计划 -> 确认订单 -> 支付 -> 获得订阅"的核心商业流程，并提供反馈渠道。
2.  **优化实时体验**: 将支付和任务状态的查询从轮询升级为WebSocket，这是提升产品专业度和用户体验的关键一步。
3.  **实现精细化权限**: 为工具执行API添加基于订阅的权限和配额检查中间件，完善SaaS产品的商业逻辑。
4.  **启动后台管理**: 开始规划并逐步开发后台管理系统，为平台的长期运营和维护打下基础。 