"use client";

import React, { createContext, useState, useContext, useEffect, ReactNode, useCallback } from 'react';
import { getMe } from '@/services/user.service';
import { logout as logoutUser } from '@/services/auth.service';

// 定义角色枚举，与后端保持一致
export enum Role {
  USER = 'USER',
  ADMIN = 'ADMIN'
}

interface User {
  id: string;
  email: string;
  name: string | null; // 更新字段名以匹配后端schema
  avatar: string | null; // 更新字段名以匹配后端schema
  isVerified: boolean;
  role: Role; // 添加角色字段
  createdAt: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (user: User) => void;
  logout: () => Promise<void>;
  loading: boolean;
  isAdmin: boolean; // 添加便捷的管理员检查
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);

  // 计算是否为管理员
  const isAdmin = user?.role === Role.ADMIN;

  const logout = useCallback(async () => {
    try {
      await logoutUser();
    } catch (error) {
      console.error('Logout failed', error);
    } finally {
      setIsAuthenticated(false);
      setUser(null);
    }
  }, []);
  
  useEffect(() => {
    const checkAuthStatus = async () => {
      setLoading(true);
      try {
        const response = await getMe();
        setUser(response.data);
        setIsAuthenticated(true);
      } catch (error) {
        // 静默处理错误，不让它冒泡到全局拦截器
        // 这样游客访问时，获取用户信息失败（401）不会触发全局跳转
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();
  }, []);
  
  const login = (user: User) => {
    setUser(user);
    setIsAuthenticated(true);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout, loading, isAdmin }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 