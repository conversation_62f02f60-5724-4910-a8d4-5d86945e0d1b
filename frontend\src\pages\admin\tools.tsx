import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Wrench } from 'lucide-react';

const ToolsPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <Wrench className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold text-gray-900">工具管理</h1>
          <p className="text-gray-600">管理系统工具和配置</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>工具管理功能开发中</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            工具管理功能正在开发中，敬请期待。
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default ToolsPage; 