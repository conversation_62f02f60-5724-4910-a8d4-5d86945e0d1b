import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToolExecution, TaskStatus } from '../../hooks/useToolExecution';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, Download, FileText } from 'lucide-react';
import { motion } from 'framer-motion';

const clkFormSchema = z.object({
  sourceSpec: z.string().min(1, "时钟源规格不能为空"),
  targetDomains: z.coerce.number().int().min(1, "至少需要1个目标时钟域").max(16, "最多支持16个目标时钟域"),
  bufferType: z.enum(['BUFGMUX', 'BUFGCE', 'BUFG']),
});

type ClkFormValues = z.infer<typeof clkFormSchema>;

const ClkGeneratorPage: React.FC = () => {
    const { taskStatus, submitTask, resetTask, handleDownload } = useToolExecution();

    const form = useForm<ClkFormValues>({
        resolver: zodResolver(clkFormSchema),
        defaultValues: {
            sourceSpec: '200MHz crystal oscillator',
            targetDomains: 3,
            bufferType: 'BUFGMUX',
        },
    });

    const onSubmit = (data: ClkFormValues) => {
        submitTask({
            toolId: 'clk-generator',
            parameters: data,
        });
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto max-w-2xl p-4 sm:p-6 lg:p-8"
        >
            {taskStatus.status !== 'IDLE' ? (
                <TaskResultDisplay taskStatus={taskStatus} resetTask={resetTask} handleDownload={handleDownload} />
            ) : (
                <Card className="shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-2xl md:text-3xl font-bold">时钟电路自动生成工具</CardTitle>
                        <CardDescription>根据您的需求，自动生成优化的时钟缓冲和分配网络。</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField control={form.control} name="sourceSpec" render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>时钟源规格</FormLabel>
                                        <FormControl><Input placeholder="例如: 200MHz crystal oscillator" {...field} /></FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />
                                <FormField control={form.control} name="targetDomains" render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>目标时钟域数量</FormLabel>
                                        <FormControl><Input type="number" {...field} /></FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />
                                <FormField control={form.control} name="bufferType" render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>缓冲器类型</FormLabel>
                                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                                            <FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl>
                                            <SelectContent>
                                                <SelectItem value="BUFGMUX">BUFGMUX</SelectItem>
                                                <SelectItem value="BUFGCE">BUFGCE</SelectItem>
                                                <SelectItem value="BUFG">BUFG</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )} />
                                <Button type="submit" className="w-full" disabled={form.formState.isSubmitting}>
                                    {form.formState.isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                                    {form.formState.isSubmitting ? '正在提交...' : '生成电路'}
                                </Button>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            )}
        </motion.div>
    );
};

// Re-using the TaskResultDisplay component from MemoryDataGeneratorPage.
// For a real app, this would be in a shared components directory.
const TaskResultDisplay = ({ taskStatus, resetTask, handleDownload }: { taskStatus: TaskStatus, resetTask: () => void, handleDownload: (type: 'result' | 'log') => void }) => {
    return (
         <div className="container mx-auto max-w-2xl p-4 sm:p-6 lg:p-8">
            <Card>
                <CardHeader>
                    <CardTitle>任务状态 (ID: {taskStatus.taskId})</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {taskStatus.status === 'POLLING' && (
                        <div className="text-center">
                            <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
                            <p className="mt-2 text-lg">正在执行任务... {taskStatus.progress}%</p>
                        </div>
                    )}
                     {taskStatus.errorMessage && (
                        <Alert variant="destructive">
                            <AlertTitle>任务失败</AlertTitle>
                            <AlertDescription>{taskStatus.errorMessage}</AlertDescription>
                        </Alert>
                    )}
                    {taskStatus.status === 'COMPLETED' && (
                        <Alert variant="default" className="border-green-500 text-green-700">
                             <AlertTitle>任务成功完成！</AlertTitle>
                            <AlertDescription>您可以下载结果文件和日志。</AlertDescription>
                        </Alert>
                    )}
                     <div className="flex justify-center gap-4 pt-4">
                        {taskStatus.status === 'COMPLETED' && taskStatus.resultUrl && (
                             <Button onClick={() => handleDownload('result')}><Download className="mr-2 h-4 w-4" />下载结果</Button>
                        )}
                        {taskStatus.logUrl && (
                             <Button variant="secondary" onClick={() => handleDownload('log')}><FileText className="mr-2 h-4 w-4" />下载日志</Button>
                        )}
                    </div>
                    <Button onClick={resetTask} className="w-full">开始新任务</Button>
                </CardContent>
            </Card>
        </div>
    );
}

export default ClkGeneratorPage; 