---
description: 
globs: 
alwaysApply: false
---
# 编码规范与架构指南

本目录包含芯片后端知识平台项目的编码规范、技术架构规则和编程语言具体实践指南。这些规则旨在确保代码质量、一致性和可维护性，同时促进团队成员之间的高效协作。

## 规则文件说明

1. **通用编码规则** (`general_coding_rules.mdc`)
   - 包含适用于整个项目的编码风格、原则和最佳实践
   - 涵盖命名规范、代码格式、注释规范等基础规则
   - 定义了代码架构、性能、安全和可维护性的基本原则

2. **技术架构规则** (`technical_architecture_rules.mdc`)
   - 详细说明前端、后端、数据库和DevOps的架构设计规则
   - 规定了组件设计、状态管理、API通信等前端架构准则
   - 定义了API设计、服务层设计、数据库访问等后端架构准则
   - 包含数据库设计、容器化和部署策略的规则

3. **特定编程语言规则** (`language_specific_rules.mdc`)
   - 针对项目中使用的具体编程语言（TypeScript、JavaScript等）的规则
   - 包含TypeScript类型系统使用、JavaScript最佳实践等
   - 定义SQL查询和HTML/CSS编写的规范

## 规则应用指南

1. **新功能开发**
   - 在开始新功能开发前，开发人员应熟悉这些规则
   - 使用这些规则作为代码设计和实现的指导方针
   - 遵循既定的架构模式和编码风格

2. **代码审查**
   - 在代码审查过程中，参考这些规则作为评审标准
   - 确保新提交的代码符合项目的规范要求
   - 对违反规则的情况提出建设性的改进建议

3. **持续改进**
   - 这些规则不是一成不变的，应根据项目发展和团队反馈进行调整
   - 鼓励团队成员对规则提出改进建议
   - 定期回顾和更新规则，以反映最新的最佳实践

## 规则优先级

当不同规则文件之间存在冲突时，遵循以下优先级：

1. 特定编程语言规则
2. 技术架构规则
3. 通用编码规则

项目特定需求可能导致某些情况下需要偏离这些规则，这种情况应当在代码中通过注释说明，并在团队内部达成共识。