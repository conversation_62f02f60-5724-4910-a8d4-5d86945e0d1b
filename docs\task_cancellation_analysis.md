# 任务撤销功能详细分析报告

## 📋 功能概述

任务撤销功能允许用户在任务执行过程中主动取消任务，适用于用户发现需求填写错误或不再需要该任务结果的场景。

## 🔍 技术复杂度分析

### 1. 前端层面 (复杂度: ⭐⭐)

**需要修改的组件**：
- `SdcGeneratorPage.tsx`: 添加撤销按钮
- `useToolExecution.ts`: 添加撤销逻辑
- `TaskStatusDisplay`: 显示撤销选项

**实现要点**：
```typescript
// 新增撤销函数
const cancelTask = async (taskId: string) => {
    const confirmed = window.confirm('确定要撤销此任务吗？');
    if (confirmed) {
        await api.delete(`/tasks/${taskId}`);
        resetTask();
    }
};

// UI显示逻辑
{(taskStatus.status === 'POLLING' || taskStatus.status === 'SUBMITTING') && (
    <Button onClick={() => cancelTask(taskStatus.taskId)} variant="destructive">
        撤销任务
    </Button>
)}
```

### 2. 后端API层面 (复杂度: ⭐⭐⭐)

**需要新增的接口**：
```typescript
// DELETE /api/v1/tasks/:taskId
export const cancelTask = async (req: Request, res: Response) => {
    const { taskId } = req.params;
    const userId = req.user.id;
    
    // 1. 验证任务所有权
    const task = await prisma.task.findFirst({
        where: { id: taskId, userId }
    });
    
    if (!task) {
        return res.status(404).json({ message: 'Task not found' });
    }
    
    // 2. 检查任务状态
    if (!['PENDING', 'RUNNING'].includes(task.status)) {
        return res.status(400).json({ 
            message: 'Task cannot be cancelled in current status' 
        });
    }
    
    // 3. 发送撤销信号给Worker
    await sendCancellationSignal(taskId);
    
    // 4. 更新数据库状态
    await prisma.task.update({
        where: { id: taskId },
        data: { 
            status: 'CANCELLED',
            finishedAt: new Date(),
            errorMessage: 'Task cancelled by user'
        }
    });
    
    res.json({ message: 'Task cancelled successfully' });
};
```

### 3. Worker层面 (复杂度: ⭐⭐⭐⭐⭐)

**最复杂的部分**，需要处理不同执行阶段的中断：

**实现方案**：
```python
# 在toolWorker.py中添加撤销检查机制
import signal
import threading

class TaskCancellationHandler:
    def __init__(self, task_id):
        self.task_id = task_id
        self.cancelled = False
        self.check_interval = 5  # 每5秒检查一次
        
    def start_monitoring(self):
        """启动撤销监控线程"""
        self.monitor_thread = threading.Thread(target=self._monitor_cancellation)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
    def _monitor_cancellation(self):
        """监控撤销信号"""
        while not self.cancelled:
            # 检查Redis中的撤销信号
            cancellation_key = f"task_cancellation:{self.task_id}"
            if redis_client.get(cancellation_key):
                self.cancelled = True
                self._handle_cancellation()
                break
            time.sleep(self.check_interval)
    
    def _handle_cancellation(self):
        """处理撤销逻辑"""
        logging.info(f"Task {self.task_id} cancellation requested")
        
        # 1. 停止Docker容器
        try:
            container_name = f"sdc_task_{self.task_id}"
            docker_client.containers.get(container_name).stop(timeout=10)
            docker_client.containers.get(container_name).remove()
        except Exception as e:
            logging.error(f"Failed to stop container: {e}")
        
        # 2. 清理临时文件
        temp_dir = f"/tmp/logiccore_jobs/{self.task_id}"
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        
        # 3. 清理OSS文件
        self._cleanup_oss_files()
        
        # 4. 更新数据库状态
        self._update_task_status('CANCELLED')

def process_task_with_cancellation(task_id):
    """带撤销功能的任务处理"""
    cancellation_handler = TaskCancellationHandler(task_id)
    cancellation_handler.start_monitoring()
    
    try:
        # 在每个关键步骤前检查撤销状态
        if cancellation_handler.cancelled:
            return
            
        # 步骤1: 下载文件
        download_input_files(task_id)
        
        if cancellation_handler.cancelled:
            return
            
        # 步骤2: 执行Docker
        run_docker_container(task_id)
        
        if cancellation_handler.cancelled:
            return
            
        # 步骤3: 上传结果
        upload_results(task_id)
        
    except Exception as e:
        if not cancellation_handler.cancelled:
            handle_task_error(task_id, e)
```

### 4. 数据库层面 (复杂度: ⭐⭐)

**需要的数据库修改**：
```sql
-- 可能需要添加新字段
ALTER TABLE Task ADD COLUMN cancelledAt TIMESTAMP;
ALTER TABLE Task ADD COLUMN cancellationReason TEXT;

-- 新增撤销记录表（可选）
CREATE TABLE TaskCancellation (
    id VARCHAR(255) PRIMARY KEY,
    taskId VARCHAR(255) NOT NULL,
    userId VARCHAR(255) NOT NULL,
    reason TEXT,
    cancelledAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (taskId) REFERENCES Task(id),
    FOREIGN KEY (userId) REFERENCES User(id)
);
```

### 5. Redis通信机制 (复杂度: ⭐⭐⭐)

**撤销信号传递**：
```typescript
// 后端发送撤销信号
const sendCancellationSignal = async (taskId: string) => {
    const redis = getRedisClient();
    
    // 方案1: 使用Redis键值对
    await redis.set(`task_cancellation:${taskId}`, 'true', 'EX', 3600);
    
    // 方案2: 使用Redis Pub/Sub (更实时)
    await redis.publish(`task_cancellation_channel`, JSON.stringify({
        taskId,
        action: 'cancel',
        timestamp: new Date().toISOString()
    }));
};
```

## 📊 不同执行阶段的撤销复杂度

### PENDING阶段 (简单 ⭐⭐)
- **状态**: 任务在Redis队列中等待
- **撤销操作**: 从队列移除 + 更新数据库 + 清理OSS输入文件
- **风险**: 低
- **实现难度**: 简单

### RUNNING阶段 (复杂 ⭐⭐⭐⭐⭐)

#### 子阶段1: 文件下载中
- **撤销操作**: 中断下载 + 清理部分文件
- **风险**: 中等

#### 子阶段2: Docker执行中
- **撤销操作**: 强制停止容器 + 清理临时文件
- **风险**: 高（可能有资源泄露）

#### 子阶段3: 结果上传中
- **撤销操作**: 中断上传 + 清理部分结果文件
- **风险**: 中等

## 🚨 风险评估

### 高风险点
1. **Docker容器强制停止**: 可能导致资源泄露
2. **文件清理不完整**: 可能占用存储空间
3. **并发撤销**: 可能导致数据不一致
4. **Worker无响应**: 撤销信号无法传达

### 缓解措施
1. **超时机制**: 撤销操作设置超时时间
2. **定期清理**: 定期清理孤儿资源
3. **状态锁**: 防止并发操作冲突
4. **监控告警**: 监控撤销操作成功率

## 💡 实现建议

### 阶段性实现
1. **第一阶段**: 只支持PENDING状态的撤销
2. **第二阶段**: 支持RUNNING状态的撤销
3. **第三阶段**: 完善异常处理和监控

### 最小可行产品(MVP)
```typescript
// 简化版本：只支持PENDING状态撤销
const cancelTask = async (taskId: string) => {
    const task = await prisma.task.findFirst({
        where: { id: taskId, status: 'PENDING' }
    });
    
    if (!task) {
        throw new Error('Task cannot be cancelled');
    }
    
    // 从Redis队列移除
    await redis.lrem('task_queue', 0, taskId);
    
    // 更新数据库
    await prisma.task.update({
        where: { id: taskId },
        data: { status: 'CANCELLED' }
    });
    
    // 清理OSS文件
    await cleanupOssFiles(task.userId, taskId);
};
```

## 📈 开发工作量估算

| 模块 | 工作量 | 风险等级 |
|------|--------|----------|
| 前端UI | 1-2天 | 低 |
| 后端API | 2-3天 | 中 |
| Worker改造 | 5-7天 | 高 |
| 测试验证 | 3-4天 | 中 |
| 文档编写 | 1天 | 低 |
| **总计** | **12-17天** | **中高** |

## 🎯 结论和建议

### 复杂度评级: ⭐⭐⭐⭐ (4/5)

**建议**:
1. **暂不实现**: 考虑到实现复杂度和风险，建议暂时不实现任务撤销功能
2. **替代方案**: 通过改善防重复提交机制和用户引导来减少错误提交
3. **未来规划**: 如果确实需要，建议分阶段实现，先支持PENDING状态撤销

**如果必须实现，建议采用MVP方案**:
- 只支持PENDING状态的任务撤销
- 完善的用户提示和确认机制
- 详细的操作日志和监控
