// API 基础配置和通用请求函数

import axios, { AxiosError, AxiosRequestConfig } from 'axios';

// A simple toast implementation, can be replaced with a more complete toast component later
const toast = {
  error: (message: string) => {
    console.error('API Error:', message);
    // You can integrate an actual toast component here
    alert(message); // Using alert temporarily, a better UI component should be used in production
  }
};

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // You can add common request headers or tokens here
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    // Unified error handling
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data as any;
      // Check if global 401 handling needs to be skipped
      if (status === 401 && error.config?.skipGlobal401Handler) {
        return Promise.reject(error); // Reject directly, to be handled by the catch block at the request call site
      }
      
      switch (status) {
        case 401:
          // Authentication failure, clear local auth state and redirect to login page
          toast.error('登录已过期，请重新登录');
          // The logic to clear auth state will be handled in AuthContext
          if (window.location.pathname !== '/auth/login') {
            window.location.href = '/auth/login';
          }
          break;
          
        case 403:
          toast.error('权限不足，无法访问该资源');
          break;
          
        case 404:
          toast.error('请求的资源不存在');
          break;
          
        case 422:
          // Form validation error
          const message = data?.message || '数据验证失败';
          toast.error(message);
          break;
          
        case 429:
          toast.error('请求过于频繁，请稍后再试');
          break;
          
        case 500:
        case 502:
        case 503:
        case 504:
          toast.error('服务器错误，请稍后再试');
          break;
          
        default:
          toast.error(data?.message || '请求失败，请稍后再试');
      }
    } else if (error.request) {
      // Network error
      toast.error('网络连接失败，请检查网络设置');
    } else {
      // Other errors
      toast.error('请求发生错误，请稍后再试');
    }
    
    return Promise.reject(error);
  }
);

export default api;