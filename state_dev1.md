# ChipCore 系统详细说明文档 (版本 apha-dev1)

本Apha版本报告是基于对现有前后端代码库的全面静态分析得出的。报告旨在提供对项目当前开发状态的深入理解，涵盖已实现的功能、技术架构、业务逻辑、数据库设计，并为未来的开发提供优化建议。

## 1. 已开发的功能清单

### 1.1. 核心框架
- **认证管理**:
  - 用户注册与邮箱验证
  - 用户登录与注销
  - "记住我"功能 (通过Cookie有效期实现)
  - 忘记密码与密码重置流程
  - 重新发送邮箱验证链接
- **会员与订阅**:
  - 查看不同的会员计划 (Plans)
  - 创建订单以购买或续费会员
  - 用户个人中心查看订单历史
- **支付系统**:
  - 集成支付宝 (扫码支付)
  - 集成微信支付 (扫码支付)
  - 通过Webhook接收并处理支付网关的异步通知

### 1.2. 辅助功能
- **工具运行**: (部分实现) 定义了工具模型和运行记录，但具体工具的前端页面和逻辑待实现。
- **用户反馈**: 定义了数据模型，但提交和管理功能待实现。
- **新闻/公告**: 定义了数据模型，但发布和展示功能待实现。
- **审计日志**: 定义了数据模型，但具体日志记录逻辑待实现。


## 2. 技术架构详解

项目采用**前后端分离**的**Monorepo**架构，通过 `npm workspaces` 进行管理。

- **前端 (Frontend)**: 基于 **Vite** 构建的 **React (v18)** 单页应用 (SPA)。负责用户界面和交互。
- **后端 (Backend)**: 基于 **Node.js** 和 **Express.js** 的 RESTful API 服务。负责业务逻辑、数据处理和与数据库的交互。
- **数据库 (Database)**: 使用 **PostgreSQL** 作为主数据存储。
- **缓存 (Cache)**: 使用 **Redis** 进行会话管理和缓存，特别是用于存储一次性的验证令牌。
- **容器化**: 使用 **Docker** 和 `docker-compose.yml` 来编排 `PostgreSQL` 和 `Redis` 服务，简化开发环境的搭建和部署。

### 2.1. 架构图

```mermaid
graph TD
    subgraph "用户端"
        A[浏览器/客户端]
    end

    subgraph "前端 (React SPA on Vite)"
        B[UI组件 - shadcn/ui]
        C[路由 - React Router]
        D[状态管理 - TanStack Query]
        E[API请求 - Axios]
    end

    subgraph "后端 (Node.js/Express)"
        F[API网关 (Routes)]
        G[控制器 (Controllers)]
        H[服务层 (Services)]
        I[数据访问/ORM (Prisma)]
    end

    subgraph "基础设施 (Docker)"
        J[PostgreSQL数据库]
        K[Redis缓存]
    end
    
    subgraph "第三方服务"
        L[支付宝]
        M[微信支付]
        N[邮件服务 (Nodemailer)]
    end

    A -- HTTP/S --> C
    A -- HTTP/S --> F
    C -- 调用 --> E
    E -- API请求 --> F
    F -- 路由到 --> G
    G -- 调用 --> H
    H -- 业务逻辑 --> H
    H -- 数据操作 --> I
    H -- 支付请求 --> L
    H -- 支付请求 --> M
    H -- 发送邮件 --> N
    I -- SQL查询 --> J
    H -- 读写令牌 --> K
    
    L -- 异步通知 --> F
    M -- 异步通知 --> F
```

## 3. 技术栈详解

### 3.1. 前端技术栈

- **核心框架**: React 18, TypeScript, Vite
- **路由**: React Router DOM v7
- **UI组件库**: shadcn/ui (基于 Radix UI Headless组件, 使用 Tailwind CSS)
- **样式**: Tailwind CSS v3
- **状态管理**: TanStack React Query v5 (主要用于服务端状态缓存) & React Context API (用于全局UI状态，如认证)
- **表单**: React Hook Form v7 & Zod (用于校验)
- **HTTP客户端**: Axios
- **动画**: Framer Motion
- **图表**: Recharts
- **图标**: Lucide React
- **代码规范**: ESLint, Prettier

### 3.2. 后端技术栈

- **核心框架**: Node.js, Express.js, TypeScript
- **ORM**: Prisma v5
- **数据库**: PostgreSQL
- **缓存**: Redis v4 (Client)
- **认证**: Passport.js (Local Strategy), JSON Web Tokens (JWT), Bcrypt.js
- **支付**: `alipay-sdk`, `wechatpay-axios-plugin`
- **校验**: Zod
- **邮件**: Nodemailer
- **构建/运行**: `tsx` (开发时), `esbuild` (构建)

## 4. 认证管理系统业务逻辑详解

认证系统采用 **JWT (JSON Web Token)** 方案，但前后端的实现方式存在**严重不匹配**，这是一个**高优先级**需要修正的问题。

### 4.1. 后端实现 (基于 `httpOnly` Cookie)

1.  **注册**:
    - `POST /api/auth/register`
    - Service层 (`auth.service.ts`) 对密码进行`bcrypt`哈希加密。
    - 创建新用户，`isVerified` 默认为 `false`。
    - 生成一个随机的、一次性的验证令牌，以 `verification:<token>` 为键，`userId` 为值存入 **Redis**，有效期24小时。
    - 通过 `nodemailer` 发送包含验证链接的邮件。

2.  **邮件验证**:
    - `GET /api/auth/verify-email?token=<token>`
    - 从Redis中查找令牌。
    - 如果找到，则将对应用户的 `isVerified` 字段更新为 `true`，并从Redis中删除该令牌。
    - 重定向到前端指定的结果页面。

3.  **登录**:
    - `POST /api/auth/login`
    - 校验邮箱和密码。
    - **强制检查用户的 `isVerified` 状态**，未验证则拒绝登录。
    - 成功后，生成JWT，其中包含 `userId` 和 `email`。
    - **将JWT存入一个 `httpOnly`, `secure`, `SameSite=strict` 的Cookie (`access_token`) 中**。这种方式更安全，可有效防止XSS攻击。
    - **响应体中不包含token**，只返回用户信息。

4.  **登出**:
    - `POST /api/auth/logout`
    - 清除 `access_token` Cookie。

5.  **密码重置**:
    - 流程与邮件验证类似，通过Redis存储一次性的密码重置令牌，有效期1小时。

### 4.2. 前端实现 (基于 `localStorage`)

1.  **`AuthContext.tsx`**:
    - 提供全局的 `user`, `login`, `logout` 等状态和方法。
    - **问题点**: `login` 方法尝试从登录接口的**响应体**中解析 `token` 和 `user` 数据。
    - **问题点**: 将获取到的 `token` 和 `user` 对象存储在 **`localStorage`** 中。
    - 页面加载时，从 `localStorage` 恢复登录状态。

### 4.3. 关键问题与修正建议

- **问题**: 前后端认证方式完全不匹配。后端使用安全的 `httpOnly` Cookie，而前端期望从响应体中获取Token并存入 `localStorage`。**当前代码无法完成登录。**
- **建议**:
    1.  **统一认证方式为 `httpOnly` Cookie**。这是更安全的最佳实践。
    2.  修改前端 `AuthContext` 和所有API请求的逻辑。
        - 移除 `localStorage` 的Token存储。
        - 配置 `axios` 实例，使其在所有请求中自动携带Cookie (`withCredentials: true`)。
        - 创建一个 `/api/auth/me` 或 `/api/auth/session` 类似的后端端点，用于在前端应用加载时，通过Cookie验证用户身份并返回用户信息。
        - `AuthContext` 在应用加载时调用此端点来恢复登录状态，而不是从 `localStorage` 读取。

## 5. 会员支付交易系统业务逻辑详解

支付系统设计健壮，核心流程围绕 `Order` 和 `Subscription` 两个模型展开，并正确使用了数据库事务来保证数据一致性。

### 5.1. 流程图

```mermaid
sequenceDiagram
    participant FE as 前端 (用户)
    participant BE as 后端 (Express)
    participant DB as 数据库 (Prisma)
    participant Pay as 支付网关 (支付宝/微信)

    FE->>BE: 1. POST /api/orders (planId, billingCycle, paymentMethod)
    activate BE
    BE->>DB: 2. 查询 Plan 获取价格
    DB-->>BE: Plan 详情
    BE->>DB: 3. 创建 Order (status: PENDING)
    DB-->>BE: 已创建的 Order
    BE->>Pay: 4. 调用支付SDK，请求预创建支付 (out_trade_no: orderNo)
    Pay-->>BE: 5. 返回支付二维码 URL
    BE-->>FE: 6. 返回 Order 和支付二维码 URL
    deactivate BE
    
    FE->>FE: 7. 展示二维码
    Note right of FE: 用户扫码支付

    Pay-->>BE: 8. POST /api/payment/notify/... (支付成功异步通知)
    activate BE
    BE->>Pay: 验证签名
    BE->>DB: 9. **开始数据库事务**
    activate DB
    BE->>DB: 10. 查询 Order (orderNo)，并加锁
    Note right of BE: **幂等性检查**: <br/>如果订单已是PAID，则直接返回成功
    BE->>DB: 11. 更新 Order (status: PAID, paidAt, gatewayId)
    BE->>DB: 12. 查询 User 的 Subscription
    alt 用户已有有效订阅 (续费/升级)
        BE->>DB: 13a. 更新 Subscription (延长 endDate)
    else 新购或订阅已过期
        BE->>DB: 13b. 创建/更新 Subscription (设为 ACTIVE, 设置 startDate, endDate)
    end
    DB-->>BE: **提交事务**
    deactivate DB
    BE-->>Pay: 14. 返回成功响应 (e.g., "success")
    deactivate BE
```

### 5.2. 核心逻辑

1.  **订单创建与支付发起 (`order.service.ts`)**:
    - 用户在前端选择套餐和支付方式后，请求 `POST /api/orders`。
    - 服务端创建一个状态为 `PENDING` 的订单，并生成唯一的 `orderNo`。
    - 调用 `payment.service.ts` 中的方法，与支付宝或微信支付的SDK交互，获取支付二维码的URL (`qrCode` 或 `code_url`)。
    - 将订单信息和二维码URL返回给前端展示。

2.  **支付成功回调处理 (`order.service.ts`)**:
    - 支付宝/微信支付在用户支付成功后，会向后端配置的 `notify_url` (`/api/payment/notify/...`) 发送一个异步POST请求。
    - 后端首先验证通知的合法性（签名校验）。
    - **核心逻辑在 `prisma.$transaction` 中执行，确保原子性**：
        a. 根据通知中的 `orderNo` 找到系统中的订单。
        b. **幂等性处理**：检查订单是否已经是 `PAID` 状态，如果是，则直接忽略，防止重复处理。
        c. 将订单状态更新为 `PAID`，并记录支付时间和支付网关的流水号。
        d. **更新用户订阅 (`Subscription`)**：
            - 如果用户已有 `ACTIVE` 的订阅，则在其 `endDate` 的基础上延长有效期。
            - 如果用户没有订阅或订阅已过期，则通过 `upsert` 创建一个新的 `ACTIVE` 订阅。

## 6. 数据库设计开发详解

数据库使用 **PostgreSQL**，通过 **Prisma ORM** 进行管理。`schema.prisma` 是数据结构的唯一真实来源 (Single Source of Truth)。

### 6.1. 表单详解

| 表名 (模型名) | 主要字段 | 描述 |
| --- | --- | --- |
| `User` | `id`, `email`, `passwordHash`, `nickname`, `isVerified` | 存储用户信息。`email`是唯一标识。`isVerified`标记邮箱是否验证。 |
| `Plan` | `id`, `name`, `priceMonthly`, `priceAnnually`, `features` | 定义会员套餐。`features`是JSON字段，可灵活定义套餐包含的权益。 |
| `Subscription` | `id`, `userId`, `planId`, `status`, `startDate`, `endDate`, `billingCycle` | 用户订阅表。`userId`唯一，确保一个用户只有一个订阅记录。`status`是关键字段 (ACTIVE, CANCELED等)。 |
| `Order` | `id`, `orderNo`, `userId`, `planId`, `amount`, `status`, `paymentMethod` | 交易订单表。`orderNo`是唯一的订单号。`status`记录订单状态 (PENDING, PAID等)。 |
| `Tool` | `id`, `name`, `description`, `inputSchema` | 定义可用的工具。`inputSchema`是JSON字段，用于前端动态生成工具的输入表单。 |
| `ToolRun` | `id`, `userId`, `toolId`, `inputPayload`, `status`, `resultLogUrl` | 记录用户每次运行工具的日志。 |
| `Feedback` | `id`, `userId`, `content`, `type`, `status` | 用户反馈表。 |
| `News` | `id`, `title`, `content`, `type` | 新闻和公告表。 |
| `AuditLog` | `id`, `action`, `userId`, `details` | 审计日志，用于记录关键操作。 |

### 6.2. 核心数据交互场景

- **用户注册与订阅**: `User` -> `Order` (PENDING) -> `Subscription` (PENDING_PAYMENT)
- **支付成功**: `Order` (PENDING -> PAID) -> `Subscription` (PENDING_PAYMENT -> ACTIVE)
- **用户续费**: `Order` (PAID) -> `Subscription` (`endDate`延长)
- **工具使用**: `User` -> `ToolRun` (记录输入、状态和结果)

## 7. API详解 & 路由详解

API前缀统一为 `/api`。

### 7.1. 认证路由 (`/api/auth`)

- `POST /register`: 注册新用户
- `GET /verify-email`: 验证邮箱
- `POST /login`: 用户登录
- `POST /logout`: 用户登出
- `POST /resend-verification`: 重发验证邮件
- `POST /request-password-reset`: 请求重置密码
- `POST /reset-password`: 提交新密码

### 7.2. 订单路由 (`/api/orders`) - (需认证)

- `POST /`: 创建新订单，并发起支付
- `GET /`: 获取当前用户的订单列表
- `GET /:orderNo/status`: 查询特定订单的状态

### 7.3. 支付通知路由 (`/api/payment`)

- `POST /notify/alipay`: 接收支付宝的异步回调
- `POST /notify/wechat`: 接收微信支付的异步回调

### 7.4. 其他路由 (部分)

- `/api/users`: 用户信息相关 (待实现)
- `/api/plans`: 获取会员计划列表 (待实现)
- `/api/subscriptions`: 用户订阅管理 (待实现)
- `/api/tools`: 工具相关 (待实现)

### 7.5. 前端路由

- `/`: 首页
- `/login`, `/register`: 登录/注册页
- `/membership`: 会员计划展示页
- `/profile`: 用户个人中心 (受保护)
- `/checkout`: 支付页 (受保护)
- `/email-verification-result`, `/reset-password`: 功能性页面

## 8. 后续开发优化建议

### 8.1. 安全性 (Security)

1.  **【高优先级】修复认证流程**:
    - **立即统一前后端认证机制**。推荐采纳后端已实现的、更安全的 `httpOnly` Cookie 方案。
    - **修改前端**：废弃 `localStorage` 存储Token，改为依赖浏览器自动发送Cookie。增加 `/api/auth/me` 端点以在页面加载时获取用户信息。

2.  **输入验证强化**:
    - 目前主要在Controller层做基础校验。应在 **Service层** 使用 `Zod` 对所有外部输入（包括API参数和支付回调）进行严格的Schema校验，确保数据类型和格式的正确性。

3.  **权限控制 (RBAC)**:
    - 当前JWT中未包含用户角色信息。应在 `User` 模型中增加 `role` 字段 (如 `USER`, `ADMIN`)。
    - 登录时将 `role` 加入JWT payload。
    - 创建一个 **权限校验中间件**，可以方便地应用于需要特定权限的API路由 (例如 `isAdmin` 中间件)。

4.  **安全头(Security Headers)**:
    - 使用 `helmet` 等Express中间件，自动设置 `X-Content-Type-Options`, `Strict-Transport-Security`, `X-Frame-Options` 等HTTP头，增加应用安全性。

5.  **依赖库审计**:
    - 定期运行 `npm audit` 或使用Snyk等工具扫描项目依赖，及时发现并修复已知的安全漏洞。

### 8.2. 高性能 (High Performance)

1.  **数据库查询优化**:
    - **N+1问题**: 在获取列表（如订单列表）并需要关联数据时，注意使用Prisma的 `include` 或 `select` 来避免循环查询。
    - **索引**: `schema.prisma` 中已对 `id` 和 `unique` 字段自动创建索引。对于频繁用于 `where`、`orderBy` 的字段（如 `Order.userId`, `Subscription.status`），应手动添加 `@index` 来提升查询性能。

2.  **缓存策略**:
    - **Redis** 目前仅用于一次性令牌。可扩展其用途：
        - **缓存热点数据**: 对于不常变化的`Plan`信息、新闻公告等，可以在Service层增加缓存逻辑，减少数据库读取。
        - **会话管理**: 如果未来用户会话信息变复杂，可以考虑使用 `connect-redis` 将Express session存入Redis。

3.  **前端性能**:
    - **代码分割**: `Vite` 和 `React Router` 支持基于路由的懒加载。将每个页面组件用 `React.lazy` 和 `Suspense` 包裹，实现按需加载，减小初始包体积。
    - **图片优化**: 使用现代图片格式 (如 WebP)，并对图片进行压缩。
    - **减少重渲染**: 使用 `React.memo` 包裹不常变化的组件，合理使用 `useCallback` 和 `useMemo` 来避免不必要的函数创建和计算。

### 8.3. 可扩展性 (Scalability)

1.  **配置文件集中化**:
    - 在后端 `src/config` 目录中，将所有环境变量和固定配置（如JWT有效期、支付回调地址等）进行结构化管理和导出，避免在代码中出现硬编码的字符串或魔法数字。

2.  **Service层抽象**:
    - 当前Service层职责清晰。未来如果业务逻辑变得更复杂（例如增加优惠券、退款等功能），应考虑将 `OrderService` 进一步拆分为更小的、单一职责的服务。

3.  **异步任务队列**:
    - 发送邮件、生成报告等非核心且耗时的操作，不应阻塞API响应。
    - `toolWorker.ts` 的存在表明已考虑后台任务。可以引入一个正式的消息队列 (如 BullMQ, RabbitMQ)，将这类任务作为Job推送到队列中，由Worker进程在后台异步处理。

4.  **前端组件化**:
    - `shadcn/ui` 提供了很好的基础。应继续将业务逻辑封装到自定义的复合组件和自定义Hook (`use...`) 中，提高代码的复用性和可维护性。例如，可以创建一个 `useUserOrders` 的Hook来封装获取用户订单的逻辑和状态。

5.  **类型共享**:
    - 为了保证前后端数据类型的一致性，可以考虑创建一个共享的 `types` 包/工作区，存放Prisma生成的类型或自定义的Zod Schema，供前后端共同引用。 