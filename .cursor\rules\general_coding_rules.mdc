---
description: 
globs: 
alwaysApply: true
---
# 通用编码规则

## 代码风格与一致性

1. **命名规范**：
   - 使用有意义的、描述性的名称
   - 变量和函数使用驼峰命名法（camelCase）
   - 类名和组件使用帕斯卡命名法（PascalCase）
   - 常量使用大写蛇形命名法（UPPER_SNAKE_CASE）
   - 避免使用单字母变量名（除非是循环计数器）

2. **缩进与格式**：
   - 使用2个空格作为缩进
   - 代码块使用花括号{}，并在新行开始
   - 行长度不应超过100个字符
   - 函数调用的参数过多时应当换行并对齐

3. **注释规范**：
   - 为复杂的代码块添加注释
   - 使用JSDoc风格的注释来记录函数、参数和返回值
   - 避免无意义的注释，代码应当尽可能自解释
   - 删除被注释掉的代码，不要在代码库中保留

4. **代码组织**：
   - 相关的功能应当放在同一个文件或目录中
   - 每个文件的责任应当单一且明确
   - 导入顺序：第三方库 > 项目内部模块 > 相对路径的模块

## 架构与设计原则

1. **SOLID原则**：
   - 单一职责原则（Single Responsibility）
   - 开闭原则（Open/Closed）
   - 里氏替换原则（Liskov Substitution）
   - 接口隔离原则（Interface Segregation）
   - 依赖反转原则（Dependency Inversion）

2. **DRY原则**：
   - 不要重复自己（Don't Repeat Yourself）
   - 提取重复的代码为可复用的函数或组件

3. **KISS原则**：
   - 保持简单与直接（Keep It Simple, Stupid）
   - 避免过度工程化和不必要的复杂性

4. **关注点分离**：
   - UI逻辑、业务逻辑和数据访问应当清晰分离
   - 使用适当的设计模式来管理复杂性

## 性能与优化

1. **资源管理**：
   - 妥善管理内存，避免内存泄漏
   - 及时释放不再需要的资源
   - 避免不必要的重复计算和渲染

2. **异步处理**：
   - 合理使用异步操作，避免阻塞主线程
   - 正确处理Promise和异步函数的错误情况
   - 考虑使用批处理和节流/防抖技术

3. **加载优化**：
   - 实现懒加载和代码分割
   - 优化首屏加载时间
   - 减少不必要的网络请求

## 安全考量

1. **数据验证**：
   - 在前端和后端都进行输入验证
   - 永远不要信任用户输入
   - 使用参数化查询防止SQL注入

2. **认证与授权**：
   - 正确实现用户认证机制
   - 检查用户权限访问受保护的资源
   - 使用HTTPS保护数据传输

3. **敏感数据处理**：
   - 不在客户端存储敏感信息
   - 使用安全的加密方法保护数据
   - 遵循最小权限原则

## 测试与可维护性

1. **编写测试**：
   - 为关键业务逻辑编写单元测试
   - 进行集成测试和端到端测试
   - 测试应该易于运行和维护

2. **错误处理**：
   - 全面处理异常和错误情况
   - 提供有用的错误消息
   - 记录错误以便后续分析

3. **版本控制**：
   - 编写有意义的提交消息
   - 保持较小的提交规模
   - 使用分支策略管理功能开发和修复