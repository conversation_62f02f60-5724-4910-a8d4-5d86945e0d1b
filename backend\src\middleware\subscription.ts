import { Response, NextFunction, Request } from 'express';
import { prisma } from '../utils/database';

/**
 * Middleware to check if a user has permission to execute a task based on their subscription plan.
 */
export const checkTaskExecutionPermission = async (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required.' });
  }

  const userId = req.user?.id;
  if (!userId) {
    return res.status(401).json({ message: 'User not authenticated.' });
  }

  try {
    // 1. Find the user's active subscription and plan details
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: userId,
        status: 'ACTIVE',
        endDate: {
          gt: new Date(), // Ensure subscription is not expired
        },
      },
      include: {
        plan: true, // Include the plan details
      },
    });

    // If no active subscription, deny access (or handle free plan logic)
    if (!subscription) {
      return res.status(403).json({ message: 'No active subscription found. Please subscribe to a plan to run tasks.' });
    }

    // 2. Get the execution limit from the plan's features
    // We expect features to be a JSON object like: { "taskRunsPerDay": 10 }
    const features = subscription.plan.features as { taskRunsPerDay?: number };
    const dailyLimit = features.taskRunsPerDay;

    // If dailyLimit is not defined or is null/0, it means unlimited access.
    if (dailyLimit === null || dailyLimit === undefined) {
      return next(); // Unlimited access, proceed to the next middleware
    }
    
    if (dailyLimit <= 0) {
      return res.status(403).json({ message: 'Your current plan does not allow task execution.' });
    }

    // 3. Count tasks created by the user in the last 24 hours
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const tasksToday = await prisma.task.count({
      where: {
        userId: userId,
        createdAt: {
          gte: twentyFourHoursAgo,
        },
      },
    });

    // 4. Compare usage against the limit
    if (tasksToday >= dailyLimit) {
      return res.status(403).json({ 
        message: `You have reached your daily limit of ${dailyLimit} tasks. Please upgrade your plan or wait until tomorrow.` 
      });
    }

    // If all checks pass, proceed to the actual route handler
    next();
  } catch (error) {
    console.error('Error in subscription check middleware:', error);
    res.status(500).json({ message: 'Internal server error during permission check.' });
  }
}; 