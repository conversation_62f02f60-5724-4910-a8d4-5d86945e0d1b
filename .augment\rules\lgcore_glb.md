---
type: "always_apply"
---

# 编码交互规约 (Coding Convention)

## 核心编程指令 (Core Programming Directives)

**核心思想是精准、系统、高效。**

### 1. 任务启动与理解 (Initiation & Comprehension)

- 前置分析 (Prerequisite Analysis): 在开始编码前，必须完成以下步骤：
	
	1. 需求对齐: 彻底审查需求文档（PRD）和设计规范，完全理解业务目标、真实应用场景和约束条件。
	
	2. 代码审查: 系统性地分析项目最新版本的相关代码，深入理解现有架构和业务逻辑。

- 沟通协议 (Communication Protocol):
	
	1. 语言: 所有沟通均使用中文。
	
	2. 歧义处理: 如果对需求、现有逻辑或任务影响范围存在任何不确定性，必须立即停止并向您请求澄清。严禁基于不完整的理解进行假设或继续开发。

### 2. 设计与执行 (Design & Execution)

- 解决方案设计 (Solution Design):

	1. 精准性: 解决方案必须精准地解决指定问题。对于复杂问题，需采用第一性原理进行根本原因分析。
	
	2. 代码质量: 方案设计必须兼顾代码的可扩展性、可维护性和性能。

- 编码准则 (Coding Principles):

	1. 遵循真实生产应用场景原则：必须始终理解并满足真实生产应用场景的业务功能逻辑要求，绝对不能随意盲目简化或精简业务逻辑代码，也不要为了解决技术问题，而引入跟业务逻辑无关的或者无效的代码，比如大量临时测试、模拟验证和冗余等方面代码；
	
	2. 最小化影响: 只修改完成任务所必需的代码。严禁修改不相关的代码、破坏现有功能或引入冗余逻辑。
	
	3. 逻辑一致性: 任何修改都必须确保前端与后端业务逻辑的同步和一致。
	
	4. 遵循标准: 集成第三方服务（SDK/API）时，必须查阅其最新官方文档，并结合项目技术栈进行正确集成。

### 3. 验证与报告 (Verification & Reporting)

- 强制验证 (Mandatory Verification):
	
	1. 全面自检: 代码修改完成后，必须进行全面、系统的功能和逻辑验证。
	
	2. 正确性保证: 确保修改不仅彻底解决了原有问题，并且未引入任何新的功能、逻辑或性能上的缺陷。

- 状态报告 (Status Reporting):
	
	1. 客观透明: 必须客观、真实地报告任务的完成状态。明确指出已完成的工作、遇到的问题或任何潜在风险。杜绝想当然地认为任务已正确完成。
