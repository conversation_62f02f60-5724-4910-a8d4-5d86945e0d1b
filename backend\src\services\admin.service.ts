import { prisma } from '../utils/database';
import { Role, TaskStatus, OrderStatus, SubscriptionStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

// Dashboard Statistics
export const getDashboardStatistics = async () => {
  const [
    totalUsers,
    totalTasks,
    totalOrders,
    totalRevenue,
    activeSubscriptions,
    todayTasks,
    todayOrders,
    todayRevenue,
  ] = await Promise.all([
    // Total counts
    prisma.user.count(),
    prisma.task.count(),
    prisma.order.count(),
    prisma.order.aggregate({
      where: { status: OrderStatus.PAID },
      _sum: { amount: true },
    }),
    prisma.subscription.count({
      where: { status: SubscriptionStatus.ACTIVE },
    }),
    
    // Today's statistics
    prisma.task.count({
      where: {
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0)),
        },
      },
    }),
    prisma.order.count({
      where: {
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0)),
        },
      },
    }),
    prisma.order.aggregate({
      where: {
        status: OrderStatus.PAID,
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0)),
        },
      },
      _sum: { amount: true },
    }),
  ]);

  // Task status distribution
  const tasksByStatus = await prisma.task.groupBy({
    by: ['status'],
    _count: { status: true },
  });

  // Order status distribution
  const ordersByStatus = await prisma.order.groupBy({
    by: ['status'],
    _count: { status: true },
  });

  // Recent activities (last 10 tasks and orders)
  const recentTasks = await prisma.task.findMany({
    take: 10,
    orderBy: { createdAt: 'desc' },
    include: {
      user: { select: { id: true, email: true, name: true } },
      tool: { select: { id: true, name: true } },
    },
  });

  const recentOrders = await prisma.order.findMany({
    take: 10,
    orderBy: { createdAt: 'desc' },
    include: {
      user: { select: { id: true, email: true, name: true } },
      plan: { select: { id: true, name: true } },
    },
  });

  return {
    overview: {
      totalUsers,
      totalTasks,
      totalOrders,
      totalRevenue: totalRevenue._sum.amount || 0,
      activeSubscriptions,
    },
    today: {
      tasks: todayTasks,
      orders: todayOrders,
      revenue: todayRevenue._sum.amount || 0,
    },
    distribution: {
      tasksByStatus: tasksByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>),
      ordersByStatus: ordersByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>),
    },
    recentActivities: {
      tasks: recentTasks,
      orders: recentOrders,
    },
  };
};

// Users Management
interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: Role;
}

export const getUsers = async (params: PaginationParams) => {
  const { page = 1, limit = 10, search, role } = params;
  const skip = (page - 1) * limit;

  const where: any = {};
  
  if (search) {
    where.OR = [
      { email: { contains: search, mode: 'insensitive' } },
      { name: { contains: search, mode: 'insensitive' } },
    ];
  }
  
  if (role) {
    where.role = role;
  }

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        role: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            tasks: true,
            orders: true,
          },
        },
      },
    }),
    prisma.user.count({ where }),
  ]);

  return {
    users,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    },
  };
};

export const createUser = async (
  data: {
    email: string;
    password: string;
    name?: string;
    role: Role;
  },
  adminId: string
) => {
  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: data.email },
  });

  if (existingUser) {
    throw new Error('User with this email already exists');
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(data.password, 12);

  // Create user
  const user = await prisma.user.create({
    data: {
      ...data,
      password: hashedPassword,
      isVerified: true, // Admin-created users are auto-verified
    },
    select: {
      id: true,
      email: true,
      name: true,
      avatar: true,
      role: true,
      isVerified: true,
      createdAt: true,
    },
  });

  // Create audit log
  await createAuditLog(adminId, 'createUser', user.id, {
    email: data.email,
    role: data.role,
  });

  return user;
};

export const getUserById = async (userId: string) => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      email: true,
      name: true,
      avatar: true,
      role: true,
      isVerified: true,
      createdAt: true,
      updatedAt: true,
      subscription: {
        select: {
          id: true,
          status: true,
          startDate: true,
          endDate: true,
          plan: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      _count: {
        select: {
          tasks: true,
          orders: true,
        },
      },
    },
  });

  return user;
};

export const updateUser = async (
  userId: string,
  data: {
    name?: string;
    role?: Role;
    isVerified?: boolean;
  },
  adminId: string
) => {
  // Check if user exists
  const existingUser = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, role: true, isVerified: true },
  });

  if (!existingUser) {
    throw new Error('User not found');
  }

  // Update user
  const user = await prisma.user.update({
    where: { id: userId },
    data,
    select: {
      id: true,
      email: true,
      name: true,
      avatar: true,
      role: true,
      isVerified: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  // Create audit log
  await createAuditLog(adminId, 'updateUser', userId, {
    changes: data,
    previous: {
      role: existingUser.role,
      isVerified: existingUser.isVerified,
    },
  });

  return user;
};

export const deleteUser = async (userId: string, adminId: string) => {
  // Prevent admin from deleting themselves
  if (userId === adminId) {
    throw new Error('You cannot delete yourself');
  }

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, email: true },
  });

  if (!user) {
    throw new Error('User not found');
  }

  // Delete user (this will cascade delete related records due to foreign key constraints)
  await prisma.user.delete({
    where: { id: userId },
  });

  // Create audit log
  await createAuditLog(adminId, 'deleteUser', userId, {
    email: user.email,
  });
};

// Tasks Management
export const getTasks = async (params: PaginationParams) => {
  const { page = 1, limit = 10, search } = params;
  const skip = (page - 1) * limit;

  const where: any = {};
  
  if (search) {
    where.OR = [
      { user: { email: { contains: search, mode: 'insensitive' } } },
      { tool: { name: { contains: search, mode: 'insensitive' } } },
    ];
  }

  const [tasks, total] = await Promise.all([
    prisma.task.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: { select: { id: true, email: true, name: true } },
        tool: { select: { id: true, name: true } },
      },
    }),
    prisma.task.count({ where }),
  ]);

  return {
    tasks,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    },
  };
};

export const getTaskById = async (taskId: string) => {
  const task = await prisma.task.findUnique({
    where: { id: taskId },
    include: {
      user: { 
        select: { 
          id: true, 
          email: true, 
          name: true,
          subscription: {
            select: {
              id: true,
              status: true,
              plan: { select: { name: true } },
            },
          },
        } 
      },
      tool: { 
        select: { 
          id: true, 
          name: true, 
          description: true,
          inputSchema: true,
          dockerImage: true,
          version: true,
        } 
      },
    },
  });

  return task;
};

// Orders Management
export const getOrders = async (params: PaginationParams) => {
  const { page = 1, limit = 10, search } = params;
  const skip = (page - 1) * limit;

  const where: any = {};
  
  if (search) {
    where.OR = [
      { user: { email: { contains: search, mode: 'insensitive' } } },
      { plan: { name: { contains: search, mode: 'insensitive' } } },
    ];
  }

  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: { select: { id: true, email: true, name: true } },
        plan: { select: { id: true, name: true } },
      },
    }),
    prisma.order.count({ where }),
  ]);

  return {
    orders,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    },
  };
};

export const getOrderById = async (orderId: string) => {
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    include: {
      user: { 
        select: { 
          id: true, 
          email: true, 
          name: true,
        } 
      },
      plan: { 
        select: { 
          id: true, 
          name: true, 
          description: true,
          priceMonth: true,
          priceYear: true,
          features: true,
        } 
      },
      subscription: {
        select: {
          id: true,
          status: true,
          startDate: true,
          endDate: true,
        },
      },
    },
  });

  return order;
};

// Subscriptions Management
export const getSubscriptions = async (params: PaginationParams) => {
  const { page = 1, limit = 10, search } = params;
  const skip = (page - 1) * limit;

  const where: any = {};
  
  if (search) {
    where.OR = [
      { user: { email: { contains: search, mode: 'insensitive' } } },
      { plan: { name: { contains: search, mode: 'insensitive' } } },
    ];
  }

  const [subscriptions, total] = await Promise.all([
    prisma.subscription.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: { select: { id: true, email: true, name: true } },
        plan: { select: { id: true, name: true } },
        order: { select: { id: true, amount: true, paymentMethod: true } },
      },
    }),
    prisma.subscription.count({ where }),
  ]);

  return {
    subscriptions,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    },
  };
};

export const updateSubscription = async (
  subscriptionId: string,
  data: {
    status?: SubscriptionStatus;
    endDate?: string;
  },
  adminId: string
) => {
  // Check if subscription exists
  const existingSubscription = await prisma.subscription.findUnique({
    where: { id: subscriptionId },
    select: { 
      id: true, 
      status: true, 
      endDate: true,
      user: { select: { email: true } },
    },
  });

  if (!existingSubscription) {
    throw new Error('Subscription not found');
  }

  const updateData: any = {};
  if (data.status) updateData.status = data.status;
  if (data.endDate) updateData.endDate = new Date(data.endDate);

  // Update subscription
  const subscription = await prisma.subscription.update({
    where: { id: subscriptionId },
    data: updateData,
    include: {
      user: { select: { id: true, email: true, name: true } },
      plan: { select: { id: true, name: true } },
      order: { select: { id: true, amount: true } },
    },
  });

  // Create audit log
  await createAuditLog(adminId, 'updateSubscription', subscriptionId, {
    changes: data,
    userEmail: existingSubscription.user.email,
    previous: {
      status: existingSubscription.status,
      endDate: existingSubscription.endDate,
    },
  });

  return subscription;
};

// Plans Management
export const getPlans = async () => {
  const plans = await prisma.plan.findMany({
    orderBy: { createdAt: 'desc' },
    include: {
      _count: {
        select: {
          subscriptions: true,
          orders: true,
        },
      },
    },
  });

  return plans;
};

export const createPlan = async (
  data: {
    name: string;
    description?: string;
    priceMonth: number;
    priceYear: number;
    features: Record<string, any>;
  },
  adminId: string
) => {
  // Check if plan name already exists
  const existingPlan = await prisma.plan.findUnique({
    where: { name: data.name },
  });

  if (existingPlan) {
    throw new Error('Plan with this name already exists');
  }

  // Create plan
  const plan = await prisma.plan.create({
    data,
  });

  // Create audit log
  await createAuditLog(adminId, 'createPlan', plan.id, {
    name: data.name,
    priceMonth: data.priceMonth,
    priceYear: data.priceYear,
  });

  return plan;
};

export const updatePlan = async (
  planId: string,
  data: {
    name?: string;
    description?: string;
    priceMonth?: number;
    priceYear?: number;
    features?: Record<string, any>;
  },
  adminId: string
) => {
  // Check if plan exists
  const existingPlan = await prisma.plan.findUnique({
    where: { id: planId },
    select: { id: true, name: true, priceMonth: true, priceYear: true },
  });

  if (!existingPlan) {
    throw new Error('Plan not found');
  }

  // Check if name is being changed and already exists
  if (data.name && data.name !== existingPlan.name) {
    const nameExists = await prisma.plan.findUnique({
      where: { name: data.name },
    });
    if (nameExists) {
      throw new Error('Plan with this name already exists');
    }
  }

  // Update plan
  const plan = await prisma.plan.update({
    where: { id: planId },
    data,
  });

  // Create audit log
  await createAuditLog(adminId, 'updatePlan', planId, {
    changes: data,
    previous: {
      name: existingPlan.name,
      priceMonth: existingPlan.priceMonth,
      priceYear: existingPlan.priceYear,
    },
  });

  return plan;
};

export const deletePlan = async (planId: string, adminId: string) => {
  // Check if plan exists
  const plan = await prisma.plan.findUnique({
    where: { id: planId },
    select: { 
      id: true, 
      name: true,
      _count: {
        select: {
          subscriptions: {
            where: { status: SubscriptionStatus.ACTIVE },
          },
        },
      },
    },
  });

  if (!plan) {
    throw new Error('Plan not found');
  }

  // Check if plan has active subscriptions
  if (plan._count.subscriptions > 0) {
    throw new Error('Cannot delete plan that has active subscriptions');
  }

  // Delete plan
  await prisma.plan.delete({
    where: { id: planId },
  });

  // Create audit log
  await createAuditLog(adminId, 'deletePlan', planId, {
    name: plan.name,
  });
};

// Tools Management
export const getTools = async () => {
  const tools = await prisma.tool.findMany({
    orderBy: { createdAt: 'desc' },
    include: {
      _count: {
        select: {
          tasks: true,
        },
      },
    },
  });

  return tools;
};

export const createTool = async (
  data: {
    name: string;
    description: string;
    inputSchema: Record<string, any>;
    dockerImage: string;
    version: string;
    configTemplate?: Record<string, any>;
    isPublic: boolean;
  },
  adminId: string
) => {
  // Check if tool name already exists
  const existingTool = await prisma.tool.findUnique({
    where: { name: data.name },
  });

  if (existingTool) {
    throw new Error('Tool with this name already exists');
  }

  // Create tool
  const tool = await prisma.tool.create({
    data,
  });

  // Create audit log
  await createAuditLog(adminId, 'createTool', tool.id, {
    name: data.name,
    dockerImage: data.dockerImage,
    version: data.version,
  });

  return tool;
};

export const getToolById = async (toolId: string) => {
  const tool = await prisma.tool.findUnique({
    where: { id: toolId },
    include: {
      _count: {
        select: {
          tasks: true,
        },
      },
    },
  });

  return tool;
};

export const updateTool = async (
  toolId: string,
  data: {
    name?: string;
    description?: string;
    inputSchema?: Record<string, any>;
    dockerImage?: string;
    version?: string;
    configTemplate?: Record<string, any>;
    isPublic?: boolean;
  },
  adminId: string
) => {
  // Check if tool exists
  const existingTool = await prisma.tool.findUnique({
    where: { id: toolId },
    select: { id: true, name: true, version: true, dockerImage: true },
  });

  if (!existingTool) {
    throw new Error('Tool not found');
  }

  // Check if name is being changed and already exists
  if (data.name && data.name !== existingTool.name) {
    const nameExists = await prisma.tool.findUnique({
      where: { name: data.name },
    });
    if (nameExists) {
      throw new Error('Tool with this name already exists');
    }
  }

  // Update tool
  const tool = await prisma.tool.update({
    where: { id: toolId },
    data,
  });

  // Create audit log
  await createAuditLog(adminId, 'updateTool', toolId, {
    changes: data,
    previous: {
      name: existingTool.name,
      version: existingTool.version,
      dockerImage: existingTool.dockerImage,
    },
  });

  return tool;
};

export const deleteTool = async (toolId: string, adminId: string) => {
  // Check if tool exists
  const tool = await prisma.tool.findUnique({
    where: { id: toolId },
    select: { 
      id: true, 
      name: true,
      _count: {
        select: {
          tasks: true,
        },
      },
    },
  });

  if (!tool) {
    throw new Error('Tool not found');
  }

  // Check if tool has associated tasks
  if (tool._count.tasks > 0) {
    throw new Error('Cannot delete tool that has associated tasks');
  }

  // Delete tool
  await prisma.tool.delete({
    where: { id: toolId },
  });

  // Create audit log
  await createAuditLog(adminId, 'deleteTool', toolId, {
    name: tool.name,
  });
};

// Audit Log Helper
const createAuditLog = async (
  actorId: string,
  action: string,
  targetId: string,
  details?: Record<string, any>
) => {
  await prisma.auditLog.create({
    data: {
      actorId,
      action,
      targetId,
      details: details || {},
    },
  });
}; 