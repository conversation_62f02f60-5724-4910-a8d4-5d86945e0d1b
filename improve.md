# 系统改进与优化计划 (improve.md)

## 1. 概述

经过对当前代码库的全面系统审查，我们发现系统在核心功能（如认证、支付）方面有扎实的实现基础，但在安全性、性能、代码健壮性和功能完整性方面存在一系列问题。本文件旨在详细列出所有已发现的问题，并为每个问题提供精确的修复计划和优化建议，以指导下一阶段的开发工作。

问题按优先级分为四个等级：P1 (严重)、P2 (高)、P3 (中)、P4 (低)。

---

## 2. P1 - 严重问题 (必须立即修复)

### Issue #13: 微信支付回调未验证签名 (严重安全漏洞)

- **问题描述**: `wechat.controller.ts` 中的 `handleWechatNotification` 函数在处理微信支付成功通知时，直接解码并信任了请求内容，没有验证微信官方的签名。这允许攻击者伪造支付通知，从而免费获得或延长付费订阅服务。
- **风险**: 直接导致业务收入损失，破坏商业模式的公平性。
- **修复计划**:
    1.  在 `wechat.controller.ts` 中，必须在调用 `wechatPayApi.decodeResource` 之前，使用微信支付SDK提供的签名验证功能。
    2.  签名验证需要从请求头获取 `Wechatpay-Signature`, `Wechatpay-Timestamp`, `Wechatpay-Nonce`, `Wechatpay-Serial`，并结合原始的 `request.body` (当前已通过 `raw` 中间件正确获取)。
    3.  调用类似于 `wechatPayApi.verifySign({ signature, timestamp, nonce, body, serial })` 的方法。
    4.  只有在签名验证成功后，才能继续执行后续的业务逻辑。如果验证失败，应立即返回错误响应并记录安全警告。

### Issue #14: 工具执行未使用沙箱 (严重安全漏洞)

- **问题描述**: `toolWorker.ts` 中的工具执行逻辑是模拟的，并未按设计文档要求在隔离的Docker容器中运行。当前的架构如果直接集成真实的Python脚本，将在主进程空间内执行，对服务器构成巨大威胁。
- **风险**: 一个恶意的或有漏洞的工具脚本可能读取服务器敏感文件（如 `.env`）、访问数据库、发起网络攻击（反向shell），导致服务器被完全控制。
- **修复计划**:
    1.  重构 `toolWorker.ts` 中的 `runTool` 方法。
    2.  对于每种工具，创建一个对应的 `Dockerfile`，该文件定义了一个包含Python环境和所需依赖的最小化、低权限的执行环境。
    3.  `runTool` 方法的核心逻辑应改为使用 `child_process.spawn` 调用 `docker run` 命令。
    4.  `docker run` 命令必须遵循安全最佳实践：
        -   使用 `--rm` 自动清理容器。
        -   使用 `--network none` 禁用网络访问（除非工具确实需要）。
        -   使用 `--read-only` 将容器文件系统设为只读。
        -   通过 `-v` 将一个临时的、唯一的输入/输出目录挂载到容器中。
        -   设置资源限制，如 `--memory` 和 `--cpus`。
        -   在容器内使用非root用户运行。

---

## 3. P2 - 高危问题 (强烈建议修复)

### Issue #1: JWT_SECRET 使用硬编码的默认值

- **问题描述**: `.env` 文件中的 `JWT_SECRET` 是一个公开的、不安全的默认字符串。
- **风险**: 在生产环境中，攻击者可以轻易猜到或找到这个密钥，从而伪造任意用户的登录令牌。
- **修复计划**:
    1.  在生产部署流程中，必须通过环境变量注入一个长而随机的、不可预测的密钥。
    2.  严禁将生产密钥提交到版本控制中。在文档中明确指出这一点。

### Issue #10: JWT 存储在 localStorage (XSS 风险)

- **问题描述**: 前端将JWT存储在`localStorage`，违反了`online_req.md`中使用`httpOnly` Cookie的安全性要求。
- **风险**: 如果应用存在任何XSS漏洞，攻击者可以窃取`localStorage`中的JWT，冒充用户身份。
- **修复计划**:
    1.  **后端修改**:
        -   修改登录接口 (`loginController`)，不再在响应体中返回token。
        -   改为通过设置 `httpOnly`, `secure` (在生产环境), `sameSite: 'strict'` 的cookie来发送JWT。
    2.  **前端修改**:
        -   移除所有`localStorage`中读写token的逻辑。
        -   配置API客户端（如axios）的`withCredentials: true`，使其在跨域请求中自动携带cookie。
        -   `logout` 逻辑需要调用一个后端的 `/api/auth/logout` 接口，该接口负责清除对应的cookie。

### Issue #15: 缺少对工具输入的严格清理

- **问题描述**: 在将`inputPayload`传递给工具执行逻辑之前，缺少一个显式的清理（Sanitization）步骤。
- **风险**: 当集成真实工具脚本后，如果输入被用作命令行参数或写入文件，可能导致命令注入攻击。
- **修复计划**:
    1.  在 `toolWorker.ts` 的 `executeTask` 方法中，在调用 `runTool` 之前，增加一个输入清理阶段。
    2.  根据每个工具的 `inputSchema`，对 `inputPayload` 中的每个字段进行严格的类型和格式验证（可以使用`zod`）。
    3.  对所有字符串类型的输入，剥离或转义任何可能对shell或文件系统有特殊含义的字符（如 `|`, `&`, `;`, `$`, `>`, `<` 等）。

### Issue #6: 缺少全局错误处理器

- **问题描述**: Express应用没有注册一个统一的错误处理中间件。
- **风险**: 导致错误响应格式不统一，且在开发模式下可能泄露堆栈跟踪等敏感信息。
- **修复计划**:
    1.  在 `backend/src/index.ts` 的所有路由注册之后，添加一个全局错误处理中间件。
    2.  该中间件应接收4个参数 `(err, req, res, next)`。
    3.  在中间件内部，根据错误类型（如`ZodError`, 自定义`ApiError`等）或`statusCode`属性，返回统一格式的JSON错误响应。
    4.  集成一个日志库（如`winston`），在此处记录所有服务器错误。

### Issue #11 & #12: Redis与Prisma客户端管理不当

- **问题描述**: `redisClient`在每个服务函数中重复连接；`PrismaClient`可能在多个服务文件中被单独实例化。
- **风险**: 严重影响性能，可能耗尽服务器或数据库的连接数。
- **修复计划**:
    1.  **Prisma**: 创建一个单例的Prisma客户端。在 `backend/src/utils/database.ts` 中，导出一个共享的 `prisma` 实例，并在所有服务文件中导入和使用这个共享实例。
    2.  **Redis**: 创建一个单例的Redis客户端。在 `backend/src/config/redis.ts` 中，初始化并导出客户端实例。在 `backend/src/index.ts` 应用启动时，调用一次 `redisClient.connect()`。移除所有服务函数内部的连接逻辑。

---

## 4. P3 - 中等问题 (建议修复)

### Issue #3: CORS 策略过于宽松

- **问题描述**: `cors()`中间件没有配置，允许所有来源的跨域请求。
- **修复计划**: 在 `backend/src/index.ts` 中，为生产环境配置CORS：
  ```javascript
  app.use(cors({
    origin: process.env.FRONTEND_URL, // 只允许前端域名访问
    credentials: true // 如果使用cookie或session
  }));
  ```

### Issue #2: AuditLog 中的 userId 可选

- **问题描述**: `AuditLog`模型中的`userId`字段是可选的。
- **修复计划**:
    1.  创建一个 `logAudit` 的服务函数。
    2.  对于需要认证的路由，中间件应确保`req.user`存在。
    3.  在这些路由的业务逻辑中调用 `logAudit` 时，必须传入 `userId`。

### Issue #8: JWT解码时使用 `as any`

- **问题描述**: 在 `middleware/auth.ts` 中，`jwt.verify` 的结果被断言为 `any`。
- **修复计划**:
    1.  创建一个 `JwtPayload` 接口，定义JWT中包含的数据结构（如`userId`, `email`, `role`, `iat`, `exp`）。
    2.  将 `jwt.verify` 的结果断言为 `JwtPayload`。

### Issue #5: 核心功能API缺失

- **问题描述**: `Tools`, `Feedback`, `News` 等模块的API路由未实现。
- **修复计划**:
    1.  创建 `tool.routes.ts`, `feedback.routes.ts`, `news.routes.ts`。
    2.  为这些路由创建对应的控制器和服务，实现基本的CRUD逻辑。
    3.  在 `backend/src/index.ts` 中注册这些新路由。

---

## 5. P4 - 低危问题 (代码质量与优化)

### Issue #7: 存在重复/未使用的认证文件

- **问题描述**: `auth.routes.ts` vs `auth.ts`, `auth.middleware.ts` vs `auth.ts`。
- **修复计划**:
    1.  确定当前正在使用的文件（根据 `index.ts` 的导入）。
    2.  仔细比对后，将任何需要保留的逻辑合并到正在使用的文件中。
    3.  安全地删除未使用的旧文件，清理项目结构。

### Issue #9: JWT中缺少用户角色

- **问题描述**: JWT中未包含用户角色，导致授权检查时需要额外查询数据库。
- **修复计划**:
    1.  在`auth.service.ts`的`loginUser`函数中，生成JWT时，将用户的`plan`或`role`信息也包含在payload中。
    2.  在`middleware/auth.ts`中，当验证JWT后，可以直接从解码后的payload中获取角色信息，附加到`req.user`上，避免二次查询数据库。
    3.  **注意**: 这是一种性能优化。当前实现（每次都查库）更安全，因为它总能获取最新角色。团队需要权衡决定是否采纳此项优化。 