/**
 * SDC阶段1和阶段2功能测试程序
 * 独立测试程序，与生产代码分离
 * 
 * 测试内容：
 * 1. Template文件下载功能
 * 2. 文件上传验证
 * 3. 权限检查逻辑
 * 4. 页面组件渲染
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    BACKEND_URL: 'http://localhost:8080',
    FRONTEND_URL: 'http://localhost:3000',
    TEST_FILES_DIR: './test_files',
    TEMPLATE_FILES: ['hier.yaml', 'vlog.v', 'dcont.xlsx']
};

// 创建测试文件目录
function setupTestEnvironment() {
    if (!fs.existsSync(TEST_CONFIG.TEST_FILES_DIR)) {
        fs.mkdirSync(TEST_CONFIG.TEST_FILES_DIR, { recursive: true });
    }
    
    // 创建测试用的SDC文件
    const testFiles = {
        'test_hier.yaml': `# Test hier.yaml file
pwr:
    VDD_CORE: TT0P750V TT0P700V TT0P650V

hier:
  test_module:
    hdlevel: top
    alias: TESTMOD
    prime_pwr: VDD_CORE
    intg_nlb: net
    insts:
    mac_insts:
    dig_insts:
    constr_dir: /test/path/
`,
        'test_vlog.v': `// Test Verilog file
module test_module(
    input wire clk,
    input wire rst_n,
    input wire [7:0] data_in,
    output reg [7:0] data_out
);

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        data_out <= 8'h00;
    end else begin
        data_out <= data_in;
    end
end

endmodule
`,
        'test_dcont.xlsx': 'Binary Excel file content placeholder' // 实际应该是二进制内容
    };
    
    Object.entries(testFiles).forEach(([filename, content]) => {
        const filePath = path.join(TEST_CONFIG.TEST_FILES_DIR, filename);
        fs.writeFileSync(filePath, content);
    });
    
    console.log('✅ Test environment setup complete');
}

// 测试1: Template文件下载功能
async function testTemplateDownload() {
    console.log('\n🧪 Testing Template Download Functionality...');
    
    for (const filename of TEST_CONFIG.TEMPLATE_FILES) {
        try {
            const response = await axios.get(
                `${TEST_CONFIG.BACKEND_URL}/api/v1/templates/sdcgen/${filename}`,
                { responseType: 'stream' }
            );
            
            if (response.status === 200) {
                console.log(`✅ Template download successful: ${filename}`);
                console.log(`   Content-Type: ${response.headers['content-type']}`);
                console.log(`   Content-Length: ${response.headers['content-length']}`);
            } else {
                console.log(`❌ Template download failed: ${filename} (Status: ${response.status})`);
            }
        } catch (error) {
            console.log(`❌ Template download error: ${filename}`);
            console.log(`   Error: ${error.message}`);
        }
    }
}

// 测试2: 文件上传验证（模拟前端行为）
async function testFileUpload() {
    console.log('\n🧪 Testing File Upload Functionality...');
    
    // 创建FormData模拟前端文件上传
    const formData = new FormData();
    formData.append('toolId', 'sdc-generator');
    formData.append('parameters', JSON.stringify({
        modName: 'test_module',
        isFlat: false
    }));
    
    // 添加测试文件
    const testFiles = [
        { name: 'test_hier.yaml', field: 'files' },
        { name: 'test_vlog.v', field: 'files' },
        { name: 'test_dcont.xlsx', field: 'files' }
    ];
    
    testFiles.forEach(({ name, field }) => {
        const filePath = path.join(TEST_CONFIG.TEST_FILES_DIR, name);
        if (fs.existsSync(filePath)) {
            formData.append(field, fs.createReadStream(filePath), name);
        }
    });
    
    try {
        // 注意：这个测试会失败，因为没有有效的认证token
        // 这是预期的，我们主要测试请求格式是否正确
        const response = await axios.post(
            `${TEST_CONFIG.BACKEND_URL}/api/v1/tasks`,
            formData,
            {
                headers: {
                    ...formData.getHeaders(),
                    // 'Authorization': 'Bearer test-token' // 在实际测试中需要有效token
                }
            }
        );
        
        console.log(`✅ File upload request successful (Status: ${response.status})`);
    } catch (error) {
        if (error.response && error.response.status === 401) {
            console.log('✅ File upload request format correct (401 Unauthorized as expected without token)');
        } else {
            console.log(`❌ File upload request failed: ${error.message}`);
        }
    }
}

// 测试3: 服务器健康检查
async function testServerHealth() {
    console.log('\n🧪 Testing Server Health...');
    
    try {
        // 测试后端服务器
        const backendResponse = await axios.get(`${TEST_CONFIG.BACKEND_URL}/health`, {
            timeout: 5000
        });
        console.log(`✅ Backend server healthy (Status: ${backendResponse.status})`);
    } catch (error) {
        console.log(`❌ Backend server not responding: ${error.message}`);
    }
    
    try {
        // 测试前端服务器
        const frontendResponse = await axios.get(TEST_CONFIG.FRONTEND_URL, {
            timeout: 5000
        });
        console.log(`✅ Frontend server healthy (Status: ${frontendResponse.status})`);
    } catch (error) {
        console.log(`❌ Frontend server not responding: ${error.message}`);
    }
}

// 测试4: 环境配置验证
function testEnvironmentConfig() {
    console.log('\n🧪 Testing Environment Configuration...');
    
    const requiredEnvVars = [
        'DATABASE_URL',
        'REDIS_URL',
        'OSS_REGION',
        'OSS_BUCKET_USER_INPUT'
    ];
    
    // 读取后端环境配置
    const envPath = path.join(__dirname, 'backend', '.env');
    if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const envVars = {};
        
        envContent.split('\n').forEach(line => {
            const [key, value] = line.split('=');
            if (key && value) {
                envVars[key.trim()] = value.trim();
            }
        });
        
        requiredEnvVars.forEach(varName => {
            if (envVars[varName]) {
                const isPlaceholder = envVars[varName].includes('your-') || 
                                    envVars[varName].includes('localhost') ||
                                    envVars[varName].includes('password');
                
                if (isPlaceholder) {
                    console.log(`⚠️ ${varName}: ${envVars[varName]} (placeholder value)`);
                } else {
                    console.log(`✅ ${varName}: configured`);
                }
            } else {
                console.log(`❌ ${varName}: missing`);
            }
        });
    } else {
        console.log('❌ Backend .env file not found');
    }
}

// 主测试函数
async function runTests() {
    console.log('🚀 Starting SDC Stage 1 & Stage 2 Tests');
    console.log('==========================================');
    
    // 设置测试环境
    setupTestEnvironment();
    
    // 运行测试
    await testServerHealth();
    testEnvironmentConfig();
    await testTemplateDownload();
    await testFileUpload();
    
    console.log('\n📊 Test Summary');
    console.log('================');
    console.log('✅ Tests completed. Check individual test results above.');
    console.log('⚠️ Note: Some tests may fail due to missing authentication or services not running.');
    console.log('🔧 This is expected in a development environment.');
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    runTests,
    testTemplateDownload,
    testFileUpload,
    testServerHealth,
    testEnvironmentConfig
};
