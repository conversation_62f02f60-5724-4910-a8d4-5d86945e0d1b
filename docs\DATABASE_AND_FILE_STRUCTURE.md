# SDC工具系统数据库和文件结构文档

## 📊 数据库结构

### Task表结构

```sql
CREATE TABLE Task (
    id           VARCHAR(255) PRIMARY KEY,           -- 任务唯一ID (UUID)
    userId       VARCHAR(255) NOT NULL,              -- 用户ID
    toolId       VARCHAR(255) NOT NULL,              -- 工具ID (如: 'sdc-generator')
    status       ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    parameters   JSON,                               -- 任务参数和配置
    inputFile    VARCHAR(500),                       -- 主输入文件OSS路径
    outputOssPath VARCHAR(500),                      -- 输出文件OSS路径
    logOssPath   VARCHAR(500),                       -- 日志文件OSS路径
    errorMessage TEXT,                               -- 错误信息
    workerId     VARCHAR(255),                       -- 执行Worker ID
    ecsInstanceId VARCHAR(255),                      -- ECS实例ID
    createdAt    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    startedAt    TIMESTAMP,                          -- 开始执行时间
    finishedAt   TIMESTAMP,                          -- 完成时间
    
    -- 外键关系
    FOREIGN KEY (userId) REFERENCES User(id),
    FOREIGN KEY (toolId) REFERENCES Tool(id)
);
```

### Task.parameters字段详细结构

```json
{
  // SDC工具特定参数
  "modName": "test_module",                          // 模块名称
  "isFlat": false,                                   // 是否扁平化模式
  
  // 文件路径信息
  "inputFilesDirectory": "user123/task456/inputs",   // 输入文件目录
  "outputDirectory": "user123/task456/outputs",      // 输出文件目录  
  "logDirectory": "user123/task456/logs",            // 日志文件目录
  
  // 具体文件路径列表（用于Worker下载）
  "inputFilesList": [
    "user123/task456/inputs/hier.yaml",
    "user123/task456/inputs/vlog.v", 
    "user123/task456/inputs/dcont.xlsx"
  ]
}
```

## 🗂️ OSS文件结构

### OSS Bucket组织结构

```
OSS_BUCKET_USER_INPUT (用户输入文件)
├── ${userId}/
│   └── ${taskId}/
│       └── inputs/
│           ├── hier.yaml          # 层次结构配置文件
│           ├── vlog.v             # Verilog设计文件
│           └── dcont.xlsx         # 设计约束文件

OSS_BUCKET_JOB_RESULTS (任务结果文件)  
├── ${userId}/
│   └── ${taskId}/
│       └── outputs/
│           ├── sdc_result.zip     # 主要结果文件(包含outputs/logs/rpts)
│           └── result_summary.txt # 结果摘要文件

OSS_BUCKET_JOB_LOGS (任务日志文件)
├── ${userId}/
│   └── ${taskId}/
│       └── logs/
│           ├── task_execution.log # 人类可读的执行日志
│           ├── task_execution.json # 结构化JSON日志
│           └── execution_${taskId}.log # Worker执行日志
```

### 文件路径示例

```bash
# 用户输入文件
user123/task456/inputs/hier.yaml
user123/task456/inputs/vlog.v
user123/task456/inputs/dcont.xlsx

# 任务输出文件
user123/task456/outputs/sdc_result.zip
user123/task456/outputs/result_summary.txt

# 任务日志文件
user123/task456/logs/task_execution.log
user123/task456/logs/task_execution.json
user123/task456/logs/execution_task456.log
```

## 🖥️ ECS文件结构

### ECS服务器目录结构

```
/opt/logiccore/                    # 应用根目录
├── backend/                       # 后端应用
├── frontend/                      # 前端应用
├── stuff/
│   ├── tool_template/             # 工具模板文件
│   │   ├── sdcgen/               # SDC工具模板
│   │   │   ├── hier.yaml         # 模板文件
│   │   │   ├── vlog.v            # 模板文件
│   │   │   └── dcont.xlsx        # 模板文件
│   │   ├── clkgen/               # 时钟生成工具模板
│   │   └── memgen/               # 内存数据生成工具模板
│   └── tools_collection/         # 工具集合
│       └── sdcgen/               # SDC工具可执行文件
│           ├── xconst            # SDC工具可执行文件
│           ├── xonst             # SDC工具可执行文件
│           └── com/              # 工具依赖库
└── data/
    └── logiccore_jobs/           # 临时任务目录
        └── ${taskId}/            # 任务专用临时目录
            ├── input/            # 输入文件临时存储
            ├── output/           # 输出文件临时存储
            ├── work/             # 工具工作目录
            │   └── ${modName}/   # 模块工作目录
            │       └── sdc/      # SDC工具目录
            │           ├── inputs/  # 工具输入文件
            │           ├── outputs/ # 工具输出文件
            │           ├── logs/    # 工具日志文件
            │           └── rpts/    # 工具报告文件
            └── logs/             # 任务执行日志
```

### 临时目录生命周期

```bash
# 任务开始时创建
/tmp/logiccore_jobs/${taskId}/
├── input/     # 从OSS下载的输入文件
├── output/    # 生成的输出文件（上传到OSS前）
├── work/      # Docker容器工作目录
└── logs/      # 任务执行日志

# 任务完成后自动清理
# 所有临时文件被删除，释放磁盘空间
```

## 🔄 任务执行数据流

### 1. 任务提交阶段

```sql
-- 创建任务记录
INSERT INTO Task (
    id, userId, toolId, status, 
    inputFile, parameters
) VALUES (
    'task-uuid-123', 'user-456', 'sdc-generator', 'PENDING',
    'user-456/task-uuid-123/inputs',
    '{
        "modName": "test_module",
        "isFlat": false,
        "inputFilesDirectory": "user-456/task-uuid-123/inputs",
        "outputDirectory": "user-456/task-uuid-123/outputs",
        "logDirectory": "user-456/task-uuid-123/logs",
        "inputFilesList": [
            "user-456/task-uuid-123/inputs/hier.yaml",
            "user-456/task-uuid-123/inputs/vlog.v",
            "user-456/task-uuid-123/inputs/dcont.xlsx"
        ]
    }'
);
```

### 2. 文件上传阶段

```bash
# 文件上传到OSS_BUCKET_USER_INPUT
user-456/task-uuid-123/inputs/hier.yaml
user-456/task-uuid-123/inputs/vlog.v  
user-456/task-uuid-123/inputs/dcont.xlsx
```

### 3. 任务执行阶段

```sql
-- 更新任务状态为RUNNING
UPDATE Task SET 
    status = 'RUNNING',
    startedAt = NOW(),
    workerId = 'worker-01',
    ecsInstanceId = 'ecs-prod-01'
WHERE id = 'task-uuid-123';
```

```bash
# ECS临时目录创建
/tmp/logiccore_jobs/task-uuid-123/
├── input/
│   ├── hier.yaml    # 从OSS下载
│   ├── vlog.v       # 从OSS下载  
│   └── dcont.xlsx   # 从OSS下载
├── work/
│   └── test_module/
│       └── sdc/
│           ├── inputs/   # 工具输入文件
│           ├── outputs/  # 工具生成文件
│           ├── logs/     # 工具日志
│           └── rpts/     # 工具报告
└── output/
    ├── sdc_result.zip      # 打包的结果文件
    └── result_summary.txt  # 结果摘要
```

### 4. 任务完成阶段

```sql
-- 更新任务状态为COMPLETED
UPDATE Task SET 
    status = 'COMPLETED',
    finishedAt = NOW(),
    outputOssPath = 'user-456/task-uuid-123/outputs/sdc_result.zip',
    logOssPath = 'user-456/task-uuid-123/logs/task_execution.log'
WHERE id = 'task-uuid-123';
```

```bash
# 文件上传到OSS
OSS_BUCKET_JOB_RESULTS:
  user-456/task-uuid-123/outputs/sdc_result.zip
  user-456/task-uuid-123/outputs/result_summary.txt

OSS_BUCKET_JOB_LOGS:
  user-456/task-uuid-123/logs/task_execution.log
  user-456/task-uuid-123/logs/task_execution.json

# 临时目录清理
rm -rf /tmp/logiccore_jobs/task-uuid-123/
```

## 📋 关键配置参数

### 环境变量配置

```bash
# OSS配置
OSS_BUCKET_USER_INPUT=logiccore-prod-user-input
OSS_BUCKET_JOB_RESULTS=logiccore-prod-job-results  
OSS_BUCKET_JOB_LOGS=logiccore-prod-job-logs

# 临时目录配置
TEMP_JOBS_DIR=/tmp/logiccore_jobs

# ACR配置
ACR_REGISTRY=your-acr-registry.azurecr.io
```

### 文件大小限制

```bash
# 输入文件限制
MAX_INPUT_FILE_SIZE=5MB
MAX_TOTAL_INPUT_SIZE=15MB

# 输出文件限制  
MAX_OUTPUT_FILE_SIZE=100MB

# 临时目录限制
MAX_TEMP_DIR_SIZE=500MB
```

## 🔍 监控和维护

### 关键监控指标

1. **数据库监控**
   - Task表记录数量
   - 任务状态分布
   - 平均执行时间

2. **OSS存储监控**
   - 各bucket存储使用量
   - 文件上传下载成功率
   - 存储成本

3. **ECS资源监控**
   - 临时目录磁盘使用量
   - Docker容器资源使用
   - 任务并发数量

### 清理策略

```bash
# OSS文件生命周期
用户输入文件: 30天后删除
任务结果文件: 90天后转为低频存储
任务日志文件: 180天后删除

# 数据库清理
已完成任务: 1年后归档
失败任务: 6个月后删除
```
