import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Package } from 'lucide-react';

const SubscriptionsPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <Package className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold text-gray-900">订阅管理</h1>
          <p className="text-gray-600">管理用户订阅状态</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>订阅管理功能开发中</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            订阅管理功能正在开发中，敬请期待。
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscriptionsPage; 