# SDC Generator Tool Docker Image
# 基于Ubuntu 20.04构建SDC工具执行环境

FROM ubuntu:20.04

# 设置非交互式安装，避免时区等配置问题
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    curl \
    wget \
    unzip \
    zip \
    git \
    vim \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
RUN pip3 install --no-cache-dir \
    oss2 \
    requests \
    pyyaml \
    openpyxl \
    pandas

# 创建工作目录
WORKDIR /app

# 创建数据目录
RUN mkdir -p /data/input /data/output /data/logs /data/work

# 复制SDC工具文件（从stuff/tools_collection/sdcgen/）
COPY stuff/tools_collection/sdcgen/ /app/sdcgen/

# 复制执行脚本
COPY scripts/docker_sdc_entrypoint.sh /app/entrypoint.sh

# 创建非root用户（安全最佳实践）
RUN groupadd -r sdcuser && useradd -r -g sdcuser sdcuser
RUN chown -R sdcuser:sdcuser /app /data

# 设置执行权限
RUN chmod +x /app/entrypoint.sh
RUN chmod +x /app/sdcgen/xconst
RUN chmod +x /app/sdcgen/xonst

# 设置环境变量
ENV PATH="/app/sdcgen:$PATH"
ENV SDC_TOOL_HOME="/app/sdcgen"

# 切换到非root用户
USER sdcuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD test -f /app/entrypoint.sh || exit 1

# 设置入口点
ENTRYPOINT ["/app/entrypoint.sh"]

# 默认命令
CMD ["--help"]
