import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const SdcGuidancePage: React.FC = () => {
    const navigate = useNavigate();

    const handleBackClick = () => {
        navigate('/tools/sdc-generator');
    };

    return (
        <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8"
        >
            <div className="mb-6">
                <Button 
                    variant="outline" 
                    onClick={handleBackClick}
                    className="text-blue-600 border-blue-600 hover:bg-blue-50"
                >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    返回SDC生成器
                </Button>
            </div>

            <Card className="border-2 border-orange-400 shadow-lg">
                <CardHeader>
                    <CardTitle className="text-3xl font-bold text-orange-600 text-center">
                        SDC Generation Guidance
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                        <div className="space-y-4 text-gray-800">
                            <div className="flex items-start space-x-3">
                                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</span>
                                <div>
                                    <p className="font-medium text-orange-800">ModName填入harden模块名字；</p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</span>
                                <div>
                                    <p className="font-medium text-orange-800">
                                        IsFlat选项，False表示harden block only level，不做下面层次harden模块集成，
                                        True表示flatten集成模块下面所有SDC；
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</span>
                                <div>
                                    <p className="font-medium text-orange-800">
                                        本地需要准备三个文件，分别是hier.yaml, vlog.v, dcont.xlsx，
                                        具体填写方式参考各自template，主页也有更详细的工具指南；
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</span>
                                <div>
                                    <p className="font-medium text-orange-800">
                                        上面是需求信息，务必结合格式要求填写正确清晰；
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <span className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</span>
                                <div>
                                    <p className="font-medium text-orange-800">
                                        提交任务后等待几分钟，直到提示任务完成，Download Zip Data按钮
                                        变为绿色，可供下载状态。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h3 className="text-lg font-semibold text-blue-800 mb-2">重要提示</h3>
                        <ul className="space-y-2 text-blue-700">
                            <li>• 请确保所有文件格式正确，文件大小不超过5MB</li>
                            <li>• ModName应该是有效的模块名称，建议使用字母、数字和下划线</li>
                            <li>• 如果您是首次使用，建议先下载template文件作为参考</li>
                            <li>• 任务提交前会检查您的登录状态和使用权限</li>
                            <li>• 生成的SDC文件将包含完整的时序约束信息</li>
                        </ul>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">文件说明</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="text-center">
                                <div className="bg-yellow-100 p-3 rounded-lg">
                                    <h4 className="font-medium text-yellow-800">hier.yaml</h4>
                                    <p className="text-sm text-yellow-700 mt-1">层次结构配置文件</p>
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="bg-green-100 p-3 rounded-lg">
                                    <h4 className="font-medium text-green-800">vlog.v</h4>
                                    <p className="text-sm text-green-700 mt-1">Verilog设计文件</p>
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="bg-purple-100 p-3 rounded-lg">
                                    <h4 className="font-medium text-purple-800">dcont.xlsx</h4>
                                    <p className="text-sm text-purple-700 mt-1">设计约束表格</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="text-center">
                        <Button 
                            onClick={handleBackClick}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2"
                        >
                            开始使用SDC生成器
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </motion.div>
    );
};

export default SdcGuidancePage;
