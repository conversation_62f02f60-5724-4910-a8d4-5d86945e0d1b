"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText, Zap, Settings, CheckCircle, AlertTriangle, Info } from 'lucide-react';

export default function UPFGuidancePage() {
    const navigate = useNavigate();

    const handleBackToTool = () => {
        navigate('/tools/upf-generator');
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto max-w-6xl p-4 sm:p-6 lg:p-8"
        >
            <div className="space-y-6">
                {/* 页面标题 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader className="relative">
                        <CardTitle className="text-2xl md:text-3xl font-bold text-blue-600">
                            UPF工具使用指南
                        </CardTitle>
                        <Button
                            className="absolute top-4 right-4 bg-white border-2 border-orange-600 text-orange-600 hover:bg-orange-50 font-bold text-lg px-6 py-2 rounded-lg shadow-md transform transition-all duration-200 hover:scale-105 hover:shadow-lg"
                            onClick={handleBackToTool}
                        >
                            <ArrowLeft className="mr-2 h-5 w-5" />
                            返回工具
                        </Button>
                    </CardHeader>
                    <CardContent>
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <div className="flex items-start space-x-3">
                                <Info className="h-6 w-6 text-blue-600 mt-0.5" />
                                <div>
                                    <h3 className="text-blue-800 font-semibold text-lg mb-2">工具概述</h3>
                                    <p className="text-blue-700 leading-relaxed">
                                        UPF（Unified Power Format）工具用于自动生成功耗管理文件，帮助芯片设计师实现精确的功耗控制和优化。
                                        支持多电压域管理、电源策略定义和功耗状态控制。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 输入文件说明 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl font-bold text-blue-600 flex items-center">
                            <FileText className="mr-2 h-6 w-6" />
                            输入文件说明
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* hier.yaml */}
                            <div className="border border-orange-200 rounded-lg p-4 bg-orange-50">
                                <h3 className="text-orange-800 font-semibold text-lg mb-3 flex items-center">
                                    <FileText className="mr-2 h-5 w-5" />
                                    hier.yaml
                                </h3>
                                <div className="space-y-2 text-orange-700">
                                    <p><strong>用途：</strong>层次结构定义文件</p>
                                    <p><strong>格式：</strong>YAML格式</p>
                                    <p><strong>内容：</strong>定义设计的层次结构、模块关系和功耗域划分</p>
                                    <div className="mt-3 p-2 bg-orange-100 rounded text-sm">
                                        <strong>示例内容：</strong><br/>
                                        <code className="text-orange-900">
                                            top_module:<br/>
                                            &nbsp;&nbsp;power_domains: [PD1, PD2]<br/>
                                            &nbsp;&nbsp;sub_modules: [cpu, gpu]
                                        </code>
                                    </div>
                                </div>
                            </div>

                            {/* pvlog.v */}
                            <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                                <h3 className="text-blue-800 font-semibold text-lg mb-3 flex items-center">
                                    <Zap className="mr-2 h-5 w-5" />
                                    pvlog.v
                                </h3>
                                <div className="space-y-2 text-blue-700">
                                    <p><strong>用途：</strong>功耗相关的Verilog文件</p>
                                    <p><strong>格式：</strong>Verilog格式</p>
                                    <p><strong>内容：</strong>包含功耗控制信号、电源开关和功耗管理逻辑</p>
                                    <div className="mt-3 p-2 bg-blue-100 rounded text-sm">
                                        <strong>示例内容：</strong><br/>
                                        <code className="text-blue-900">
                                            module power_ctrl(<br/>
                                            &nbsp;&nbsp;input clk, rst,<br/>
                                            &nbsp;&nbsp;output power_en<br/>
                                            );
                                        </code>
                                    </div>
                                </div>
                            </div>

                            {/* pobj.tcl */}
                            <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                                <h3 className="text-green-800 font-semibold text-lg mb-3 flex items-center">
                                    <Settings className="mr-2 h-5 w-5" />
                                    pobj.tcl
                                </h3>
                                <div className="space-y-2 text-green-700">
                                    <p><strong>用途：</strong>功耗对象定义脚本</p>
                                    <p><strong>格式：</strong>TCL脚本格式</p>
                                    <p><strong>内容：</strong>定义功耗域、电源网络和功耗策略对象</p>
                                    <div className="mt-3 p-2 bg-green-100 rounded text-sm">
                                        <strong>示例内容：</strong><br/>
                                        <code className="text-green-900">
                                            create_power_domain PD1<br/>
                                            create_power_switch SW1<br/>
                                            set_domain_supply_net PD1 VDD
                                        </code>
                                    </div>
                                </div>
                            </div>

                            {/* pcont.xlsx */}
                            <div className="border border-purple-200 rounded-lg p-4 bg-purple-50">
                                <h3 className="text-purple-800 font-semibold text-lg mb-3 flex items-center">
                                    <FileText className="mr-2 h-5 w-5" />
                                    pcont.xlsx
                                </h3>
                                <div className="space-y-2 text-purple-700">
                                    <p><strong>用途：</strong>功耗约束配置表</p>
                                    <p><strong>格式：</strong>Excel表格格式</p>
                                    <p><strong>内容：</strong>功耗约束参数、电压等级和功耗预算配置</p>
                                    <div className="mt-3 p-2 bg-purple-100 rounded text-sm">
                                        <strong>表格列：</strong><br/>
                                        <span className="text-purple-900">
                                            Domain | Voltage | Power_Budget | Switch_Type
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 参数说明 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl font-bold text-blue-600 flex items-center">
                            <Settings className="mr-2 h-6 w-6" />
                            参数配置说明
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {/* ModName */}
                            <div className="border border-orange-200 rounded-lg p-4">
                                <h3 className="text-orange-800 font-semibold text-lg mb-3">ModName</h3>
                                <div className="space-y-2 text-gray-700">
                                    <p><strong>说明：</strong>顶层模块名称</p>
                                    <p><strong>要求：</strong>必须与设计中的顶层模块名一致</p>
                                    <p><strong>示例：</strong>cpu_top, soc_main</p>
                                </div>
                            </div>

                            {/* Version */}
                            <div className="border border-blue-200 rounded-lg p-4">
                                <h3 className="text-blue-800 font-semibold text-lg mb-3">Version</h3>
                                <div className="space-y-2 text-gray-700">
                                    <p><strong>说明：</strong>UPF格式版本</p>
                                    <p><strong>选项：</strong>1.0, 2.0, 3.0</p>
                                    <p><strong>推荐：</strong>使用2.0版本（兼容性最佳）</p>
                                </div>
                            </div>

                            {/* IsFlat */}
                            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                <h3 className="text-gray-600 font-semibold text-lg mb-3">IsFlat</h3>
                                <div className="space-y-2 text-gray-500">
                                    <p><strong>说明：</strong>平坦化生成模式</p>
                                    <p><strong>状态：</strong>暂不支持</p>
                                    <p><strong>备注：</strong>后续版本将支持此功能</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 使用流程 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl font-bold text-blue-600 flex items-center">
                            <CheckCircle className="mr-2 h-6 w-6" />
                            使用流程
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-start space-x-4">
                                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">1</div>
                                <div>
                                    <h3 className="font-semibold text-lg text-gray-800">准备输入文件</h3>
                                    <p className="text-gray-600">准备hier.yaml、pvlog.v、pobj.tcl、pcont.xlsx四个文件</p>
                                </div>
                            </div>
                            
                            <div className="flex items-start space-x-4">
                                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">2</div>
                                <div>
                                    <h3 className="font-semibold text-lg text-gray-800">配置参数</h3>
                                    <p className="text-gray-600">设置ModName（模块名）和Version（UPF版本）</p>
                                </div>
                            </div>
                            
                            <div className="flex items-start space-x-4">
                                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">3</div>
                                <div>
                                    <h3 className="font-semibold text-lg text-gray-800">上传文件</h3>
                                    <p className="text-gray-600">依次上传所有必需的输入文件</p>
                                </div>
                            </div>
                            
                            <div className="flex items-start space-x-4">
                                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">4</div>
                                <div>
                                    <h3 className="font-semibold text-lg text-gray-800">提交任务</h3>
                                    <p className="text-gray-600">点击提交按钮，系统将自动处理并生成UPF文件</p>
                                </div>
                            </div>
                            
                            <div className="flex items-start space-x-4">
                                <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center font-bold">5</div>
                                <div>
                                    <h3 className="font-semibold text-lg text-gray-800">下载结果</h3>
                                    <p className="text-gray-600">任务完成后，下载生成的UPF文件和相关报告</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 注意事项 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl font-bold text-blue-600 flex items-center">
                            <AlertTriangle className="mr-2 h-6 w-6" />
                            注意事项
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h3 className="text-yellow-800 font-semibold mb-2">文件格式要求</h3>
                                <ul className="text-yellow-700 space-y-1">
                                    <li>• hier.yaml文件必须符合YAML语法规范</li>
                                    <li>• pvlog.v文件必须是有效的Verilog代码</li>
                                    <li>• pobj.tcl文件必须是有效的TCL脚本</li>
                                    <li>• pcont.xlsx文件必须包含必要的列字段</li>
                                </ul>
                            </div>
                            
                            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                                <h3 className="text-red-800 font-semibold mb-2">常见错误</h3>
                                <ul className="text-red-700 space-y-1">
                                    <li>• ModName与设计文件中的模块名不匹配</li>
                                    <li>• 文件编码格式不正确（建议使用UTF-8）</li>
                                    <li>• 功耗域定义与实际设计不符</li>
                                    <li>• 电源网络连接关系错误</li>
                                </ul>
                            </div>
                            
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h3 className="text-green-800 font-semibold mb-2">最佳实践</h3>
                                <ul className="text-green-700 space-y-1">
                                    <li>• 使用模板文件作为起始点，确保格式正确</li>
                                    <li>• 在提交前仔细检查所有参数设置</li>
                                    <li>• 保持文件命名的一致性</li>
                                    <li>• 定期备份重要的配置文件</li>
                                </ul>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 返回按钮 */}
                <div className="flex justify-center pt-6">
                    <Button
                        className="bg-gradient-to-r from-blue-600 to-orange-500 hover:from-blue-700 hover:to-orange-600 text-white font-bold text-lg px-8 py-3 rounded-lg shadow-lg transform transition-all duration-200 hover:scale-105 hover:shadow-xl"
                        onClick={handleBackToTool}
                    >
                        <ArrowLeft className="mr-2 h-5 w-5" />
                        返回UPF工具
                    </Button>
                </div>
            </div>
        </motion.div>
    );
}
