import { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import HomePage from "./pages/home";
import LoginPage from "./pages/auth/login";
import RegisterPage from "./pages/auth/register";
import MembershipPage from "./pages/membership";
import ContactPage from "./pages/contact";
import NotFoundPage from "./pages/not-found";
import Navigation from "./components/navigation";
import Footer from "./components/footer";
import EmailVerificationResultPage from './pages/auth/email-verification-result';
import ForgotPasswordPage from './pages/auth/forgot-password';
import ResetPasswordPage from './pages/auth/reset-password';
import VerifyCodePage from './pages/auth/verify-code';
import ProfilePage from './pages/profile';
import ProtectedRoute from './components/protected-route';

// Admin components
import AdminLoginPage from './pages/admin/login';
import AdminDashboardPage from './pages/admin/dashboard';
import UsersPage from './pages/admin/users';
import TasksPage from './pages/admin/tasks';
const OrdersPage = lazy(() => import('./pages/admin/orders'));
const PlansPage = lazy(() => import('./pages/admin/plans'));
const SubscriptionsPage = lazy(() => import('./pages/admin/subscriptions'));
const ToolsPage = lazy(() => import('./pages/admin/tools'));
import AdminLayout from './components/admin/admin-layout';
import AdminRoute from './components/admin/admin-route';

// Order and Payment pages
import OrderConfirmPage from "./pages/order/confirm";
import CheckoutPage from "./pages/order/checkout";
import PaymentResultPage from "./pages/payment/result";

const SdcGeneratorPage = lazy(() => import('./pages/tools/SdcGeneratorPage'));
const ClkGeneratorPage = lazy(() => import('./pages/tools/ClkGeneratorPage'));
const MemoryDataGeneratorPage = lazy(() => import('./pages/tools/MemoryDataGeneratorPage'));
const ToolsIndexPage = lazy(() => import('./pages/tools/index'));
const OrderDetailsPage = lazy(() => import('./pages/order/details'));
const OrderHistoryPage = lazy(() => import('./pages/order/history'));
import { Toaster } from "@/components/ui/toaster";

function App() {
  console.log('‍ ChipCore App组件开始渲染');
  const environment = import.meta.env.VITE_APP_ENV || 'development';
  console.log('[ChipCore Debug] Environment Configuration:', { environment, ...import.meta.env });

  return (
    <Router>
      <div className="flex flex-col min-h-screen">
        <Navigation />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/auth/login" element={<LoginPage />} />
            <Route path="/auth/register" element={<RegisterPage />} />
            <Route path="/membership" element={<MembershipPage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/auth/email-verification-result" element={<EmailVerificationResultPage />} />
            <Route path="/auth/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/auth/reset-password" element={<ResetPasswordPage />} />
            <Route path="/auth/verify-code" element={<VerifyCodePage />} />
            
            {/* Admin routes */}
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="/admin" element={<AdminRoute />}>
              <Route element={<AdminLayout />}>
                <Route path="dashboard" element={<AdminDashboardPage />} />
                <Route path="users" element={<UsersPage />} />
                <Route path="tasks" element={<TasksPage />} />
                <Route 
                  path="orders" 
                  element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <OrdersPage />
                    </Suspense>
                  } 
                />
                <Route 
                  path="plans" 
                  element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <PlansPage />
                    </Suspense>
                  } 
                />
                <Route 
                  path="subscriptions" 
                  element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <SubscriptionsPage />
                    </Suspense>
                  } 
                />
                <Route 
                  path="tools" 
                  element={
                    <Suspense fallback={<div>Loading...</div>}>
                      <ToolsPage />
                    </Suspense>
                  } 
                />
                <Route index element={<AdminDashboardPage />} />
              </Route>
            </Route>
            
            {/* Payment result can be viewed without login */}
            <Route path="/payment/result" element={<PaymentResultPage />} />

            <Route element={<ProtectedRoute />}>
              <Route path="/profile" element={<ProfilePage />} />
              {/* Order Flow */}
              <Route path="/order/confirm" element={<OrderConfirmPage />} />
              <Route path="/order/checkout" element={<CheckoutPage />} />
              <Route 
                path="/order/details/:orderNo" 
                element={
                  <Suspense fallback={<div>Loading...</div>}>
                    <OrderDetailsPage />
                  </Suspense>
                } 
              />
              <Route 
                path="/user/orders" 
                element={
                  <Suspense fallback={<div>Loading...</div>}>
                    <OrderHistoryPage />
                  </Suspense>
                } 
              />
            </Route>
            
            {/* Tool pages are public, usage is restricted inside the component */}
            <Route
              path="/tools"
              element={
                <Suspense fallback={<div>Loading Tools...</div>}>
                  <ToolsIndexPage />
                </Suspense>
              }
            />
            <Route
              path="/tools/sdc-generator"
              element={
                <Suspense fallback={<div>Loading SDC Generator...</div>}>
                  <SdcGeneratorPage />
                </Suspense>
              }
            />
            <Route 
              path="/tools/clk-generator" 
              element={
                <Suspense fallback={<div>Loading Clock Generator...</div>}>
                  <ClkGeneratorPage />
                </Suspense>
              } 
            />
            <Route 
              path="/tools/memory-generator" 
              element={
                <Suspense fallback={<div>Loading Memory Generator...</div>}>
                  <MemoryDataGeneratorPage />
                </Suspense>
              } 
            />

            {/* Catch-all for 404 */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </main>
        <Footer />
        <Toaster />
      </div>
    </Router>
  );
}

export default App;
