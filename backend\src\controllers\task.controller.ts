import { Request, Response } from 'express';
import * as taskService from '../services/task.service';
import { prisma } from '../utils/database';

/**
 * Submits a new task for execution.
 * Handles file upload, creates a task record in the DB, and queues the task in Redis.
 */
export const submitTask = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }

  const files = req.files as Express.Multer.File[];

  try {
    const task = await taskService.createTask(req.body, req.user.id, files);
    res.status(202).json(task);
  } catch (error) {
    console.error('Task submission error:', error);
    if ((error as Error).message.includes('not found')) {
      return res.status(404).json({ message: (error as Error).message });
    }
    res.status(500).json({ message: 'Failed to submit task.', error: (error as Error).message });
  }
};

/**
 * Retrieves the status of a specific task.
 */
export const getTaskStatus = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }
  const { taskId } = req.params;

  try {
    const status = await taskService.getTaskStatus(taskId, req.user.id);
    res.json(status);
  } catch (error) {
    if ((error as Error).message.includes('not found')) {
      return res.status(404).json({ message: (error as Error).message });
    }
    res.status(500).json({ message: 'Failed to get task status.', error: (error as Error).message });
  }
};

/**
 * Generates a pre-signed URL for downloading a task's result or log file.
 */
export const downloadTaskResult = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }
  const { taskId } = req.params;
  const { type } = req.query;

  if (type !== 'result' && type !== 'log') {
    return res.status(400).json({ message: 'Invalid download type specified. Use "result" or "log".' });
  }

  try {
    const downloadUrl = await taskService.getDownloadUrl(taskId, req.user.id, type as 'result' | 'log');
    res.json({ url: downloadUrl });
  } catch (error) {
     if ((error as Error).message.includes('not found')) {
      return res.status(404).json({ message: (error as Error).message });
    }
    res.status(500).json({ message: 'Failed to get download URL.', error: (error as Error).message });
  }
};

/**
 * Retrieves a paginated history of tasks for the current user.
 */
export const getTaskHistory = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }
  const userId = req.user.id;

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const skip = (page - 1) * limit;

  try {
    const [tasks, total] = await prisma.$transaction([
      prisma.task.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          tool: {
            select: {
              name: true,
            },
          },
        },
      }),
      prisma.task.count({ where: { userId } }),
    ]);

    res.status(200).json({
      data: tasks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error: any) {
    console.error('Error getting task history:', error);
    res.status(500).json({ message: 'Failed to get task history.', error: error.message });
  }
};

/**
 * Retrieves the full details of a specific task.
 */
export const getTaskDetails = async (req: Request, res: Response) => {
  const { taskId } = req.params;
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }
  const userId = req.user.id;

  try {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
    });

    // Ensure the user can only access their own tasks
    if (!task || task.userId !== userId) {
      return res.status(404).json({ message: 'Task not found or unauthorized.' });
    }

    res.status(200).json(task);
  } catch (error: any) {
    console.error('Error getting task details:', error);
    res.status(500).json({ message: 'Failed to get task details.', error: error.message });
  }
};

export const getTasks = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }
  
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  try {
    const tasks = await taskService.getUserTasks(req.user.id, page, limit);
    res.json(tasks);
  } catch (error) {
    console.error('Error getting tasks:', error);
    res.status(500).json({ message: 'Failed to get tasks.', error: (error as Error).message });
  }
};

export const getTaskById = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }
  const { taskId } = req.params;
  
  try {
    const task = await taskService.getTaskById(taskId, req.user.id);
    res.json(task);
  } catch (error) {
     if ((error as Error).message.includes('not found')) {
      return res.status(404).json({ message: (error as Error).message });
    }
    res.status(500).json({ message: 'Error retrieving task', error: (error as Error).message });
  }
}; 