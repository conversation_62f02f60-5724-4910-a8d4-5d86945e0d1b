const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestPlan() {
  try {
    console.log('🔄 创建测试计划...');

    // 删除现有的Professional计划（如果存在）
    await prisma.plan.deleteMany({
      where: {
        name: 'Professional'
      }
    });

    // 创建新的Professional计划，价格为0.01元
    const plan = await prisma.plan.create({
      data: {
        name: 'Professional',
        description: '适合专业团队和企业用户',
        priceMonth: 0.01,
        priceYear: 0.01,
        features: {
          toolRunsPerDay: 1000,
          parallelTasks: 10,
          storageGB: 100,
          supportLevel: 'priority',
          advancedFeatures: true,
          apiAccess: true
        }
      }
    });

    console.log('✅ Professional计划创建成功:');
    console.log('   ID:', plan.id);
    console.log('   名称:', plan.name);
    console.log('   月价格:', plan.priceMonth, '元');
    console.log('   年价格:', plan.priceYear, '元');

    // 同时创建Free计划
    await prisma.plan.deleteMany({
      where: {
        name: 'Free'
      }
    });

    const freePlan = await prisma.plan.create({
      data: {
        name: 'Free',
        description: '适合个人用户和小型项目',
        priceMonth: 0,
        priceYear: 0,
        features: {
          toolRunsPerDay: 10,
          parallelTasks: 1,
          storageGB: 1,
          supportLevel: 'basic',
          advancedFeatures: false,
          apiAccess: false
        }
      }
    });

    console.log('✅ Free计划创建成功:');
    console.log('   ID:', freePlan.id);
    console.log('   名称:', freePlan.name);
    console.log('   月价格:', freePlan.priceMonth, '元');
    console.log('   年价格:', freePlan.priceYear, '元');

    // 查询所有计划
    const allPlans = await prisma.plan.findMany();
    console.log('\n📊 所有计划:');
    allPlans.forEach(p => {
      console.log(`   ${p.name}: 月价格 ${p.priceMonth}元, 年价格 ${p.priceYear}元`);
    });

  } catch (error) {
    console.error('❌ 创建计划失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestPlan();
