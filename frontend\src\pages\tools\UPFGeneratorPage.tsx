"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2, FileText, Upload, CheckCircle, AlertCircle, Download } from 'lucide-react';

// UPF表单验证Schema
const upfFormSchema = z.object({
    modName: z.string().min(1, "模块名称不能为空"),
    version: z.string().min(1, "版本不能为空"),
    isFlat: z.boolean().default(false),
    hierYamlFile: z.instanceof(File).optional(),
    pvlogFile: z.instanceof(File).optional(),
    pobjTclFile: z.instanceof(File).optional(),
    pcontXlsxFile: z.instanceof(File).optional()
});

type UPFFormValues = z.infer<typeof upfFormSchema>;

// 任务状态接口
interface TaskStatus {
    status: 'IDLE' | 'VALIDATING' | 'SUBMITTING' | 'POLLING' | 'COMPLETED' | 'FAILED';
    taskId?: string;
    resultUrl?: string;
    error?: string;
}

// 文件上传组件接口
interface FileUploadSectionProps {
    title: string;
    field: any;
    onChange: (file: File | null) => void;
    accept: string;
    placeholder: string;
    form: any;
    name: string;
}

// 文件上传组件
const FileUploadSection: React.FC<FileUploadSectionProps> = ({
    title,
    field,
    onChange,
    accept,
    placeholder,
    form,
    name
}) => {
    const value = field;

    return (
        <div className="border-2 border-dashed border-orange-300 rounded-lg p-4">
            <div className="mb-2">
                <Label className="text-orange-600 font-semibold text-lg">{title}</Label>
            </div>
            <FormField control={form.control} name={name} render={({ field }) => (
                <FormItem>
                    <FormControl>
                        <div>
                            <Label
                                htmlFor={`${name}-upload`}
                                className={`flex items-center space-x-2 border-2 border-dashed rounded-lg p-4 cursor-pointer transition-colors ${
                                    value ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:border-orange-500 hover:bg-orange-50'
                                }`}
                            >
                                {value ? (
                                    <FileText className="h-5 w-5 text-green-700" />
                                ) : (
                                    <Upload className="h-5 w-5 text-gray-500" />
                                )}
                                <span className={value ? 'text-green-700 font-medium' : 'text-gray-500'}>
                                    {value ? value.name : placeholder}
                                </span>
                            </Label>
                            <input
                                id={`${name}-upload`}
                                type="file"
                                accept={accept}
                                className="hidden"
                                onChange={(e) => {
                                    const file = e.target.files?.[0] || null;
                                    onChange(file);
                                    field.onChange(file);
                                }}
                            />
                        </div>
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )} />
        </div>
    );
};

export default function UPFGeneratorPage() {
    const navigate = useNavigate();
    const [taskStatus, setTaskStatus] = useState<TaskStatus>({ status: 'IDLE' });
    const [modNameHistory, setModNameHistory] = useState<string[]>([]);

    // 表单初始化
    const form = useForm<UPFFormValues>({
        resolver: zodResolver(upfFormSchema),
        defaultValues: {
            modName: '',
            version: '',
            isFlat: false,
            hierYamlFile: undefined,
            pvlogFile: undefined,
            pobjTclFile: undefined,
            pcontXlsxFile: undefined
        }
    });

    // 页面加载时获取历史记录
    useEffect(() => {
        const savedHistory = localStorage.getItem('upf_modname_history');
        if (savedHistory) {
            try {
                setModNameHistory(JSON.parse(savedHistory));
            } catch (error) {
                console.error('解析历史记录失败:', error);
            }
        }
    }, []);

    // 保存ModName到历史记录
    const saveModNameToHistory = (modName: string) => {
        if (!modName.trim()) return;
        
        const newHistory = [modName, ...modNameHistory.filter(name => name !== modName)].slice(0, 5);
        setModNameHistory(newHistory);
        localStorage.setItem('upf_modname_history', JSON.stringify(newHistory));
    };

    // 导航到指导页面
    const handleGuidanceClick = () => {
        navigate('/tools/guidance/upf-generator');
    };

    // 模板下载
    const downloadTemplate = async (filename: string) => {
        try {
            const response = await fetch(`/api/v1/templates/upfgen/${filename}`);
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } else {
                console.error('模板下载失败:', response.statusText);
            }
        } catch (error) {
            console.error('模板下载错误:', error);
        }
    };

    // 表单提交处理
    const onSubmit = async (data: UPFFormValues) => {
        console.log('UPF表单提交:', data);
        
        setTaskStatus({ status: 'VALIDATING' });
        
        try {
            // 保存ModName到历史记录
            saveModNameToHistory(data.modName);
            
            // 创建FormData
            const formData = new FormData();
            formData.append('toolId', 'upf-generator');
            formData.append('parameters', JSON.stringify({
                modName: data.modName,
                version: data.version,
                isFlat: data.isFlat
            }));

            // 添加文件
            if (data.hierYamlFile) formData.append('hierYamlFile', data.hierYamlFile);
            if (data.pvlogFile) formData.append('pvlogFile', data.pvlogFile);
            if (data.pobjTclFile) formData.append('pobjTclFile', data.pobjTclFile);
            if (data.pcontXlsxFile) formData.append('pcontXlsxFile', data.pcontXlsxFile);

            setTaskStatus({ status: 'SUBMITTING' });

            // 提交任务
            const response = await fetch('/api/v1/tasks', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`任务提交失败: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('任务提交成功:', result);

            setTaskStatus({ 
                status: 'POLLING', 
                taskId: result.taskId 
            });

            // 开始轮询任务状态
            pollTaskStatus(result.taskId);

        } catch (error) {
            console.error('任务提交错误:', error);
            setTaskStatus({ 
                status: 'FAILED', 
                error: error instanceof Error ? error.message : '未知错误' 
            });
        }
    };

    // 轮询任务状态
    const pollTaskStatus = async (taskId: string) => {
        const maxAttempts = 60; // 最多轮询60次（10分钟）
        let attempts = 0;

        const poll = async () => {
            try {
                const response = await fetch(`/api/v1/tasks/${taskId}/status`);
                if (!response.ok) {
                    throw new Error('获取任务状态失败');
                }

                const statusData = await response.json();
                console.log('任务状态:', statusData);

                if (statusData.status === 'COMPLETED') {
                    setTaskStatus({
                        status: 'COMPLETED',
                        taskId,
                        resultUrl: statusData.resultUrl
                    });
                    return;
                } else if (statusData.status === 'FAILED') {
                    setTaskStatus({
                        status: 'FAILED',
                        taskId,
                        error: statusData.error || '任务执行失败'
                    });
                    return;
                }

                attempts++;
                if (attempts < maxAttempts) {
                    setTimeout(poll, 10000); // 10秒后再次轮询
                } else {
                    setTaskStatus({
                        status: 'FAILED',
                        taskId,
                        error: '任务执行超时'
                    });
                }
            } catch (error) {
                console.error('轮询错误:', error);
                setTaskStatus({
                    status: 'FAILED',
                    taskId,
                    error: '获取任务状态失败'
                });
            }
        };

        poll();
    };

    // 下载结果
    const handleDownload = async () => {
        if (!taskStatus.resultUrl) return;

        try {
            const response = await fetch(taskStatus.resultUrl);
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'upf_result.zip';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }
        } catch (error) {
            console.error('下载失败:', error);
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8"
        >
            <div className="space-y-6">
                {/* UPF需求输入框 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader className="relative">
                        <CardTitle className="text-2xl md:text-3xl font-bold text-blue-600">
                            UPF需求输入：
                        </CardTitle>
                        <div className="absolute top-4 right-4 flex space-x-3">
                            <Button
                                className="bg-white border-2 border-orange-600 text-orange-600 hover:bg-orange-50 font-bold text-lg px-6 py-2 rounded-lg shadow-md transform transition-all duration-200 hover:scale-105 hover:shadow-lg"
                                onClick={handleGuidanceClick}
                            >
                                Guidance
                            </Button>
                            <Button
                                className="bg-white border-2 border-orange-600 text-orange-600 hover:bg-orange-50 font-bold text-lg px-6 py-2 rounded-lg shadow-md transform transition-all duration-200 hover:scale-105 hover:shadow-lg"
                                onClick={() => downloadTemplate('upfgen.zip')}
                            >
                                📁 Template
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                {/* 参数设置区域 */}
                                <div className="bg-gray-50 p-4 rounded-lg">
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                                        {/* ModName输入 */}
                                        <FormField control={form.control} name="modName" render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-orange-600 font-semibold text-lg">ModName</FormLabel>
                                                <FormControl>
                                                    <div className="space-y-2">
                                                        <Input
                                                            placeholder="输入模块名称"
                                                            {...field}
                                                            className="border-orange-300 focus:border-orange-500"
                                                        />
                                                        {modNameHistory.length > 0 && (
                                                            <Select onValueChange={field.onChange}>
                                                                <SelectTrigger className="border-orange-300">
                                                                    <SelectValue placeholder="选择历史记录" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {modNameHistory.map((name, index) => (
                                                                        <SelectItem key={index} value={name}>
                                                                            {name}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                        )}
                                                    </div>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />

                                        {/* Version选择 */}
                                        <FormField control={form.control} name="version" render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-orange-600 font-semibold text-lg">Version</FormLabel>
                                                <FormControl>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <SelectTrigger className="border-orange-300">
                                                            <SelectValue placeholder="选择版本" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="1.0">1.0</SelectItem>
                                                            <SelectItem value="2.0">2.0</SelectItem>
                                                            <SelectItem value="3.0">3.0</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />

                                        {/* IsFlat复选框 - 禁用状态 */}
                                        <FormField control={form.control} name="isFlat" render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-orange-600 font-semibold text-lg">IsFlat</FormLabel>
                                                <div className="flex items-center space-x-3">
                                                    <FormControl>
                                                        <Checkbox
                                                            checked={false}
                                                            disabled={true}
                                                            className="border-orange-300"
                                                        />
                                                    </FormControl>
                                                    <span className="text-gray-500 text-sm">(暂不支持)</span>
                                                </div>
                                            </FormItem>
                                        )} />
                                    </div>
                                </div>

                                {/* 文件上传区域 */}
                                <div className="space-y-4">
                                    {/* hier.yaml上传 */}
                                    <FileUploadSection
                                        title="上传hier.yaml"
                                        field={form.watch('hierYamlFile')}
                                        onChange={(file) => form.setValue('hierYamlFile', file)}
                                        accept=".yaml,.yml"
                                        placeholder="点击或拖拽上传(.yaml)"
                                        form={form}
                                        name="hierYamlFile"
                                    />

                                    {/* pvlog.v上传 */}
                                    <FileUploadSection
                                        title="上传pvlog.v"
                                        field={form.watch('pvlogFile')}
                                        onChange={(file) => form.setValue('pvlogFile', file)}
                                        accept=".v,.sv"
                                        placeholder="点击或拖拽上传(.v)"
                                        form={form}
                                        name="pvlogFile"
                                    />

                                    {/* pobj.tcl上传 */}
                                    <FileUploadSection
                                        title="上传pobj.tcl"
                                        field={form.watch('pobjTclFile')}
                                        onChange={(file) => form.setValue('pobjTclFile', file)}
                                        accept=".tcl"
                                        placeholder="点击或拖拽上传(.tcl)"
                                        form={form}
                                        name="pobjTclFile"
                                    />

                                    {/* pcont.xlsx上传 */}
                                    <FileUploadSection
                                        title="上传pcont.xlsx"
                                        field={form.watch('pcontXlsxFile')}
                                        onChange={(file) => form.setValue('pcontXlsxFile', file)}
                                        accept=".xlsx,.xls"
                                        placeholder="点击或拖拽上传(.xlsx)"
                                        form={form}
                                        name="pcontXlsxFile"
                                    />
                                </div>

                                {/* 提交按钮 */}
                                <div className="flex justify-center pt-6">
                                    <Button
                                        type="submit"
                                        className={`font-bold text-lg px-12 py-3 rounded-lg shadow-lg transform transition-all duration-200 ${
                                            form.formState.isSubmitting || (taskStatus.status !== 'IDLE' && taskStatus.status !== 'COMPLETED' && taskStatus.status !== 'FAILED')
                                                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                                                : 'bg-gradient-to-r from-blue-600 to-orange-500 hover:from-blue-700 hover:to-orange-600 text-white hover:scale-105 hover:shadow-xl'
                                        }`}
                                        disabled={form.formState.isSubmitting || (taskStatus.status !== 'IDLE' && taskStatus.status !== 'COMPLETED' && taskStatus.status !== 'FAILED')}
                                    >
                                        {(form.formState.isSubmitting || taskStatus.status === 'VALIDATING' || taskStatus.status === 'SUBMITTING') ?
                                            <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : null}
                                        {taskStatus.status === 'VALIDATING' ? '验证中...' :
                                         taskStatus.status === 'SUBMITTING' ? '提交中...' :
                                         taskStatus.status === 'POLLING' ? '执行中...' : '提交'}
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>

                {/* UPF数据输出框 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-2xl md:text-3xl font-bold text-blue-600">
                            UPF数据输出：
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex justify-center">
                            <Button
                                className={`px-16 py-4 text-lg border-2 border-dashed w-96 font-bold transform transition-all duration-300 ${
                                    taskStatus.status === 'COMPLETED' && taskStatus.resultUrl
                                        ? 'bg-gradient-to-r from-blue-600 to-orange-500 hover:from-blue-700 hover:to-orange-600 text-white cursor-pointer border-blue-600 shadow-lg hover:scale-105 animate-pulse'
                                        : taskStatus.status === 'POLLING' || taskStatus.status === 'SUBMITTING'
                                        ? 'bg-yellow-400 text-yellow-800 cursor-not-allowed border-yellow-400'
                                        : 'bg-gray-400 text-gray-600 cursor-not-allowed border-gray-400'
                                }`}
                                disabled={taskStatus.status !== 'COMPLETED' || !taskStatus.resultUrl}
                                onClick={handleDownload}
                            >
                                {taskStatus.status === 'COMPLETED' && taskStatus.resultUrl ? (
                                    <>
                                        <Download className="mr-2 h-5 w-5" />
                                        下载UPF结果
                                    </>
                                ) : taskStatus.status === 'POLLING' || taskStatus.status === 'SUBMITTING' ? (
                                    <>
                                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                        处理中...
                                    </>
                                ) : taskStatus.status === 'FAILED' ? (
                                    <>
                                        <AlertCircle className="mr-2 h-5 w-5" />
                                        执行失败
                                    </>
                                ) : (
                                    <>
                                        <Upload className="mr-2 h-5 w-5" />
                                        等待提交
                                    </>
                                )}
                            </Button>
                        </div>
                        
                        {/* 错误信息显示 */}
                        {taskStatus.status === 'FAILED' && taskStatus.error && (
                            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                <div className="flex items-center">
                                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                                    <span className="text-red-700 font-medium">错误信息：</span>
                                </div>
                                <p className="text-red-600 mt-1">{taskStatus.error}</p>
                            </div>
                        )}

                        {/* 任务状态显示 */}
                        {taskStatus.taskId && (
                            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="flex items-center">
                                    <CheckCircle className="h-5 w-5 text-blue-500 mr-2" />
                                    <span className="text-blue-700 font-medium">任务ID：</span>
                                </div>
                                <p className="text-blue-600 mt-1 font-mono">{taskStatus.taskId}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </motion.div>
    );
}
