const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api/v1';

// 创建axios实例，支持cookie
const apiClient = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
  timeout: 10000
});

async function diagnoseProblem() {
  try {
    console.log('🔍 开始诊断订单创建问题...');
    console.log('─'.repeat(50));

    // 1. 检查服务器状态
    console.log('1️⃣ 检查服务器状态...');
    try {
      const healthResponse = await axios.get('http://localhost:8080/health');
      console.log('✅ 服务器运行正常');
    } catch (error) {
      console.log('❌ 服务器状态检查失败:', error.message);
    }

    // 2. 检查计划是否存在
    console.log('\n2️⃣ 检查计划是否存在...');
    try {
      const plansResponse = await axios.get('http://localhost:8080/api/v1/plans');
      console.log('✅ 计划API可访问');
      console.log('   可用计划:', plansResponse.data.map(p => ({ id: p.id, name: p.name, priceMonth: p.priceMonth })));
      
      const targetPlan = plansResponse.data.find(p => p.id === 'cmcu3yg3w0000bcs1eqdlxngz');
      if (targetPlan) {
        console.log('✅ 目标计划存在:', targetPlan.name, '价格:', targetPlan.priceMonth);
      } else {
        console.log('❌ 目标计划不存在');
      }
    } catch (error) {
      console.log('❌ 计划检查失败:', error.message);
    }

    // 3. 尝试登录（使用不同的邮箱避免频率限制）
    console.log('\n3️⃣ 尝试登录...');
    try {
      // 先尝试注册一个测试用户
      try {
        await apiClient.post('/auth/register', {
          email: '<EMAIL>',
          password: 'qaz123',
          name: 'Test User'
        });
        console.log('✅ 测试用户注册成功');
      } catch (regError) {
        console.log('ℹ️ 测试用户可能已存在');
      }

      // 登录（使用已验证的邮箱）
      const loginResponse = await apiClient.post('/auth/login', {
        email: '<EMAIL>',
        password: 'qaz123'
      });

      console.log('✅ 登录成功');
      console.log('   用户ID:', loginResponse.data.user.id);
      console.log('   邮箱:', loginResponse.data.user.email);

      // 提取cookie
      const cookies = loginResponse.headers['set-cookie'];
      console.log('   Cookies:', cookies);

      // 设置cookie到后续请求
      if (cookies) {
        const accessTokenCookie = cookies.find(cookie => cookie.startsWith('access_token='));
        if (accessTokenCookie) {
          // 设置cookie到axios实例
          apiClient.defaults.headers.Cookie = accessTokenCookie.split(';')[0];
          console.log('   ✅ Cookie已设置');
        }
      }

      // 4. 测试订单创建参数
      console.log('\n4️⃣ 测试订单创建参数...');
      
      const orderPayloads = [
        {
          name: '标准参数',
          data: {
            planId: 'cmcu3yg3w0000bcs1eqdlxngz',
            billingCycle: 'MONTHLY',
            paymentMethod: 'ALIPAY'
          }
        },
        {
          name: '微信支付参数',
          data: {
            planId: 'cmcu3yg3w0000bcs1eqdlxngz',
            billingCycle: 'MONTHLY',
            paymentMethod: 'WECHAT'
          }
        }
      ];

      for (const payload of orderPayloads) {
        console.log(`\n   测试 ${payload.name}:`);
        console.log('   参数:', JSON.stringify(payload.data, null, 4));
        
        try {
          const orderResponse = await apiClient.post('/orders', payload.data);
          console.log('   ✅ 订单创建成功');
          console.log('   订单ID:', orderResponse.data.order.id);
          console.log('   订单状态:', orderResponse.data.order.status);
          console.log('   订单金额:', orderResponse.data.order.amount);
          
          // 返回第一个成功的订单ID
          if (!global.testOrderId) {
            global.testOrderId = orderResponse.data.order.id;
          }
          
        } catch (orderError) {
          console.log('   ❌ 订单创建失败');
          console.log('   错误信息:', orderError.response?.data?.message || orderError.message);
          if (orderError.response?.data?.errors) {
            console.log('   验证错误:', JSON.stringify(orderError.response.data.errors, null, 4));
          }
        }
      }

    } catch (loginError) {
      console.log('❌ 登录失败:', loginError.response?.data?.message || loginError.message);
    }

    console.log('\n🎯 诊断完成');
    console.log('─'.repeat(50));

  } catch (error) {
    console.error('❌ 诊断过程出错:', error.message);
  }
}

// 运行诊断
diagnoseProblem();
