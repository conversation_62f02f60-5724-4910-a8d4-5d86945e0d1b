/**
 * 系统路径配置
 * 统一管理ECS上的文件存储路径
 */

import path from 'path';

// ECS项目根目录
export const PROJECT_ROOT = process.cwd();

// Template文件存储路径配置
export const TEMPLATE_PATHS = {
    // Template根目录：/path/to/project/stuff/tool_template/
    ROOT: path.join(PROJECT_ROOT, 'stuff', 'tool_template'),
    
    // 各工具的Template目录
    SDC_GENERATOR: path.join(PROJECT_ROOT, 'stuff', 'tool_template', 'sdcgen'),
    CLK_GENERATOR: path.join(PROJECT_ROOT, 'stuff', 'tool_template', 'clkgen'),
    MEMORY_DATA_GENERATOR: path.join(PROJECT_ROOT, 'stuff', 'tool_template', 'memgen'),
    
    // 获取特定工具的Template目录
    getToolTemplatePath: (toolId: string): string => {
        return path.join(PROJECT_ROOT, 'stuff', 'tool_template', toolId);
    },
    
    // 获取特定Template文件路径
    getTemplateFilePath: (toolId: string, filename: string): string => {
        return path.join(PROJECT_ROOT, 'stuff', 'tool_template', toolId, filename);
    }
};

// 临时工作目录配置
export const TEMP_PATHS = {
    // 临时目录根路径：/tmp/logiccore_jobs/
    ROOT: process.env.TEMP_JOBS_DIR || '/tmp/logiccore_jobs',
    
    // 获取任务临时目录
    getJobTempDir: (taskId: string): string => {
        const tempRoot = process.env.TEMP_JOBS_DIR || '/tmp/logiccore_jobs';
        return path.join(tempRoot, taskId);
    },
    
    // 获取任务输入目录
    getJobInputDir: (taskId: string): string => {
        const tempRoot = process.env.TEMP_JOBS_DIR || '/tmp/logiccore_jobs';
        return path.join(tempRoot, taskId, 'input');
    },
    
    // 获取任务输出目录
    getJobOutputDir: (taskId: string): string => {
        const tempRoot = process.env.TEMP_JOBS_DIR || '/tmp/logiccore_jobs';
        return path.join(tempRoot, taskId, 'output');
    },
    
    // 获取任务工作目录
    getJobWorkDir: (taskId: string): string => {
        const tempRoot = process.env.TEMP_JOBS_DIR || '/tmp/logiccore_jobs';
        return path.join(tempRoot, taskId, 'work');
    },
    
    // 获取任务日志目录
    getJobLogDir: (taskId: string): string => {
        const tempRoot = process.env.TEMP_JOBS_DIR || '/tmp/logiccore_jobs';
        return path.join(tempRoot, taskId, 'logs');
    }
};

// OSS路径配置
export const OSS_PATHS = {
    // 获取用户输入文件OSS路径
    getUserInputPath: (userId: string, taskId: string): string => {
        return `${userId}/${taskId}/inputs`;
    },
    
    // 获取任务输出文件OSS路径
    getTaskOutputPath: (userId: string, taskId: string): string => {
        return `${userId}/${taskId}/outputs`;
    },
    
    // 获取任务日志文件OSS路径
    getTaskLogPath: (userId: string, taskId: string): string => {
        return `${userId}/${taskId}/logs`;
    },
    
    // 获取具体文件的OSS路径
    getFileOssPath: (userId: string, taskId: string, category: 'inputs' | 'outputs' | 'logs', filename: string): string => {
        return `${userId}/${taskId}/${category}/${filename}`;
    }
};

// Docker容器内路径配置
export const CONTAINER_PATHS = {
    INPUT_DIR: '/data/input',
    OUTPUT_DIR: '/data/output',
    WORK_DIR: '/data/work',
    LOG_DIR: '/data/logs',
    
    // SDC工具特定路径
    SDC_MODULE_DIR: (modName: string) => `/data/work/${modName}`,
    SDC_INPUT_DIR: (modName: string) => `/data/work/${modName}/sdc/inputs`,
    SDC_OUTPUT_DIR: (modName: string) => `/data/work/${modName}/sdc/outputs`,
};

// 路径验证函数
export const validatePath = {
    // 验证Template路径是否安全
    isValidTemplatePath: (toolId: string, filename: string): boolean => {
        const filePath = TEMPLATE_PATHS.getTemplateFilePath(toolId, filename);
        const allowedDir = TEMPLATE_PATHS.getToolTemplatePath(toolId);

        const resolvedPath = path.resolve(filePath);
        const resolvedAllowedDir = path.resolve(allowedDir);

        return resolvedPath.startsWith(resolvedAllowedDir);
    },
    
    // 验证文件名是否安全（防止路径遍历）
    isSafeFilename: (filename: string): boolean => {
        // 不允许包含路径分隔符和特殊字符
        const dangerousChars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|'];
        return !dangerousChars.some(char => filename.includes(char));
    }
};

export default {
    TEMPLATE_PATHS,
    TEMP_PATHS,
    OSS_PATHS,
    CONTAINER_PATHS,
    validatePath
};
