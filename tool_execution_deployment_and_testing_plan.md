# 工具执行业务生产部署与测试方案

本文档为"芯片设计在线工具集"的工具执行业务功能模块，提供一份详细、完整的生产环境集成部署与全面测试的方案。

---

## 1. 生产环境集成与部署方案

本方案基于单台ECS实例的架构，详细说明了从环境准备到服务上线的完整步骤。

### 1.1 前置条件与环境准备

在部署之前，请确保已准备好以下阿里云资源和配置：

1.  **阿里云账户**: 具备所有相关资源的操作权限。
2.  **ECS实例**:
    *   购买一台ECS实例（建议规格：4vCPU/32GB或8vCPU/64GB，根据预期负载选择）。
    *   操作系统：选择Ubuntu 22.04 LTS或CentOS 8+。
    *   网络：配置专有网络VPC，并规划好安全组规则。
3.  **RDS for PostgreSQL**: 创建一个PostgreSQL实例，记录其内网连接地址、端口、用户名和密码。
4.  **ApsaraDB for Redis**: 创建一个Redis实例，记录其内网连接地址、端口和密码。
5.  **对象存储 (OSS)**:
    *   创建四个**私有**Bucket：
        *   `your-app-user-input`: 存储用户上传的输入文件。
        *   `your-app-job-results`: 存储任务成功后的结果文件。
        *   `your-app-job-logs`: 存储任务执行的日志文件。
        *   `your-app-static-assets` (可选): 用于托管前端静态文件。
6.  **容器镜像服务 (ACR)**: 创建一个私有镜像仓库，用于存储后端服务的Docker镜像。
7.  **资源访问管理 (RAM)**:
    *   创建一个RAM用户或为ECS实例赋予RAM角色，并授予其访问上述OSS、ACR等服务的最小必要权限。记录其`AccessKeyId`和`AccessKeySecret`。
8.  **域名与SSL证书**: 准备好应用的域名，并为其申请SSL证书以启用HTTPS。

### 1.2 环境变量配置

在ECS实例上，为后端服务和Python Worker创建`.env`文件，并配置以下变量：

```ini
# backend/src/.env

# Application
PORT=8080
FRONTEND_URL=https://your-domain.com

# Database & Redis
DATABASE_URL="postgresql://<user>:<password>@<rds-internal-host>:<port>/<db-name>"
REDIS_URL="redis://:<password>@<redis-internal-host>:<port>/0"

# Security
JWT_SECRET=<your-strong-jwt-secret>

# Aliyun Credentials
ALIYUN_ACCESS_KEY_ID=<your-ram-user-access-key-id>
ALIYUN_ACCESS_KEY_SECRET=<your-ram-user-access-key-secret>

# Aliyun OSS
OSS_REGION=cn-hangzhou
OSS_BUCKET_USER_INPUT=your-app-user-input
OSS_BUCKET_JOB_RESULTS=your-app-job-results
OSS_BUCKET_JOB_LOGS=your-app-job-logs

# Python Worker (on the same ECS, can share env or have its own)
ECS_TOTAL_CPU=8
ECS_TOTAL_MEMORY_GB=64
JOB_CPU_REQUEST=2
JOB_MEMORY_REQUEST_GB=16
WORKER_ID=worker-main-01
ECS_INSTANCE_ID=<your-ecs-instance-id>
```

### 1.3 构建流程

1.  **前端应用**:
    ```bash
    cd frontend
    npm install
    npm run build
    ```
    这将在`frontend/dist`目录下生成生产环境的静态文件。

2.  **后端服务 (Docker镜像)**:
    *   在`backend`目录下创建一个`Dockerfile`:
        ```Dockerfile
        # Dockerfile for backend
        FROM node:18-alpine
        WORKDIR /app
        COPY package*.json ./
        RUN npm install --production
        COPY . .
        # If using Prisma
        RUN npx prisma generate
        CMD ["node", "./dist/index.js"]
        ```
    *   构建并推送到ACR：
        ```bash
        # 登录ACR
        docker login --username=<your-acr-username> registry.cn-hangzhou.aliyuncs.com

        # 构建镜像
        docker build -t registry.cn-hangzhou.aliyuncs.com/your-namespace/logiccore-backend:v1.0 .

        # 推送镜像
        docker push registry.cn-hangzhou.aliyuncs.com/your-namespace/logiccore-backend:v1.0
        ```

### 1.4 部署步骤

1.  **数据库迁移**:
    *   在本地或ECS实例上，确保有Node.js和Prisma CLI环境。
    *   执行生产迁移：
        ```bash
        npx prisma migrate deploy --schema=./backend/prisma/schema.prisma
        ```
    *   **数据填充**: 手动或通过脚本向`Tool`表中插入需要支持的工具及其Docker镜像地址等元数据。

2.  **ECS实例初始化**:
    *   SSH登录到您的ECS实例。
    *   **安装Docker**:
        ```bash
        sudo apt-get update
        sudo apt-get install -y docker.io
        sudo systemctl start docker
        sudo systemctl enable docker
        ```
    *   **安装Python环境和依赖**:
        ```bash
        sudo apt-get install -y python3 python3-pip
        cd backend # (将worker脚本和requirements.txt复制到此)
        pip3 install -r requirements.txt
        ```

3.  **部署后端和Worker**:
    *   **登录ACR**:
        ```bash
        sudo docker login --username=<your-acr-username> registry.cn-hangzhou.aliyuncs.com
        ```
    *   **运行后端容器**:
        ```bash
        sudo docker run -d --name logiccore-backend \
          -p 8080:8080 \
          --restart always \
          -v /path/to/your/env:/app/.env \ # 挂载环境变量文件
          registry.cn-hangzhou.aliyuncs.com/your-namespace/logiccore-backend:v1.0
        ```
    *   **运行Python Worker**:
        *   建议使用`systemd`来管理Worker进程，确保其持久运行和开机自启动。
        *   创建`systemd`服务文件 `/etc/systemd/system/tool-worker.service`:
            ```ini
            [Unit]
            Description=LogicCore Tool Worker
            After=network.target docker.service

            [Service]
            User=root # Or a non-root user
            WorkingDirectory=/path/to/backend/src/workers
            ExecStart=/usr/bin/python3 toolWorker.py
            Restart=always
            EnvironmentFile=/path/to/your/env # 加载环境变量

            [Install]
            WantedBy=multi-user.target
            ```
        *   启动并启用服务：
            ```bash
            sudo systemctl daemon-reload
            sudo systemctl start tool-worker
            sudo systemctl enable tool-worker
            ```

4.  **配置Nginx反向代理 (可选但推荐)**:
    *   安装Nginx。
    *   配置Nginx将来自公网的HTTPS流量（443端口）反向代理到本地的后端服务（8080端口），并配置SSL证书。

---

## 2. 全面测试方案

### 2.1 单元与集成测试

*   **后端 (Jest/Vitest)**:
    *   **单元测试**: 为`task.controller.ts`中的核心逻辑编写测试，模拟`req`和`res`对象，断言响应状态码和内容。
    *   **集成测试**: 测试API路由与数据库的交互，例如，验证`submitTask`是否成功在数据库中创建了`Task`记录。
*   **Worker (Pytest)**:
    *   为`toolWorker.py`中的资源管理、任务处理逻辑编写单元测试。
    *   使用`mock`库模拟Docker和OSS的交互，验证核心流程。

### 2.2 端到端(E2E)测试场景

| 测试模块 | 测试场景 | 预期结果 |
| :--- | :--- | :--- |
| **任务提交 (Happy Path)** | 1. 用户登录。 2. 填写有效参数并上传文件。 3. 点击"提交任务"。 | 1. 后端返回202状态码和`taskId`。 2. 前端显示任务状态为"PENDING"。 3. 数据库中创建了对应任务记录。 4. Redis `task_queue`中加入了该`taskId`。 |
| **任务执行与状态轮询** | 1. Worker成功从队列中获取任务。 2. ECS资源充足。 3. 前端周期性请求状态API。 | 1. 任务状态在数据库中变为"RUNNING"。 2. 前端UI同步更新为"RUNNING"。 3. Worker启动Docker容器。 4. 任务完成后，状态变为"COMPLETED"。 5. 前端UI更新，显示下载按钮。 |
| **结果下载** | 1. 任务状态为"COMPLETED"。 2. 用户点击"下载结果"或"下载日志"。 | 1. 前端请求`download_url`接口。 2. 后端返回一个有效的、有时效性的OSS预签名URL。 3. 浏览器在新标签页中开始下载文件。 |
| **任务失败处理** | 1. 提交的任务因内部错误而执行失败 (模拟工具返回非零退出码)。 | 1. 任务状态变为"FAILED"。 2. 数据库中记录了`errorMessage`和`logOssPath`。 3. 前端UI更新为"FAILED"，并显示错误信息和日志下载按钮。 |
| **资源不足处理** | 1. 同时提交的任务数超过ECS资源上限 (例如，提交第5个任务)。 | 1. 前4个任务正常运行。 2. 第5个任务进入"PENDING"状态，在Redis队列中等待。 3. 当一个任务完成后，Worker能正确获取并执行第5个任务。 |
| **输入验证** | 1. 提交任务时不提供`toolId`。 2. 提交任务时参数格式错误。 | 1. 后端返回400 Bad Request错误。 2. 前端显示清晰的错误提示。 |
| **权限与安全** | 1. 未登录用户尝试访问任何任务API。 2. 用户A尝试查询或下载用户B的任务。 | 1. 后端返回401 Unauthorized错误。 2. 后端返回404 Not Found或403 Forbidden错误。 |

### 2.3 安全专项测试

1.  **STS凭证与预签名URL**:
    *   验证生成的预签名URL确实具有预设的过期时间。
    *   尝试使用过期的URL访问OSS资源，应返回权限错误。
2.  **文件上传安全**:
    *   尝试上传超大文件，预期应被后端或Nginx拒绝。
    *   尝试上传恶意脚本文件（如`.sh`, `.php`），验证后端是否仅处理预期的文件类型，并且在执行端没有造成安全问题。
3.  **参数注入**:
    *   在工具参数中输入特殊字符（如`;`, `|`, `&`, `$(ls)`），验证后端和Worker是否能正确处理，没有发生命令注入。

### 2.4 性能与稳定性测试

1.  **长时任务**: 运行一个设计为长时间执行的工具任务，监控Worker和后端服务的内存和CPU使用情况，检查是否存在内存泄漏。
2.  **并发测试**: 使用工具（如JMeter, k6）模拟多个用户同时提交任务，观察系统的响应时间和资源使用率。
3.  **服务重启测试**:
    *   手动重启ECS实例，验证后端和Worker服务是否能通过`systemd`和`docker --restart always`策略自动恢复。
    *   手动重启Redis或PostgreSQL实例（如果使用云服务，可模拟故障切换），验证服务的重连机制。

通过以上部署和测试流程，可以最大限度地保证工具执行业务功能在生产环境中的稳定性、安全性和性能。 