// This is a mock service for fetching subscription data.
// In a real application, this would make a request to the backend API.

// Simulate a network delay
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const getMySubscription = async () => {
  await sleep(1000); // Simulate network latency

  // Mock a successful response for an active subscription
  const mockResponse = {
    data: {
      plan: {
        name: '专业版 (Pro)',
      },
      status: 'ACTIVE',
      // Set end date to one year from now
      endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
      autoRenew: true,
    },
  };

  // To simulate a user with no subscription, return a 404-like error or empty data
  // For example:
  // throw { response: { status: 404, data: { message: "Subscription not found." } } };

  return mockResponse;
};

export const cancelSubscription = async () => {
    await sleep(1000);
    // 取消订阅请求
    // Just return a success message
    return {
        data: {
            message: "Subscription cancelled successfully."
        }
    }
}; 