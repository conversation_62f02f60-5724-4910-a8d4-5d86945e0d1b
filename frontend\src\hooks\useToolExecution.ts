import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/auth.context';
import { useNavigate, useLocation } from 'react-router-dom';
import api from '@/services/api'; // 使用我们封装的axios实例
import { useToast } from "@/hooks/use-toast";
import axios from 'axios';

// Defines the shape of the status object returned by the hook
export type TaskStatus = {
    status: 'IDLE' | 'SUBMITTING' | 'POLLING' | 'COMPLETED' | 'FAILED';
    progress: number;
    taskId: string | null;
    resultUrl: string | null;
    logUrl: string | null;
    errorMessage: string | null;
}

// Defines the parameters for the submitTask function
export interface SubmitTaskParams {
    toolId: string;
    parameters: Record<string, any>;
    inputFile?: File | null;
    inputFiles?: File[]; // 支持多文件上传
}

const POLLING_INTERVAL = 3000;

/**
 * A custom hook to manage the lifecycle of a tool execution task.
 * It handles task submission, status polling, and result management.
 * @returns An object containing the task status and functions to interact with the task.
 */
export function useToolExecution() {
    const { isAuthenticated } = useAuth();
    const { toast } = useToast();
    const navigate = useNavigate();
    const location = useLocation();

    const [taskStatus, setTaskStatus] = useState<TaskStatus>({
        status: 'IDLE',
        progress: 0,
        taskId: null,
        resultUrl: null,
        logUrl: null,
        errorMessage: null,
    });
    
    const pollingRef = useRef<NodeJS.Timeout | null>(null);

    const cleanup = useCallback(() => {
        if (pollingRef.current) {
            clearInterval(pollingRef.current);
            pollingRef.current = null;
        }
    }, []);
    
    const handleApiError = (error: any, defaultMessage: string) => {
        let message = defaultMessage;
        if (error?.response?.data?.message) {
            message = error.response.data.message;
        } else if (error instanceof Error) {
            message = error.message;
        }
        setTaskStatus(prev => ({ ...prev, status: 'FAILED', errorMessage: message }));
        toast({
            title: "发生错误",
            description: message,
            variant: "destructive",
        });
        cleanup();
    };

    const submitTask = useCallback(async ({ toolId, parameters, inputFile, inputFiles }: SubmitTaskParams) => {
        if (!isAuthenticated) {
            navigate('/auth/login', { state: { from: location } });
            return;
        }

        setTaskStatus({
            status: 'SUBMITTING',
            progress: 0,
            taskId: null,
            resultUrl: null,
            logUrl: null,
            errorMessage: null,
        });

        try {
            const formData = new FormData();
            formData.append('toolId', toolId);
            formData.append('parameters', JSON.stringify(parameters));

            // 支持多文件上传
            if (inputFiles && inputFiles.length > 0) {
                inputFiles.forEach((file, index) => {
                    formData.append('files', file);
                });
            } else if (inputFile) {
                formData.append('files', inputFile);
            }

            const response = await api.post('/tasks', formData);
            const { taskId } = response.data;

            // 提交成功后立即显示成功提示
            toast({
                title: "任务提交成功！",
                description: `任务ID: ${taskId}，正在开始执行...`,
                duration: 3000,
            });

            setTaskStatus(prev => ({ ...prev, status: 'POLLING', taskId, progress: 0 }));

        } catch (error) {
            // 处理特定的权限错误
            if (axios.isAxiosError(error) && error.response) {
                const status = error.response.status;
                const message = error.response.data?.message || "任务提交失败";

                if (status === 401) {
                    handleApiError(error, "请先登录后再提交任务");
                    navigate('/auth/login', { state: { from: location } });
                    return;
                } else if (status === 403) {
                    if (message.includes('subscription')) {
                        handleApiError(error, "您需要有效的订阅才能使用此功能，请升级您的会员计划");
                    } else if (message.includes('daily limit')) {
                        handleApiError(error, "您今日的任务配额已用完，请明天再试或升级会员计划");
                    } else {
                        handleApiError(error, message);
                    }
                    return;
                }
            }
            handleApiError(error, "任务提交失败");
        }
    }, [isAuthenticated, navigate, location, cleanup]);

    const pollTaskStatus = useCallback(async (taskId: string) => {
        try {
            const response = await api.get(`/tasks/${taskId}/status`);
            const { status, progress, resultUrl, logUrl } = response.data;

            if (status === 'COMPLETED' || status === 'FAILED') {
                setTaskStatus(prev => ({ ...prev, status, resultUrl, logUrl, progress: 100 }));
                cleanup();
            } else {
                setTaskStatus(prev => ({ ...prev, status, progress, resultUrl, logUrl }));
            }
        } catch (error) {
            handleApiError(error, "获取任务状态失败");
        }
    }, [toast, cleanup]);

    useEffect(() => {
        if (taskStatus.status === 'POLLING' && taskStatus.taskId && !pollingRef.current) {
            pollingRef.current = setInterval(() => {
                pollTaskStatus(taskStatus.taskId!);
            }, POLLING_INTERVAL);
        }
        
        return () => {
            if (taskStatus.status !== 'POLLING' && pollingRef.current) {
                cleanup();
            }
        };
    }, [taskStatus.status, taskStatus.taskId, pollTaskStatus, cleanup]);

    const handleDownload = useCallback(async (type: 'result' | 'log') => {
        const url = type === 'result' ? taskStatus.resultUrl : taskStatus.logUrl;
        if (!url) {
            toast({ title: "下载链接不存在", variant: "destructive" });
            return;
        }
        window.open(url, '_blank');
    }, [taskStatus.resultUrl, taskStatus.logUrl, toast]);

    const resetTask = useCallback(() => {
        cleanup();
        setTaskStatus({
            status: 'IDLE',
            progress: 0,
            taskId: null,
            resultUrl: null,
            logUrl: null,
            errorMessage: null,
        });
    }, [cleanup]);

    return { taskStatus, submitTask, handleDownload, resetTask };
} 