import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/auth.context';
import { useNavigate, useLocation } from 'react-router-dom';
import api from '@/services/api'; // 使用我们封装的axios实例
import { useToast } from "@/hooks/use-toast";

// Defines the shape of the status object returned by the hook
export type TaskStatus = {
    status: 'IDLE' | 'SUBMITTING' | 'POLLING' | 'COMPLETED' | 'FAILED';
    progress: number;
    taskId: string | null;
    resultUrl: string | null;
    logUrl: string | null;
    errorMessage: string | null;
}

// Defines the parameters for the submitTask function
export interface SubmitTaskParams {
    toolId: string;
    parameters: Record<string, any>;
    inputFile?: File | null;
}

const POLLING_INTERVAL = 3000;

/**
 * A custom hook to manage the lifecycle of a tool execution task.
 * It handles task submission, status polling, and result management.
 * @returns An object containing the task status and functions to interact with the task.
 */
export function useToolExecution() {
    const { isAuthenticated } = useAuth();
    const { toast } = useToast();
    const navigate = useNavigate();
    const location = useLocation();

    const [taskStatus, setTaskStatus] = useState<TaskStatus>({
        status: 'IDLE',
        progress: 0,
        taskId: null,
        resultUrl: null,
        logUrl: null,
        errorMessage: null,
    });
    
    const pollingRef = useRef<NodeJS.Timeout | null>(null);

    const cleanup = useCallback(() => {
        if (pollingRef.current) {
            clearInterval(pollingRef.current);
            pollingRef.current = null;
        }
    }, []);
    
    const handleApiError = (error: any, defaultMessage: string) => {
        let message = defaultMessage;
        if (error?.response?.data?.message) {
            message = error.response.data.message;
        } else if (error instanceof Error) {
            message = error.message;
        }
        setTaskStatus(prev => ({ ...prev, status: 'FAILED', errorMessage: message }));
        toast({
            title: "发生错误",
            description: message,
            variant: "destructive",
        });
        cleanup();
    };

    const submitTask = useCallback(async ({ toolId, parameters, inputFile }: SubmitTaskParams) => {
        if (!isAuthenticated) {
            navigate('/auth/login', { state: { from: location } });
            return;
        }

        setTaskStatus({
            status: 'SUBMITTING',
            progress: 0,
            taskId: null,
            resultUrl: null,
            logUrl: null,
            errorMessage: null,
        });

        try {
            const formData = new FormData();
            formData.append('toolId', toolId);
            formData.append('parameters', JSON.stringify(parameters));
            if (inputFile) {
                formData.append('inputFile', inputFile);
            }

            const response = await api.post('/tasks', formData);
            const { taskId } = response.data;
            setTaskStatus(prev => ({ ...prev, status: 'POLLING', taskId, progress: 0 }));

        } catch (error) {
            handleApiError(error, "任务提交失败");
        }
    }, [isAuthenticated, navigate, location, cleanup]);

    const pollTaskStatus = useCallback(async (taskId: string) => {
        try {
            const response = await api.get(`/tasks/${taskId}/status`);
            const { status, progress, resultUrl, logUrl } = response.data;

            if (status === 'COMPLETED' || status === 'FAILED') {
                setTaskStatus(prev => ({ ...prev, status, resultUrl, logUrl, progress: 100 }));
                cleanup();
            } else {
                setTaskStatus(prev => ({ ...prev, status, progress, resultUrl, logUrl }));
            }
        } catch (error) {
            handleApiError(error, "获取任务状态失败");
        }
    }, [toast, cleanup]);

    useEffect(() => {
        if (taskStatus.status === 'POLLING' && taskStatus.taskId && !pollingRef.current) {
            pollingRef.current = setInterval(() => {
                pollTaskStatus(taskStatus.taskId!);
            }, POLLING_INTERVAL);
        }
        
        return () => {
            if (taskStatus.status !== 'POLLING' && pollingRef.current) {
                cleanup();
            }
        };
    }, [taskStatus.status, taskStatus.taskId, pollTaskStatus, cleanup]);

    const handleDownload = useCallback(async (type: 'result' | 'log') => {
        const url = type === 'result' ? taskStatus.resultUrl : taskStatus.logUrl;
        if (!url) {
            toast({ title: "下载链接不存在", variant: "destructive" });
            return;
        }
        window.open(url, '_blank');
    }, [taskStatus.resultUrl, taskStatus.logUrl, toast]);

    const resetTask = useCallback(() => {
        cleanup();
        setTaskStatus({
            status: 'IDLE',
            progress: 0,
            taskId: null,
            resultUrl: null,
            logUrl: null,
            errorMessage: null,
        });
    }, [cleanup]);

    return { taskStatus, submitTask, handleDownload, resetTask };
} 