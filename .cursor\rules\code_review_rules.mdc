---
description: 
globs: 
alwaysApply: false
---
# 代码评审规则

## 评审基本原则

1. **目标与态度**：
   - 代码评审的主要目标是提高代码质量和团队能力
   - 保持积极、建设性和尊重的态度
   - 评论应针对代码而非开发者个人
   - 既指出问题，也肯定优点

2. **评审范围**：
   - 关注代码的正确性、性能和安全性
   - 检查代码是否符合项目的规范和架构
   - 评估代码的可读性和可维护性
   - 确认是否有适当的错误处理和边界情况考虑

3. **时间管理**：
   - 优先处理代码评审请求，避免成为项目瓶颈
   - 大型变更可分段评审
   - 每次评审不应超过1小时，避免疲劳
   - 如果需要深入讨论，考虑面对面沟通

## 评审检查项

1. **功能完整性**：
   - 代码是否完全实现了需求
   - 是否考虑了所有用户场景和边界情况
   - 功能实现是否直观且符合用户期望
   - 是否有适当的错误处理机制

2. **代码质量**：
   - 代码是否遵循项目的编码规范
   - 是否有不必要的复杂性或冗余
   - 命名是否清晰且一致
   - 是否有可能的重构机会

3. **性能考量**：
   - 是否有性能瓶颈或资源密集型操作
   - 查询和算法是否高效
   - 是否考虑了缓存策略
   - 大数据集的处理是否合理

4. **安全性**：
   - 是否存在安全漏洞
   - 用户输入是否得到适当验证和清洁
   - 敏感数据是否安全处理
   - 认证和授权逻辑是否正确

5. **可测试性**：
   - 代码是否易于测试
   - 是否有适当的单元测试覆盖
   - 测试是否涵盖主要功能和边界情况
   - 是否使用了测试模拟和存根

6. **文档和注释**：
   - 复杂逻辑是否有适当的注释
   - API和公共接口是否有文档
   - 是否解释了非常规决策和实现
   - 注释是否与代码保持同步

## 评审流程

1. **提交前准备**：
   - 开发者应自行检查代码符合规范
   - 确保所有测试通过
   - 提供清晰的变更描述和上下文
   - 对复杂变更提供实现说明

2. **评审过程**：
   - 评审者先理解变更目的和上下文
   - 按逻辑顺序审查代码
   - 提供具体、可操作的反馈
   - 区分必须修改和建议性意见

3. **反馈处理**：
   - 开发者应对每条反馈作出响应
   - 对有争议的问题进行建设性讨论
   - 不必对每个建议都做修改，但应说明理由
   - 对一致同意的问题进行修复

4. **最终确认**：
   - 评审者确认问题已解决
   - 检查是否引入了新问题
   - 确认代码质量满足项目标准
   - 批准合并或提供最终反馈

## 沟通指南

1. **提问而非命令**：
   - "这段代码能否考虑使用X模式来提高可读性？"
   - 而非"使用X模式重写这段代码"

2. **解释理由**：
   - "建议使用Promise.all处理这些并行请求，这样可以减少总体响应时间"
   - 而非"用Promise.all替换这些请求"

3. **提供示例**：
   - 对于复杂的建议，提供简短的代码示例
   - 引用文档或最佳实践资源

4. **积极肯定**：
   - 指出代码中做得好的部分
   - "这个错误处理实现得很全面，覆盖了所有可能的情况"

5. **避免绝对措辞**：
   - "考虑简化这个函数"而非"这个函数太复杂了"
   - "这种方法可能有性能问题"而非"这种方法效率很低"

## 工具使用

1. **自动化检查**：
   - 使用ESLint等静态代码分析工具
   - 配置CI/CD流程中的自动检查
   - 使用代码格式化工具确保一致性

2. **评审效率**：
   - 熟悉版本控制系统的代码评审功能
   - 使用工具比较不同版本
   - 学习使用键盘快捷键提高效率