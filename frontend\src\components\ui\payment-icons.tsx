import React from 'react';

// In a real application, you would use actual SVG icons for the logos.
// For now, we are using placeholder components.

export const AlipayIcon: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24Z" fill="#1677FF"/>
    <path d="M36.143 28.1961L32.227 12H27.999L24.846 24.123L21.684 12H17.456L13.54 28.1961L17.768 28.7291L19.299 23.9931L21.399 29.4121H22.759L25.962 19.3321L28.875 29.4121H30.347L31.878 24.6031L34.357 28.7291L36.143 28.1961ZM15.82 30.5891L18.435 34.6901H21.2L19.462 31.6931L15.82 30.5891Z" fill="white"/>
  </svg>
);

export const WechatPayIcon: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48ZM20.3121 17.512C19.2321 17.512 18.3371 18.402 18.3371 19.49C18.3371 20.578 19.2321 21.467 20.3121 21.467C21.3921 21.467 22.2871 20.578 22.2871 19.49C22.2871 18.402 21.3921 17.512 20.3121 17.512ZM13.8441 23.978C12.7631 23.978 11.8681 24.867 11.8681 25.955C11.8681 27.043 12.7631 27.933 13.8441 27.933C14.9241 27.933 15.8191 27.043 15.8191 25.955C15.8191 24.867 14.9241 23.978 13.8441 23.978ZM30.9592 12C28.4712 12 26.2412 13.433 25.1052 15.545C25.9272 16.035 26.5492 16.784 26.7912 17.669C27.8382 16.059 29.6192 15.018 31.6442 15.018C34.7822 15.018 37.1192 17.54 36.9312 20.662C36.9312 20.817 36.9202 20.973 36.9022 21.129C38.9952 21.411 41.5002 23.235 41.5002 26.11C41.5002 28.53 39.7362 30.132 37.6062 30.59C37.0692 33.159 34.6962 35 31.8862 35C28.9892 35 26.6522 33.023 26.5392 20.403C26.5392 19.752 26.5052 19.11 26.4442 18.483C24.4782 19.344 22.8442 21.192 22.5642 23.515C22.5032 24.013 22.4692 24.52 22.4692 25.034C22.4692 28.57 25.2952 32.406 28.5032 32.406C32.3232 32.406 35.1092 28.899 35.1092 25.955C35.1092 23.868 33.8662 23.102 32.4462 22.753L32.4462 22.685C33.3412 22.254 34.0262 21.411 34.0262 20.358C34.0262 18.846 32.8872 17.843 31.4242 17.843C30.2942 17.843 29.2892 18.524 28.9082 19.527C28.4792 16.517 25.4852 14.885 24.0002 14.885C22.5152 14.885 20.5762 16.083 19.5392 17.136L19.4622 17.158C18.5062 16.14 17.0602 15.346 15.4242 15.346C12.1812 15.346 9.50022 17.96 9.50022 21.233C9.50022 24.028 11.5332 26.496 14.1222 26.853C14.0752 27.202 14.0442 27.56 14.0442 27.925C14.0442 30.728 15.7512 33.004 18.2392 33.886C17.5152 35.251 15.9392 36 14.0442 36C10.7182 36 8.00022 33.314 8.00022 30C8.00022 28.452 8.70622 27.043 9.87322 26.069C7.43022 25.066 6.50022 22.685 6.50022 20.311C6.50022 16.21 9.83722 13 14.0442 13C17.0782 13 19.6482 14.623 20.5432 16.923C21.6522 14.053 24.4692 12 27.9592 12H30.9592Z" fill="#00C700"/>
  </svg>
); 