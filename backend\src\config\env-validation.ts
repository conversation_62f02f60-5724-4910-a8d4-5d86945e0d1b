import { z } from 'zod';

// 环境变量验证schema
const envSchema = z.object({
  // 基础配置
  PORT: z.string().regex(/^\d+$/, 'PORT must be a number').default('8080'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // 数据库配置
  DATABASE_URL: z.string().url('DATABASE_URL must be a valid URL'),
  REDIS_URL: z.string().url('REDIS_URL must be a valid URL'),
  
  // JWT配置
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().regex(/^\d+[dhms]$/, 'JWT_EXPIRES_IN must be in format like "1d", "24h", "1440m"').default('1d'),
  COOKIE_SECRET: z.string().min(32, 'COOKIE_SECRET must be at least 32 characters'),
  
  // 前端配置
  FRONTEND_URL: z.string().url('FRONTEND_URL must be a valid URL').default('http://localhost:3000'),
  
  // 支付配置 - 严格验证，确保生产环境的安全性
  ALIPAY_APP_ID: z.string().min(1, 'ALIPAY_APP_ID is required'),
  ALIPAY_APP_PRIVATE_KEY_PATH: z.string().min(1, 'ALIPAY_APP_PRIVATE_KEY_PATH is required'),
  ALIPAY_PUBLIC_KEY_PATH: z.string().min(1, 'ALIPAY_PUBLIC_KEY_PATH is required'),
  ALIPAY_NOTIFY_URL: z.string().url('ALIPAY_NOTIFY_URL must be a valid URL'),
  
  WECHAT_APP_ID: z.string().min(1, 'WECHAT_APP_ID is required'),
  WECHAT_MCH_ID: z.string().min(1, 'WECHAT_MCH_ID is required'),
  WECHAT_CERTIFICATE_SERIAL_NO: z.string().min(1, 'WECHAT_CERTIFICATE_SERIAL_NO is required'),
  WECHAT_API_V3_KEY: z.string().min(32, 'WECHAT_API_V3_KEY must be at least 32 characters'),
  WECHAT_NOTIFY_URL: z.string().url('WECHAT_NOTIFY_URL must be a valid URL'),
  API_BASE_URL: z.string().url('API_BASE_URL must be a valid URL').default('http://localhost:8080'),
  
  // 阿里云配置
  ALIYUN_ACCESS_KEY_ID: z.string().min(1, 'ALIYUN_ACCESS_KEY_ID is required'),
  ALIYUN_ACCESS_KEY_SECRET: z.string().min(1, 'ALIYUN_ACCESS_KEY_SECRET is required'),
  ALIYUN_RAM_ROLE_ARN: z.string().min(1, 'ALIYUN_RAM_ROLE_ARN is required'),
  ALIYUN_STS_REGION: z.string().default('cn-hangzhou'),
  
  // OSS配置
  OSS_REGION: z.string().default('cn-hangzhou'),
  OSS_BUCKET_USER_INPUT: z.string().min(1, 'OSS_BUCKET_USER_INPUT is required'),
  OSS_BUCKET_JOB_RESULTS: z.string().min(1, 'OSS_BUCKET_JOB_RESULTS is required'),
  OSS_BUCKET_JOB_LOGS: z.string().min(1, 'OSS_BUCKET_JOB_LOGS is required'),
  
  // Worker配置
  ECS_TOTAL_CPU: z.string().regex(/^\d+$/, 'ECS_TOTAL_CPU must be a number').default('8'),
  ECS_TOTAL_MEMORY_GB: z.string().regex(/^\d+$/, 'ECS_TOTAL_MEMORY_GB must be a number').default('64'),
  JOB_CPU_REQUEST: z.string().regex(/^\d+$/, 'JOB_CPU_REQUEST must be a number').default('2'),
  JOB_MEMORY_REQUEST_GB: z.string().regex(/^\d+$/, 'JOB_MEMORY_REQUEST_GB must be a number').default('16'),
  WORKER_ID: z.string().default('worker-main-01'),
  ECS_INSTANCE_ID: z.string().default('ecs-single-node-dev'),
  TASK_QUEUE_NAME: z.string().default('task_queue'),
});

// 验证环境变量
const validateEnv = () => {
  try {
    const result = envSchema.parse(process.env);
    
    // 转换数字类型
    return {
      ...result,
      PORT: parseInt(result.PORT),
      ECS_TOTAL_CPU: parseInt(result.ECS_TOTAL_CPU),
      ECS_TOTAL_MEMORY_GB: parseInt(result.ECS_TOTAL_MEMORY_GB),
      JOB_CPU_REQUEST: parseInt(result.JOB_CPU_REQUEST),
      JOB_MEMORY_REQUEST_GB: parseInt(result.JOB_MEMORY_REQUEST_GB),
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Environment variable validation failed:');
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`);
      });
      console.error('\nPlease check your .env file and ensure all required variables are set correctly.');
      process.exit(1);
    }
    throw error;
  }
};

export const env = validateEnv();

// 在开发环境下显示配置摘要
if (env.NODE_ENV === 'development') {
  console.log('✅ Environment variables validated successfully');
  console.log('📊 Configuration summary:');
  console.log(`  - Environment: ${env.NODE_ENV}`);
  console.log(`  - Port: ${env.PORT}`);
  console.log(`  - Database: ${env.DATABASE_URL.split('@')[1] || 'configured'}`);
  console.log(`  - Redis: ${env.REDIS_URL.split('@')[1] || 'configured'}`);
  console.log(`  - Frontend URL: ${env.FRONTEND_URL}`);
  console.log(`  - Worker ID: ${env.WORKER_ID}`);
}

// 类型定义
export type EnvConfig = typeof env; 