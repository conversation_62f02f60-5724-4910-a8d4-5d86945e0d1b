// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
}

model User {
  id                 String        @id @default(cuid())
  email              String        @unique
  password           String
  name               String?
  avatar             String?
  isVerified         Boolean       @default(false)
  role               Role          @default(USER)
  verificationToken  String?       @unique
  resetPasswordToken String?       @unique
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt

  tasks        Task[]
  orders       Order[]
  subscription Subscription?
  auditLogs    AuditLog[]
}

model Plan {
  id            String   @id @default(cuid())
  name          String   @unique
  description   String?
  priceMonth    Decimal  @map("price_month") @db.Decimal(10, 2)
  priceYear     Decimal  @map("price_year") @db.Decimal(10, 2)
  features      Json // e.g., { "toolRunsPerDay": 10, "parallelTasks": 1 }
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  subscriptions Subscription[]
  orders        Order[]
}

model Tool {
  id             String   @id @default(cuid())
  name           String   @unique
  description    String
  inputSchema    Json
  dockerImage    String
  version        String
  configTemplate Json?
  isPublic       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  tasks Task[]
}

model AuditLog {
  id        String   @id @default(cuid())
  actorId   String
  actor     User     @relation(fields: [actorId], references: [id])
  action    String
  targetId  String
  details   Json?
  createdAt DateTime @default(now())

  @@index([actorId])
  @@index([action])
}

enum TaskStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
}

model Task {
  id           String     @id @default(cuid())
  userId       String
  toolId       String
  status       TaskStatus @default(PENDING)
  parameters   Json
  inputFile    String?
  outputFile   String?
  logFile      String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  user User @relation(fields: [userId], references: [id])
  tool Tool @relation(fields: [toolId], references: [id])

  @@index([userId, status])
  @@index([userId, createdAt])
}

enum OrderStatus {
  PENDING
  PAID
  FAILED
  CANCELED
}

enum PaymentMethod {
  WECHAT
  ALIPAY
}

model Order {
  id            String        @id @default(cuid())
  userId        String
  planId        String
  status        OrderStatus   @default(PENDING)
  amount        Decimal       @db.Decimal(10, 2)
  paymentMethod PaymentMethod
  paymentId     String? // payment gateway's transaction id
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  user         User         @relation(fields: [userId], references: [id])
  plan         Plan         @relation(fields: [planId], references: [id])
  subscription Subscription?

  @@index([userId, status])
  @@index([userId, createdAt])
  @@index([status, createdAt])
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  EXPIRED
}

model Subscription {
  id        String             @id @default(cuid())
  userId    String             @unique
  planId    String
  orderId   String             @unique
  status    SubscriptionStatus @default(ACTIVE)
  startDate DateTime
  endDate   DateTime
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  user  User  @relation(fields: [userId], references: [id])
  plan  Plan  @relation(fields: [planId], references: [id])
  order Order @relation(fields: [orderId], references: [id])

  @@index([status, endDate])
  @@index([userId, status])
}
