import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupUsers() {
  try {
    const emailsToDelete = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ];

    console.log('Deleting users with emails:', emailsToDelete);
    
    const result = await prisma.user.deleteMany({
      where: {
        email: {
          in: emailsToDelete
        }
      }
    });

    console.log(`Deleted ${result.count} users`);
    
    // 列出剩余用户
    const remainingUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        isVerified: true,
        createdAt: true
      }
    });
    
    console.log('Remaining users:', remainingUsers);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupUsers();
