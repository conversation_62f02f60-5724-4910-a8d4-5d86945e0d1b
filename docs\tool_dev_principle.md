# EDA工具开发原则与规范

## 📋 概述

基于SDC工具的成功开发经验，本文档总结了EDA工具开发的核心原则、设计模式和最佳实践，为后续工具（如UPF工具）的开发提供指导。

## 🎯 核心开发原则

### 1. 代码复用优先 (Code Reuse First)
- **最大化复用现有代码逻辑**：新工具应复用80%以上的现有代码
- **组件化设计**：将通用功能抽象为可复用组件
- **接口标准化**：保持API接口的一致性和兼容性

### 2. 架构一致性 (Architecture Consistency)
- **技术栈统一**：前端React+TypeScript，后端Node.js+Express+Prisma
- **目录结构标准化**：遵循既定的项目目录结构
- **设计模式统一**：使用相同的设计模式和编程范式

### 3. 用户体验一致 (UX Consistency)
- **界面风格统一**：保持ChipCore品牌色彩和设计语言
- **交互逻辑一致**：相同的操作流程和反馈机制
- **错误处理统一**：标准化的错误提示和处理方式

## 🏗️ 前端开发规范

### 页面设计模式

#### 1. 页面布局结构
```typescript
// 标准页面结构
<motion.div className="container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8">
    <div className="space-y-6">
        {/* 工具需求输入框 */}
        <Card className="border-2 border-orange-400 shadow-lg">
            <CardHeader className="relative">
                <CardTitle className="text-2xl md:text-3xl font-bold text-blue-600">
                    {toolName}需求输入：
                </CardTitle>
                <div className="absolute top-4 right-4 flex space-x-3">
                    <Button className="bg-white border-2 border-orange-600 text-orange-600">
                        Guidance
                    </Button>
                    <Button className="bg-white border-2 border-orange-600 text-orange-600">
                        📁 Template
                    </Button>
                </div>
            </CardHeader>
            {/* 表单内容 */}
        </Card>
        
        {/* 工具数据输出框 */}
        <Card className="border-2 border-orange-400 shadow-lg">
            {/* 下载区域 */}
        </Card>
    </div>
</motion.div>
```

#### 2. 颜色规范
- **主色调**：蓝色 (`text-blue-600`) 用于标题
- **品牌色**：橙色 (`text-orange-600`, `border-orange-400`) 用于标签和边框
- **按钮样式**：白色背景 + 橙色边框 + 橙色文字
- **渐变按钮**：`bg-gradient-to-r from-blue-600 to-orange-500`

#### 3. 表单组件规范
```typescript
// 参数输入区域
<div className="bg-gray-50 p-4 rounded-lg">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
        {/* ModName输入 */}
        <FormField control={form.control} name="modName" render={({ field }) => (
            <FormItem>
                <FormLabel className="text-orange-600 font-semibold text-lg">ModName</FormLabel>
                <FormControl>
                    <Input 
                        placeholder="输入模块名称"
                        className="border-orange-300 focus:border-orange-500"
                        {...field}
                    />
                </FormControl>
            </FormItem>
        )} />
    </div>
</div>
```

#### 4. 文件上传组件
```typescript
// 标准文件上传组件
const FileUploadSection: React.FC<FileUploadSectionProps> = ({
    title, field, onChange, accept, placeholder, form, name
}) => {
    return (
        <div className="border-2 border-dashed border-orange-300 rounded-lg p-4">
            <div className="mb-2">
                <Label className="text-orange-600 font-semibold text-lg">{title}</Label>
            </div>
            {/* 文件上传逻辑 */}
        </div>
    );
};
```

### 状态管理规范

#### 1. 表单验证
```typescript
// 使用Zod进行表单验证
const toolFormSchema = z.object({
    modName: z.string().min(1, "模块名称不能为空"),
    isFlat: z.boolean().default(false),
    // 文件字段
    hierYamlFile: z.instanceof(File).optional(),
    // 其他工具特定字段...
});

type ToolFormValues = z.infer<typeof toolFormSchema>;
```

#### 2. 任务状态管理
```typescript
// 标准任务状态
interface TaskStatus {
    status: 'IDLE' | 'VALIDATING' | 'SUBMITTING' | 'POLLING' | 'COMPLETED' | 'FAILED';
    taskId?: string;
    resultUrl?: string;
    error?: string;
}
```

## 🔧 后端开发规范

### API设计规范

#### 1. 路由结构
```
/api/v1/tasks          - 任务管理
/api/v1/templates      - 模板下载
/api/v1/download       - 结果下载
```

#### 2. 任务提交接口
```typescript
// POST /api/v1/tasks
interface TaskSubmissionRequest {
    toolId: string;           // 工具标识
    parameters: {             // 工具参数
        modName: string;
        isFlat: boolean;
        // 工具特定参数...
    };
    files: {                  // 上传文件
        [key: string]: File;
    };
}
```

#### 3. 数据库设计
```sql
-- Tool表：工具配置
CREATE TABLE Tool (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    dockerImage VARCHAR(200) NOT NULL,
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    inputFileTypes JSON,      -- 支持的输入文件类型
    parameters JSON           -- 工具参数配置
);

-- Task表：任务记录
CREATE TABLE Task (
    id VARCHAR(50) PRIMARY KEY,
    userId VARCHAR(50) NOT NULL,
    toolId VARCHAR(50) NOT NULL,
    parameters JSON,
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED'),
    -- 其他字段...
);
```

### Worker执行规范

#### 1. 工具执行流程
```python
def execute_tool(task_id, tool_id, parameters, input_files):
    """标准工具执行流程"""
    try:
        # 1. 创建工作目录
        work_dir = f"/tmp/jobs/{task_id}"
        os.makedirs(work_dir, exist_ok=True)
        
        # 2. 下载输入文件
        download_input_files(task_id, work_dir)
        
        # 3. 执行工具命令
        execute_tool_commands(tool_id, parameters, work_dir)
        
        # 4. 打包输出结果
        package_results(task_id, work_dir)
        
        # 5. 上传到OSS
        upload_results_to_oss(task_id, work_dir)
        
        # 6. 清理临时文件
        cleanup_temp_files(work_dir)
        
    except Exception as e:
        handle_execution_error(task_id, e)
```

#### 2. Docker容器规范
```dockerfile
# 标准Dockerfile结构
FROM ubuntu:20.04

# 安装工具依赖
RUN apt-get update && apt-get install -y \
    python3 python3-pip \
    # 工具特定依赖...

# 复制工具文件
COPY tool_files/ /opt/tool/
COPY execution_script.sh /usr/local/bin/

# 设置执行权限
RUN chmod +x /usr/local/bin/execution_script.sh

# 设置入口点
CMD ["/usr/local/bin/execution_script.sh"]
```

## 📁 文件结构规范

### 目录组织
```
project/
├── frontend/src/pages/tools/          # 工具页面
│   ├── SdcGeneratorPage.tsx
│   ├── UPFGeneratorPage.tsx
│   └── guidance/                      # 指导页面
├── backend/src/
│   ├── controllers/                   # 控制器
│   ├── services/                      # 业务逻辑
│   ├── routes/                        # 路由定义
│   └── workers/                       # 工具执行器
├── scripts/                           # 构建脚本
│   ├── docker_*_Dockerfile           # Docker文件
│   └── *_execution_script.sh         # 执行脚本
├── stuff/
│   ├── tool_template/                 # 模板文件
│   │   ├── sdcgen/
│   │   └── upfgen/
│   └── upload_data/                   # 测试数据
└── docs/                              # 文档
```

### OSS存储结构
```
OSS Buckets:
├── user-input/{userId}/{taskId}/      # 用户输入文件
├── job-results/{userId}/{taskId}/     # 任务结果文件
└── job-logs/{userId}/{taskId}/        # 执行日志
```

## 🧪 测试规范

### 1. 单元测试
- 每个新功能必须有对应的单元测试
- 测试覆盖率要求 > 80%
- 使用Jest + React Testing Library

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 文件上传下载测试

### 3. 端到端测试
- 使用Playwright进行自动化测试
- 覆盖完整的用户操作流程

## 🚀 部署规范

### 1. 环境配置
- 开发环境：本地Docker + 本地文件系统
- 生产环境：ECS + OSS + ACR

### 2. CI/CD流程
- 代码提交 → 自动测试 → 构建镜像 → 部署到环境

### 3. 监控和日志
- 应用性能监控
- 错误日志收集
- 用户行为分析

## 📊 质量标准

### 代码质量
- ESLint/Prettier检查通过
- TypeScript严格模式
- 代码审查通过

### 性能标准
- 页面加载时间 < 3秒
- API响应时间 < 500ms
- 任务执行时间 < 10分钟

### 用户体验
- 界面响应时间 < 200ms
- 错误提示清晰明确
- 操作流程简洁直观

## 🔄 迭代开发流程

### 1. 需求分析
- 明确工具功能需求
- 分析与现有工具的差异
- 确定复用和新开发的部分

### 2. 设计阶段
- UI/UX设计
- API接口设计
- 数据库设计
- 架构设计

### 3. 开发阶段
- 前端页面开发
- 后端API开发
- Worker执行逻辑
- Docker镜像构建

### 4. 测试阶段
- 单元测试
- 集成测试
- 用户验收测试

### 5. 部署上线
- 生产环境部署
- 性能监控
- 用户反馈收集

---

**遵循这些原则和规范，可以确保新工具的开发质量、一致性和可维护性。**
