/**
 * 简化的测试服务器
 * 用于测试阶段1和阶段2的核心功能，不依赖数据库和Redis
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 8080;

// 中间件
app.use(cors());
app.use(express.json());

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: 'test-1.0.0'
    });
});

// Template下载端点（模拟真实功能）
app.get('/api/v1/templates/:toolId/:filename', (req, res) => {
    const { toolId, filename } = req.params;
    
    console.log(`📥 Template download request: ${toolId}/${filename}`);
    
    // 构建模板文件路径
    const templatePath = path.join(__dirname, 'stuff', 'tool_template', toolId, filename);
    
    // 检查文件是否存在
    if (!fs.existsSync(templatePath)) {
        console.log(`❌ Template file not found: ${templatePath}`);
        return res.status(404).json({ 
            message: 'Template file not found',
            toolId,
            filename,
            path: templatePath
        });
    }
    
    // 设置正确的Content-Type
    const ext = path.extname(filename).toLowerCase();
    const contentTypes = {
        '.yaml': 'text/yaml',
        '.yml': 'text/yaml',
        '.v': 'text/plain',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };
    
    const contentType = contentTypes[ext] || 'application/octet-stream';
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    console.log(`✅ Serving template file: ${filename} (${contentType})`);
    res.sendFile(templatePath);
});

// 任务提交端点（模拟权限检查）
app.post('/api/v1/tasks', (req, res) => {
    console.log('📤 Task submission request received');
    console.log('Headers:', req.headers);
    
    // 模拟权限检查
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.log('❌ No valid authorization header');
        return res.status(401).json({ 
            message: 'Authentication required',
            code: 'NO_AUTH_TOKEN'
        });
    }
    
    // 模拟成功响应
    console.log('✅ Task submission would succeed with valid auth');
    res.status(202).json({
        message: 'Task submitted successfully',
        taskId: 'test-task-' + Date.now(),
        status: 'PENDING'
    });
});

// API根端点
app.get('/api', (req, res) => {
    res.send('Test ChipCore API is running!');
});

// 错误处理
app.use((err, req, res, next) => {
    console.error('Server error:', err);
    res.status(500).json({ message: 'Internal server error' });
});

// 404处理
app.use((req, res) => {
    console.log(`❌ 404 Not Found: ${req.method} ${req.url}`);
    res.status(404).json({ message: 'Endpoint not found' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 Test server running on http://localhost:${PORT}`);
    console.log('📋 Available endpoints:');
    console.log('  GET  /health');
    console.log('  GET  /api');
    console.log('  GET  /api/v1/templates/:toolId/:filename');
    console.log('  POST /api/v1/tasks');
    
    // 检查模板文件
    const templateDir = path.join(__dirname, 'stuff', 'tool_template', 'sdcgen');
    if (fs.existsSync(templateDir)) {
        const files = fs.readdirSync(templateDir);
        console.log(`📁 Template files available: ${files.join(', ')}`);
    } else {
        console.log('⚠️ Template directory not found:', templateDir);
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down test server...');
    process.exit(0);
});
