import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToolExecution, TaskStatus } from '../../hooks/useToolExecution';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Upload, Loader2, Download, FileText, HelpCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

const sdcFormSchema = z.object({
  modName: z.string().min(1, "模块名称不能为空"),
  isFlat: z.boolean().default(false),
  hierYamlFile: z.any()
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      return file instanceof File;
    }, "必须上传hier.yaml文件")
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      if (!file) return false;
      return file.size <= MAX_FILE_SIZE;
    }, "文件大小不能超过5MB")
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      if (!file) return false;
      return file.name.endsWith('.yaml') || file.name.endsWith('.yml');
    }, "只支持.yaml或.yml格式的文件"),
  vlogFile: z.any()
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      return file instanceof File;
    }, "必须上传vlog.v文件")
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      if (!file) return false;
      return file.size <= MAX_FILE_SIZE;
    }, "文件大小不能超过5MB")
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      if (!file) return false;
      return file.name.endsWith('.v') || file.name.endsWith('.sv');
    }, "只支持.v或.sv格式的文件"),
  dcontFile: z.any()
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      return file instanceof File;
    }, "必须上传dcont.xlsx文件")
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      if (!file) return false;
      return file.size <= MAX_FILE_SIZE;
    }, "文件大小不能超过5MB")
    .refine((file) => {
      if (typeof window === 'undefined') return true;
      if (!file) return false;
      return file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
    }, "只支持.xlsx或.xls格式的文件"),
});

type SdcFormValues = z.infer<typeof sdcFormSchema>;

// 文件上传组件
interface FileUploadSectionProps {
  title: string;
  field: any;
  onChange: (file: File | null) => void;
  accept: string;
  placeholder: string;
  onTemplateDownload: () => void;
  form: any;
  name: string;
}

const FileUploadSection: React.FC<FileUploadSectionProps> = ({
  title,
  field,
  onChange,
  accept,
  placeholder,
  onTemplateDownload,
  form,
  name
}) => {
  return (
    <div className="border-2 border-dashed border-orange-300 rounded-lg p-4">
      <div className="flex justify-between items-center mb-2">
        <Label className="text-orange-600 font-semibold text-lg">{title}</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="text-orange-600 border-orange-400 hover:bg-orange-50"
          onClick={onTemplateDownload}
        >
          template
        </Button>
      </div>
      <FormField
        control={form.control}
        name={name}
        render={({ field: { onChange: fieldOnChange, value, ...rest } }) => (
          <FormItem>
            <FormControl>
              <div>
                <Label
                  htmlFor={`${name}-upload`}
                  className={`flex items-center space-x-2 border-2 border-dashed rounded-lg p-4 cursor-pointer transition-colors ${
                    value ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:border-orange-500 hover:bg-orange-50'
                  }`}
                >
                  {value ? (
                    <FileText className="h-5 w-5 text-green-700" />
                  ) : (
                    <Upload className="h-5 w-5 text-gray-500" />
                  )}
                  <span className={value ? 'text-green-800' : 'text-gray-600'}>
                    {value?.name || placeholder}
                  </span>
                </Label>
                <Input
                  id={`${name}-upload`}
                  type="file"
                  className="hidden"
                  accept={accept}
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    fieldOnChange(file);
                    onChange(file);
                  }}
                  {...rest}
                />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

const SdcGeneratorPage: React.FC = () => {
    const { taskStatus, submitTask, resetTask, handleDownload } = useToolExecution();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [modNameHistory, setModNameHistory] = useState<string[]>([]);

    // 加载ModName历史记录
    useEffect(() => {
        const history = localStorage.getItem('sdc_modname_history');
        if (history) {
            setModNameHistory(JSON.parse(history));
        }
    }, []);

    const form = useForm<SdcFormValues>({
        resolver: zodResolver(sdcFormSchema),
        defaultValues: {
            modName: '',
            isFlat: false,
            hierYamlFile: undefined,
            vlogFile: undefined,
            dcontFile: undefined,
        },
    });

    // 保存ModName到历史记录
    const saveModNameToHistory = (modName: string) => {
        if (modName && !modNameHistory.includes(modName)) {
            const newHistory = [modName, ...modNameHistory.slice(0, 9)]; // 保留最近10个
            setModNameHistory(newHistory);
            localStorage.setItem('sdc_modname_history', JSON.stringify(newHistory));
        }
    };

    const onSubmit = (data: SdcFormValues) => {
        const { modName, isFlat, hierYamlFile, vlogFile, dcontFile } = data;

        // 保存ModName到历史记录
        saveModNameToHistory(modName);

        // 准备多文件数组
        const inputFiles: File[] = [];
        if (hierYamlFile) inputFiles.push(hierYamlFile);
        if (vlogFile) inputFiles.push(vlogFile);
        if (dcontFile) inputFiles.push(dcontFile);

        // 使用现有的submitTask方法，它会处理权限检查
        submitTask({
            toolId: 'sdc-generator',
            parameters: { modName, isFlat },
            inputFiles: inputFiles,
        });
    };

    const handleGuidanceClick = () => {
        navigate('/tools/guidance/sdc-generator');
    };

    const downloadTemplate = async (filename: string) => {
        try {
            const response = await fetch(`/api/v1/templates/sdcgen/${filename}`);
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                toast({
                    title: "模板下载成功",
                    description: `${filename} 已下载到您的设备`,
                });
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('下载模板文件失败:', error);
            toast({
                title: "下载失败",
                description: "模板文件下载失败，请稍后重试",
                variant: "destructive",
            });
        }
    };
    
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8"
        >
            <div className="space-y-6">
                {/* SDC需求输入框 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader className="relative">
                        <CardTitle className="text-2xl md:text-3xl font-bold text-blue-600">
                            SDC需求输入：
                        </CardTitle>
                        <Button
                            variant="outline"
                            className="absolute top-4 right-4 text-blue-600 border-blue-600 hover:bg-blue-50 font-bold text-lg px-6 py-2 rounded-lg shadow-md transform transition-all duration-200 hover:scale-105 hover:shadow-lg"
                            onClick={handleGuidanceClick}
                        >
                            Guidance
                        </Button>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                {/* ModName和IsFlat参数区域 */}
                                <div className="bg-gray-50 p-4 rounded-lg">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                                        {/* ModName输入 */}
                                        <FormField control={form.control} name="modName" render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-orange-600 font-semibold text-lg">ModName:</FormLabel>
                                                <FormControl>
                                                    <div className="space-y-2">
                                                        <Input
                                                            placeholder="输入模块名称"
                                                            {...field}
                                                            className="border-orange-300 focus:border-orange-500"
                                                        />
                                                        {modNameHistory.length > 0 && (
                                                            <Select onValueChange={field.onChange}>
                                                                <SelectTrigger className="border-orange-300">
                                                                    <SelectValue placeholder="选择历史记录" />
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {modNameHistory.map((name, index) => (
                                                                        <SelectItem key={index} value={name}>
                                                                            {name}
                                                                        </SelectItem>
                                                                    ))}
                                                                </SelectContent>
                                                            </Select>
                                                        )}
                                                    </div>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />

                                        {/* IsFlat复选框 */}
                                        <FormField control={form.control} name="isFlat" render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-orange-600 font-semibold text-lg">IsFlat</FormLabel>
                                                <div className="flex items-center space-x-3">
                                                    <FormControl>
                                                        <Checkbox
                                                            checked={false}
                                                            disabled={true}
                                                            className="border-gray-400 bg-gray-100 data-[state=checked]:bg-gray-300"
                                                        />
                                                    </FormControl>
                                                    <Select defaultValue="False" value="False" disabled={true}>
                                                        <SelectTrigger className="w-24 h-8 border-gray-300 bg-gray-100 text-gray-500 cursor-not-allowed">
                                                            <SelectValue />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="False">False</SelectItem>
                                                            <SelectItem value="True" disabled>True</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <span className="text-sm text-gray-500">(目前只支持False)</span>
                                                </div>
                                            </FormItem>
                                        )} />
                                    </div>
                                </div>

                                {/* 文件上传区域 */}
                                <div className="space-y-4">
                                    {/* hier.yaml上传 */}
                                    <FileUploadSection
                                        title="上传hier.yaml"
                                        field={form.watch('hierYamlFile')}
                                        onChange={(file) => form.setValue('hierYamlFile', file)}
                                        accept=".yaml,.yml"
                                        placeholder="点击或拖拽上传(.yaml)"
                                        onTemplateDownload={() => downloadTemplate('hier.yaml')}
                                        form={form}
                                        name="hierYamlFile"
                                    />

                                    {/* vlog.v上传 */}
                                    <FileUploadSection
                                        title="上传vlog.v"
                                        field={form.watch('vlogFile')}
                                        onChange={(file) => form.setValue('vlogFile', file)}
                                        accept=".v,.sv"
                                        placeholder="点击或拖拽上传(.v)"
                                        onTemplateDownload={() => downloadTemplate('vlog.v')}
                                        form={form}
                                        name="vlogFile"
                                    />

                                    {/* dcont.xlsx上传 */}
                                    <FileUploadSection
                                        title="上传dcont.xlsx"
                                        field={form.watch('dcontFile')}
                                        onChange={(file) => form.setValue('dcontFile', file)}
                                        accept=".xlsx,.xls"
                                        placeholder="点击或拖拽上传(.xlsx)"
                                        onTemplateDownload={() => downloadTemplate('dcont.xlsx')}
                                        form={form}
                                        name="dcontFile"
                                    />
                                </div>

                                {/* 提交按钮 - 移到右下角 */}
                                <div className="flex justify-end mt-6">
                                    <Button
                                        type="submit"
                                        className={`font-bold text-lg px-12 py-3 rounded-lg shadow-lg transform transition-all duration-200 ${
                                            form.formState.isSubmitting || (taskStatus.status !== 'IDLE' && taskStatus.status !== 'COMPLETED' && taskStatus.status !== 'FAILED')
                                                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                                                : 'bg-gradient-to-r from-blue-600 to-orange-500 hover:from-blue-700 hover:to-orange-600 text-white hover:scale-105 hover:shadow-xl'
                                        }`}
                                        disabled={form.formState.isSubmitting || (taskStatus.status !== 'IDLE' && taskStatus.status !== 'COMPLETED' && taskStatus.status !== 'FAILED')}
                                    >
                                        {form.formState.isSubmitting || taskStatus.status === 'SUBMITTING' ?
                                            <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : null}
                                        {form.formState.isSubmitting || taskStatus.status === 'SUBMITTING' ? 'Submitting...' :
                                         taskStatus.status === 'POLLING' ? 'Processing...' :
                                         'Submission'}
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>

                {/* SDC数据输出框 */}
                <Card className="border-2 border-orange-400 shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-2xl md:text-3xl font-bold text-blue-600">
                            SDC数据输出：
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex justify-center">
                            <Button
                                className={`px-16 py-4 text-lg border-2 border-dashed w-80 font-bold transform transition-all duration-300 ${
                                    taskStatus.status === 'COMPLETED' && taskStatus.resultUrl
                                        ? 'bg-green-600 hover:bg-green-700 text-white cursor-pointer border-green-600 shadow-lg hover:scale-105 animate-pulse'
                                        : taskStatus.status === 'POLLING' || taskStatus.status === 'SUBMITTING'
                                        ? 'bg-yellow-400 text-yellow-800 cursor-not-allowed border-yellow-400'
                                        : 'bg-gray-400 text-gray-600 cursor-not-allowed border-gray-400'
                                }`}
                                disabled={taskStatus.status !== 'COMPLETED' || !taskStatus.resultUrl}
                                onClick={() => taskStatus.resultUrl && handleDownload('result')}
                            >
                                <Download className="mr-2 h-5 w-5" />
                                {taskStatus.status === 'COMPLETED' && taskStatus.resultUrl ?
                                    'Download Zip Data ✓' :
                                    taskStatus.status === 'POLLING' || taskStatus.status === 'SUBMITTING' ?
                                    'Processing...' :
                                    'Download Zip Data'}
                            </Button>
                        </div>

                        {/* 任务状态显示 */}
                        {taskStatus.status !== 'IDLE' && (
                            <div className="mt-6">
                                <TaskStatusDisplay taskStatus={taskStatus} resetTask={resetTask} />
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </motion.div>
    );
};

const TaskStatusDisplay = ({ taskStatus, resetTask }: { taskStatus: TaskStatus, resetTask: () => void }) => {
    const { toast } = useToast();

    // 任务完成时显示通知
    useEffect(() => {
        if (taskStatus.status === 'COMPLETED' && taskStatus.resultUrl) {
            toast({
                title: "任务已经完成，请下载数据",
                description: "SDC文件生成完成，点击下载按钮获取结果",
                duration: 5000,
            });
        }
    }, [taskStatus.status, taskStatus.resultUrl, toast]);

    return (
        <div className="space-y-4">
            {taskStatus.status === 'SUBMITTING' && (
                <div className="text-center bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto" />
                    <p className="mt-2 text-blue-700 font-semibold text-lg">正在提交任务...</p>
                    <p className="text-sm text-blue-600 mt-1">请勿重复点击提交按钮</p>
                </div>
            )}

            {taskStatus.status === 'POLLING' && (
                <div className="text-center bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <Loader2 className="h-8 w-8 animate-spin text-yellow-600 mx-auto" />
                    <p className="mt-2 text-yellow-700 font-semibold text-lg">正在执行任务... {taskStatus.progress}%</p>
                    <p className="text-sm text-yellow-600 mt-1">SDC工具正在生成约束文件，请耐心等待</p>
                    {taskStatus.taskId && (
                        <p className="text-xs text-gray-500 mt-2">任务ID: {taskStatus.taskId}</p>
                    )}
                </div>
            )}

            {taskStatus.errorMessage && (
                <Alert variant="destructive">
                    <AlertTitle>任务失败</AlertTitle>
                    <AlertDescription>{taskStatus.errorMessage}</AlertDescription>
                </Alert>
            )}

            {taskStatus.status === 'COMPLETED' && (
                <Alert variant="default" className="border-green-500 bg-green-50 text-green-700">
                    <AlertTitle className="text-green-800 font-bold text-lg">🎉 任务成功完成！</AlertTitle>
                    <AlertDescription className="text-green-700 text-base">
                        SDC文件已生成完成，包含outputs、logs和rpts三个目录的完整数据。
                        <br />
                        <span className="font-semibold">请点击上方绿色的"Download Zip Data ✓"按钮下载结果。</span>
                    </AlertDescription>
                </Alert>
            )}

            {taskStatus.status !== 'IDLE' && (
                <div className="flex justify-center">
                    <Button
                        onClick={resetTask}
                        variant="outline"
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                    >
                        开始新任务
                    </Button>
                </div>
            )}
        </div>
    );
};

export default SdcGeneratorPage; 