import { prisma } from '../utils/database';
import redisClient from '../config/redis';
import { getOssClient, generatePresignedUrl } from '../utils/oss';
import { v4 as uuidv4 } from 'uuid';
import { Task } from '@prisma/client';

export const createTask = async (body: any, userId: string, inputFile?: Express.Multer.File): Promise<Task> => {
    const { toolId, parameters } = body;

    const tool = await prisma.tool.findUnique({ where: { id: toolId } });
    if (!tool) {
        throw new Error('Tool not found.');
    }

    const taskId = uuidv4();
    let inputOssPath: string | null = null;

    if (inputFile) {
        const ossClient = getOssClient(process.env.OSS_BUCKET_USER_INPUT as string);
        const objectName = `${userId}/${taskId}/${inputFile.originalname}`;
        await ossClient.put(objectName, inputFile.buffer);
        inputOssPath = objectName;
    }

    const task = await prisma.task.create({
        data: {
            id: taskId,
            userId: userId,
            toolId: toolId,
            status: 'PENDING',
            inputFile: inputOssPath,
            parameters: JSON.parse(parameters || '{}'),
        },
    });

    await redisClient.rpush('task_queue', task.id);
    
    return task;
};

export const getTaskStatus = async (taskId: string, userId: string) => {
    const task = await prisma.task.findUnique({
        where: { id: taskId },
        select: {
            id: true,
            userId: true,
            status: true,
            createdAt: true,
            updatedAt: true,
            outputFile: true,
            logFile: true,
        },
    });

    if (!task || task.userId !== userId) {
        throw new Error('Task not found or unauthorized.');
    }

    return task;
};

export const getDownloadUrl = async (taskId: string, userId: string, type: 'result' | 'log'): Promise<string> => {
    const task = await prisma.task.findUnique({
        where: { id: taskId },
        select: { userId: true, outputFile: true, logFile: true, status: true },
    });

    if (!task || task.userId !== userId) {
        throw new Error('Task not found or unauthorized.');
    }

    if (task.status !== 'COMPLETED' && task.status !== 'FAILED') {
        throw new Error('Download is not available until the task is completed or has failed.');
    }

    let bucketName: string;
    let objectName: string | null;

    if (type === 'result') {
        bucketName = process.env.OSS_BUCKET_JOB_RESULTS as string;
        objectName = task.outputFile;
    } else { // type === 'log'
        bucketName = process.env.OSS_BUCKET_JOB_LOGS as string;
        objectName = task.logFile;
    }

    if (!objectName) {
        throw new Error(`No ${type} file available for this task.`);
    }

    return generatePresignedUrl(bucketName, objectName);
};

export const getUserTasks = async (userId: string, page: number, limit: number) => {
    const skip = (page - 1) * limit;

    const [tasks, total] = await prisma.$transaction([
        prisma.task.findMany({
            where: { userId },
            orderBy: { createdAt: 'desc' },
            skip,
            take: limit,
            include: {
                tool: {
                    select: { name: true, description: true },
                },
            },
        }),
        prisma.task.count({ where: { userId } }),
    ]);

    return {
        data: tasks,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    };
};

export const getTaskById = async (taskId: string, userId: string) => {
    const task = await prisma.task.findUnique({
        where: {
            id: taskId,
            userId: userId,
        },
        include: {
            tool: true,
        },
    });

    if (!task) {
        throw new Error('Task not found or you do not have permission to view it.');
    }

    return task;
}; 