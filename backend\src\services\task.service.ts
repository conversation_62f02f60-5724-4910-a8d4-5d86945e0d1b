import { prisma } from '../utils/database';
import redisClient from '../config/redis';
import { getOssClient, generatePresignedUrl, validateOssConfig } from '../utils/oss';
import { v4 as uuidv4 } from 'uuid';
import { Task } from '@prisma/client';

export const createTask = async (body: any, userId: string, inputFiles?: Express.Multer.File[]): Promise<Task> => {
    const { toolId, parameters } = body;

    const tool = await prisma.tool.findUnique({ where: { id: toolId } });
    if (!tool) {
        throw new Error('Tool not found.');
    }

    const taskId = uuidv4();
    const inputFilePaths: string[] = [];

    // 处理多文件上传到OSS
    if (inputFiles && inputFiles.length > 0) {
        // 验证OSS配置
        if (!validateOssConfig()) {
            console.warn('⚠️ OSS configuration invalid, using local storage for development');
            // 在开发环境中，可以选择跳过OSS上传或使用本地存储
            if (process.env.NODE_ENV === 'development') {
                // 开发环境：记录文件信息但不实际上传
                for (const file of inputFiles) {
                    const objectName = `dev-local/${userId}/${taskId}/inputs/${file.originalname}`;
                    inputFilePaths.push(objectName);
                    console.log(`📁 [DEV] Would upload file: ${objectName} (${file.size} bytes)`);
                }
            } else {
                throw new Error('OSS configuration is required in production environment');
            }
        } else {
            // 生产环境：实际上传到OSS
            const bucketName = process.env.OSS_BUCKET_USER_INPUT as string;
            const ossClient = getOssClient(bucketName);

            for (const file of inputFiles) {
                try {
                    const objectName = `${userId}/${taskId}/inputs/${file.originalname}`;
                    await ossClient.put(objectName, file.buffer);
                    inputFilePaths.push(objectName);
                    console.log(`✅ File uploaded to OSS: ${objectName}`);
                } catch (error) {
                    console.error(`❌ Failed to upload file ${file.originalname}:`, error);
                    throw new Error(`Failed to upload file ${file.originalname} to OSS`);
                }
            }
        }
    }

    const task = await prisma.task.create({
        data: {
            id: taskId,
            userId: userId,
            toolId: toolId,
            status: 'PENDING',
            inputFile: inputFilePaths.length > 0 ? inputFilePaths[0] : null, // 主文件
            parameters: {
                ...JSON.parse(parameters || '{}'),
                inputFiles: inputFilePaths, // 所有文件路径
            },
        },
    });

    await redisClient.rpush('task_queue', task.id);

    return task;
};

export const getTaskStatus = async (taskId: string, userId: string) => {
    const task = await prisma.task.findUnique({
        where: { id: taskId },
        select: {
            id: true,
            userId: true,
            status: true,
            createdAt: true,
            updatedAt: true,
            outputFile: true,
            logFile: true,
        },
    });

    if (!task || task.userId !== userId) {
        throw new Error('Task not found or unauthorized.');
    }

    return task;
};

export const getDownloadUrl = async (taskId: string, userId: string, type: 'result' | 'log'): Promise<string> => {
    const task = await prisma.task.findUnique({
        where: { id: taskId },
        select: { userId: true, outputFile: true, logFile: true, status: true },
    });

    if (!task || task.userId !== userId) {
        throw new Error('Task not found or unauthorized.');
    }

    if (task.status !== 'COMPLETED' && task.status !== 'FAILED') {
        throw new Error('Download is not available until the task is completed or has failed.');
    }

    let bucketName: string;
    let objectName: string | null;

    if (type === 'result') {
        bucketName = process.env.OSS_BUCKET_JOB_RESULTS as string;
        objectName = task.outputFile;
    } else { // type === 'log'
        bucketName = process.env.OSS_BUCKET_JOB_LOGS as string;
        objectName = task.logFile;
    }

    if (!objectName) {
        throw new Error(`No ${type} file available for this task.`);
    }

    return generatePresignedUrl(bucketName, objectName);
};

export const getUserTasks = async (userId: string, page: number, limit: number) => {
    const skip = (page - 1) * limit;

    const [tasks, total] = await prisma.$transaction([
        prisma.task.findMany({
            where: { userId },
            orderBy: { createdAt: 'desc' },
            skip,
            take: limit,
            include: {
                tool: {
                    select: { name: true, description: true },
                },
            },
        }),
        prisma.task.count({ where: { userId } }),
    ]);

    return {
        data: tasks,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    };
};

export const getTaskById = async (taskId: string, userId: string) => {
    const task = await prisma.task.findUnique({
        where: {
            id: taskId,
            userId: userId,
        },
        include: {
            tool: true,
        },
    });

    if (!task) {
        throw new Error('Task not found or you do not have permission to view it.');
    }

    return task;
}; 