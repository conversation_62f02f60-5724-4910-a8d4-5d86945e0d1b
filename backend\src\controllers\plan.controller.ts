import { Request, Response } from 'express';
import * as planService from '../services/plan.service';

/**
 * @description Get all available plans
 * @route GET /api/plans
 */
export const getPlans = async (req: Request, res: Response) => {
  try {
    const plans = await planService.findAllPlans();
    res.status(200).json(plans);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching plans', error });
  }
}; 