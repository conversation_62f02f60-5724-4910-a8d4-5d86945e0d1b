# 数据库连接
DATABASE_URL="postgresql://username:password@localhost:5432/chip_tools_db"

# JWT 密钥（生产环境请使用强密钥）
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Redis 连接
REDIS_URL="redis://localhost:6379"

# 阿里云 OSS 配置（可选，用于文件存储）
OSS_REGION="oss-cn-hangzhou"
OSS_ACCESS_KEY_ID="your-access-key-id"
OSS_ACCESS_KEY_SECRET="your-access-key-secret"
OSS_BUCKET="your-bucket-name"

# 支付配置（可选）
ALIPAY_APP_ID="your-alipay-app-id"
ALIPAY_PRIVATE_KEY="your-alipay-private-key"
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_APP_SECRET="your-wechat-app-secret"

# 开发环境配置
NODE_ENV="development"
PORT="5000"

# 模板文件路径配置
# 开发环境：留空使用默认路径 (项目目录/stuff/tool_template)
# 生产环境：设置为ECS上的模板目录路径，如 /data/templates 或 /opt/chipcore/templates
TEMPLATE_ROOT_PATH=""

# 临时工作目录配置
# 开发环境：留空使用默认路径 (/tmp/logiccore_jobs)
# 生产环境：设置为ECS上的工作目录路径，如 /data/jobs
TEMP_JOBS_DIR=""