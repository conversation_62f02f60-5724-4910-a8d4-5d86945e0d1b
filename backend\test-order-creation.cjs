const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api/v1';

// 创建axios实例，支持cookie
const apiClient = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
  timeout: 10000
});

async function testOrderCreation() {
  try {
    console.log('🔄 开始测试订单创建...');

    // 1. 登录获取cookie
    console.log('1️⃣ 登录获取cookie...');
    const loginResponse = await apiClient.post('/auth/login', {
      email: '<EMAIL>',
      password: 'qaz123'
    });

    console.log('✅ 登录成功');
    console.log('   用户信息:', loginResponse.data.user.email);
    console.log('   Cookie已设置:', !!loginResponse.headers['set-cookie']);

    // 2. 创建订单
    console.log('2️⃣ 创建订单...');
    const orderResponse = await apiClient.post('/orders', {
      planId: 'cmcu3yg3w0000bcs1eqdlxngz',
      billingCycle: 'MONTHLY',
      paymentMethod: 'ALIPAY'
    });

    console.log('✅ 订单创建成功:');
    console.log('   订单ID:', orderResponse.data.order.id);
    console.log('   订单状态:', orderResponse.data.order.status);
    console.log('   订单金额:', orderResponse.data.order.amount);
    console.log('   支付方式:', orderResponse.data.order.paymentMethod);

    // 返回订单ID用于后续测试
    return orderResponse.data.order.id;

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response?.data?.errors) {
      console.error('   验证错误:', error.response.data.errors);
    }
    return null;
  }
}

// 模拟支付成功回调
async function simulatePaymentSuccess(orderId) {
  try {
    console.log('\n3️⃣ 模拟支付宝支付成功回调...');
    
    const params = new URLSearchParams({
      app_id: '9021000122671080',
      auth_app_id: '9021000122671080',
      buyer_id: '2088102177846875',
      buyer_logon_id: 'csq***@sandbox.com',
      buyer_pay_amount: '0.01',
      charset: 'utf-8',
      gmt_create: new Date().toISOString().replace('T', ' ').substring(0, 19),
      gmt_payment: new Date().toISOString().replace('T', ' ').substring(0, 19),
      notify_id: `notify_${Date.now()}`,
      notify_time: new Date().toISOString().replace('T', ' ').substring(0, 19),
      notify_type: 'trade_status_sync',
      out_trade_no: orderId,
      receipt_amount: '0.01',
      seller_email: '<EMAIL>',
      seller_id: '2088102177649450',
      subject: `Membership Subscription - ${orderId}`,
      total_amount: '0.01',
      trade_no: `2025070822001446870${Math.floor(Math.random() * 1000000)}`,
      trade_status: 'TRADE_SUCCESS',
      version: '1.0',
      sign_type: 'RSA2',
      sign: 'mock_signature_for_testing'
    });

    const response = await axios.post(
      `${BASE_URL}/payment/notify/alipay`,
      params.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    console.log('✅ 支付回调成功:', response.status, response.data);
    return true;
  } catch (error) {
    console.error('❌ 支付回调失败:', error.response?.data || error.message);
    return false;
  }
}

// 检查订单状态
async function checkOrderStatus(orderId) {
  try {
    console.log('\n4️⃣ 检查订单状态...');
    const response = await apiClient.get(`/orders/${orderId}`);
    
    console.log('📊 订单状态:', response.data.status);
    console.log('📊 订单金额:', response.data.amount);
    return response.data;
  } catch (error) {
    console.error('❌ 检查订单状态失败:', error.response?.data || error.message);
    return null;
  }
}

// 主测试流程
async function runCompleteTest() {
  console.log('🚀 开始完整支付流程测试...');
  console.log('─'.repeat(50));

  // 1. 创建订单
  const orderId = await testOrderCreation();
  if (!orderId) {
    console.log('❌ 订单创建失败，测试终止');
    return;
  }

  // 2. 模拟支付成功
  const paymentSuccess = await simulatePaymentSuccess(orderId);
  if (!paymentSuccess) {
    console.log('❌ 支付模拟失败');
    return;
  }

  // 3. 等待处理
  console.log('\n⏳ 等待回调处理...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  // 4. 检查最终状态
  const finalOrder = await checkOrderStatus(orderId);
  
  console.log('\n🎉 测试完成!');
  console.log('─'.repeat(50));
  console.log('📊 测试结果:');
  console.log(`   订单ID: ${orderId}`);
  console.log(`   最终状态: ${finalOrder?.status || '未知'}`);
  console.log(`   支付成功: ${paymentSuccess ? '是' : '否'}`);
}

runCompleteTest();
