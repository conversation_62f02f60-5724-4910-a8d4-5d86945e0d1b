import { Request, Response } from 'express';
import * as subscriptionService from '../services/subscription.service';

/**
 * @description Get the current user's subscription details.
 * @route GET /api/subscriptions/me
 */
export const getMySubscription = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }

  try {
    const subscription = await subscriptionService.findSubscriptionByUserId(req.user.id);
    if (!subscription) {
      return res.status(404).json({ message: 'No active subscription found.' });
    }
    res.json(subscription);
  } catch (error) {
    res.status(500).json({ message: 'Error retrieving subscription', error: (error as Error).message });
  }
};

/**
 * @description Cancel a subscription (disable auto-renewal).
 * @route DELETE /api/subscriptions/me
 */
export const cancelMySubscription = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }

  try {
    const subscription = await subscriptionService.cancelSubscription(req.user.id);
    res.json(subscription);
  } catch (error) {
     if ((error as Error).message.includes('not found')) {
      return res.status(404).json({ message: (error as Error).message });
    }
    res.status(500).json({ message: 'Error canceling subscription', error: (error as Error).message });
  }
}; 