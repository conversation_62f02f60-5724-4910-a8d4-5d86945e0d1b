import { Router } from 'express';
import { submitTask, getTasks, getTaskById, getTaskStatus, downloadTaskResult } from '../controllers/task.controller';
import { authenticateToken } from '../middleware/auth';
import { checkTaskExecutionPermission } from '../middleware/subscription';
import multer from 'multer';
import { validate } from '../middleware/validate';
import { 
  submitTaskSchema, 
  getTaskStatusSchema, 
  getDownloadUrlSchema 
} from '../schemas/task.schema';

const upload = multer({ storage: multer.memoryStorage() });
const router = Router();

/**
 * @route   POST /api/v1/tasks
 * @desc    Submit a new task for a tool
 * @access  Private
 */
router.post(
  '/',
  authenticateToken,
  checkTaskExecutionPermission,
  upload.array('files'),
  validate(submitTaskSchema),
  submitTask
);

/**
 * @route   GET /api/v1/tasks
 * @desc    Get paginated history of tasks for the current user
 * @access  Private
 */
router.get(
  '/',
  authenticateToken,
  getTasks
);

/**
 * @route   GET /api/v1/tasks/:taskId
 * @desc    Get the full details of a specific task
 * @access  Private
 */
router.get(
  '/:taskId',
  authenticateToken,
  // You might want to add a schema validation for params here later
  getTaskById
);

/**
 * @route   GET /api/v1/tasks/:taskId/status
 * @desc    Get the status of a specific task
 * @access  Private
 */
router.get(
  '/:taskId/status',
  authenticateToken,
  validate(getTaskStatusSchema),
  getTaskStatus
);

/**
 * @route   GET /api/v1/tasks/:taskId/download
 * @desc    Get a pre-signed URL to download task result or log
 * @access  Private
 */
router.get(
  '/:taskId/download',
  authenticateToken,
  validate(getDownloadUrlSchema),
  downloadTaskResult
);

export default router;
