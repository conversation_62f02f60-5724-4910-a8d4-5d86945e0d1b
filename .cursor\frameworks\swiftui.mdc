---
description: 
globs: 
alwaysApply: false
---
---
description: 该规则解释了 SwiftUI 在 iOS、macOS、watchOS 和 tvOS 开发中的模式和最佳实践。
globs: **/*.swift
alwaysApply: false
---

# SwiftUI 规则

- 使用结构体（struct）创建视图，并保持其小巧和专注
- 使用 @State 管理简单的视图本地状态
- 使用带有 @Published 的 @ObservableObject 管理共享状态
- 使用 @Binding 将可变状态传递给子视图
- 创建自定义 ViewModifiers 实现可复用的样式
- 使用环境对象（environment objects）进行依赖注入
- 对大型集合使用 LazyVStack 和 LazyHStack
- 将复杂的视图逻辑提取到单独的组件中 