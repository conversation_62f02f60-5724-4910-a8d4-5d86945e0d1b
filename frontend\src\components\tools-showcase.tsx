"use client";


import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";

const tools = [
  {
    title: "SDC高效生成",
    description: "自动化生成SDC约束文件，优化时序设计，提升芯片性能表现。支持复杂约束条件，确保设计满足时序要求。",
    features: ["自动约束生成", "时序优化", "多格式支持", "智能检查"],
    icon: "fas fa-microchip",
    gradient: "gradient-bg-blue",
    path: "/tools/sdc-generator"
  },
  {
    title: "CLK电路自动生成",
    description: "智能生成时钟电路，确保信号同步，降低设计复杂度。提供多种时钟域解决方案。",
    features: ["时钟树生成", "信号同步", "功耗优化", "抖动控制"],
    icon: "fas fa-project-diagram",
    gradient: "gradient-bg-orange",
    path: "/tools/clk-generator"
  },
  {
    title: "Memory数据生成",
    description: "快速生成内存配置数据，优化存储架构设计。支持多种内存类型和配置方案。",
    features: ["配置生成", "性能分析", "容量规划", "接口优化"],
    icon: "fas fa-memory",
    gradient: "gradient-bg-blue",
    path: "/tools/memory-generator"
  }
];

export default function ToolsShowcase() {
  console.log('🔧 ToolsShowcase组件开始渲染');
  
  try {
    return (
      <section className="py-20">
        {/* Section Header */}
        <div className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">专业工具集</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              为芯片设计提供全方位的自动化工具，提升开发效率
            </p>
          </motion.div>
        </div>
        
        {/* Tools List */}
        <div className="space-y-0">
          {tools.map((tool, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className={`py-20 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
            >
              <div className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12">
                <div className="flex flex-col lg:flex-row items-center gap-16">
                  {/* Content Section */}
                  <div className="flex-1 space-y-6">
                    <div className={`w-16 h-16 ${tool.gradient} rounded-xl flex items-center justify-center`}>
                      <i className={`${tool.icon} text-white text-2xl`}></i>
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900">{tool.title}</h3>
                    <p className="text-lg text-gray-600 leading-relaxed">{tool.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4">
                      {tool.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <div className={`w-2 h-2 ${tool.gradient} rounded-full`}></div>
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <Link to={tool.path}>
                      <Button className={`${tool.gradient} text-white hover:opacity-90 px-8 py-3`}>
                        立即使用
                      </Button>
                    </Link>
                  </div>
                  
                  {/* Visual Section - 简化版 */}
                  <div className="flex-1">
                    <div className="relative">
                      <div className={`w-full h-80 ${tool.gradient} rounded-2xl overflow-hidden relative flex items-center justify-center`}>
                        {/* 简化的视觉效果 */}
                        <div className="text-center">
                          <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                            <i className={`${tool.icon} text-white text-4xl`}></i>
                          </div>
                          <h4 className="text-white text-xl font-semibold">{tool.title}</h4>
                        </div>
                        
                        {/* 简单的装饰点 */}
                        <div className="absolute top-4 left-4 w-2 h-2 bg-white/30 rounded-full"></div>
                        <div className="absolute top-8 right-6 w-3 h-3 bg-white/20 rounded-full"></div>
                        <div className="absolute bottom-6 left-8 w-2 h-2 bg-white/40 rounded-full"></div>
                        <div className="absolute bottom-4 right-4 w-2 h-2 bg-white/30 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </section>
    );
  } catch (error) {
    console.error('🚨 ToolsShowcase渲染错误:', error);
    return (
      <div style={{ 
        padding: '20px', 
        backgroundColor: 'red', 
        color: 'white',
        minHeight: '200px'
      }}>
        <h1>ToolsShowcase渲染错误</h1>
        <p>错误: {String(error)}</p>
      </div>
    );
  }
}
