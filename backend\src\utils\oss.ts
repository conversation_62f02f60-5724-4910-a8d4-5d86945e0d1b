import OSS from 'ali-oss';

let ossClient: OSS | null = null;

// OSS Configuration from environment variables
const ossConfig = {
  region: process.env.OSS_REGION as string,
  accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID as string,
  accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET as string,
};

/**
 * Initializes and returns a singleton OSS client instance.
 * @param bucket - The name of the bucket to interact with.
 * @returns An OSS client instance configured for the specified bucket.
 */
export const getOssClient = (bucket: string): OSS => {
  if (!ossClient) {
    ossClient = new OSS({
      ...ossConfig,
      bucket: bucket,
    });
  } else {
    // If client exists, just switch the bucket for the operation
    ossClient.useBucket(bucket);
  }
  return ossClient;
};

/**
 * Generates a pre-signed URL for a given OSS object.
 * @param bucketName - The name of the bucket where the object is stored.
 * @param objectName - The name of the object.
 * @param expires - The expiration time for the URL in seconds. Defaults to 300 (5 minutes).
 * @returns A promise that resolves to the pre-signed URL.
 */
export const generatePresignedUrl = async (
  bucketName: string,
  objectName: string,
  expires: number = 300
): Promise<string> => {
  try {
    const client = getOssClient(bucketName);
    const signedUrl = client.signatureUrl(objectName, { expires });
    return signedUrl;
  } catch (error) {
    console.error('Failed to generate pre-signed URL:', error);
    throw new Error('Could not generate pre-signed URL.');
  }
}; 