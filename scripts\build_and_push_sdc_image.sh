#!/bin/bash

# SDC工具Docker镜像构建和ACR上传脚本
# 用于生产环境部署

set -e  # 遇到错误立即退出

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
IMAGE_NAME="logiccore/sdc-generator"
VERSION="${1:-latest}"
ACR_REGISTRY="${ACR_REGISTRY:-your-acr-registry.azurecr.io}"
DOCKERFILE_PATH="$PROJECT_ROOT/stuff/docker_sdc_generator_Dockerfile"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必需的环境变量
check_environment() {
    log "Checking environment variables..."
    
    if [[ -z "$ACR_REGISTRY" ]]; then
        error "ACR_REGISTRY environment variable is not set"
        exit 1
    fi
    
    if [[ -z "$ACR_USERNAME" ]]; then
        warning "ACR_USERNAME not set, will try to use Azure CLI"
    fi
    
    if [[ -z "$ACR_PASSWORD" ]]; then
        warning "ACR_PASSWORD not set, will try to use Azure CLI"
    fi
    
    success "Environment check completed"
}

# 检查必需的文件
check_files() {
    log "Checking required files..."
    
    local required_files=(
        "$DOCKERFILE_PATH"
        "$PROJECT_ROOT/stuff/docker_sdc_entrypoint.sh"
        "$PROJECT_ROOT/stuff/tools_collection/sdcgen/xconst"
        "$PROJECT_ROOT/stuff/tools_collection/sdcgen/xonst"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Required file not found: $file"
            exit 1
        fi
    done
    
    success "All required files found"
}

# 检查Docker是否运行
check_docker() {
    log "Checking Docker..."
    
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running or not accessible"
        exit 1
    fi
    
    success "Docker is running"
}

# 构建Docker镜像
build_image() {
    log "Building Docker image..."
    
    local full_image_name="$ACR_REGISTRY/$IMAGE_NAME:$VERSION"
    
    log "Image name: $full_image_name"
    log "Dockerfile: $DOCKERFILE_PATH"
    log "Build context: $PROJECT_ROOT"
    
    # 构建镜像
    docker build \
        -f "$DOCKERFILE_PATH" \
        -t "$full_image_name" \
        -t "$ACR_REGISTRY/$IMAGE_NAME:latest" \
        "$PROJECT_ROOT"
    
    if [[ $? -eq 0 ]]; then
        success "Docker image built successfully: $full_image_name"
    else
        error "Failed to build Docker image"
        exit 1
    fi
    
    # 显示镜像信息
    log "Image details:"
    docker images "$ACR_REGISTRY/$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# 测试镜像
test_image() {
    log "Testing Docker image..."
    
    local full_image_name="$ACR_REGISTRY/$IMAGE_NAME:$VERSION"
    
    # 测试帮助命令
    if docker run --rm "$full_image_name" --help >/dev/null 2>&1; then
        success "Image test passed"
    else
        error "Image test failed"
        exit 1
    fi
}

# 登录ACR
login_acr() {
    log "Logging into ACR..."
    
    if [[ -n "$ACR_USERNAME" && -n "$ACR_PASSWORD" ]]; then
        # 使用用户名密码登录
        echo "$ACR_PASSWORD" | docker login "$ACR_REGISTRY" -u "$ACR_USERNAME" --password-stdin
    else
        # 尝试使用Azure CLI登录
        if command -v az >/dev/null 2>&1; then
            log "Using Azure CLI for ACR login..."
            az acr login --name "${ACR_REGISTRY%%.*}"
        else
            error "No ACR credentials provided and Azure CLI not found"
            error "Please set ACR_USERNAME and ACR_PASSWORD environment variables"
            exit 1
        fi
    fi
    
    success "ACR login successful"
}

# 推送镜像到ACR
push_image() {
    log "Pushing image to ACR..."
    
    local full_image_name="$ACR_REGISTRY/$IMAGE_NAME:$VERSION"
    
    # 推送版本标签
    docker push "$full_image_name"
    
    # 推送latest标签
    docker push "$ACR_REGISTRY/$IMAGE_NAME:latest"
    
    success "Image pushed successfully to ACR"
    
    # 显示推送的镜像
    log "Pushed images:"
    echo "  - $full_image_name"
    echo "  - $ACR_REGISTRY/$IMAGE_NAME:latest"
}

# 清理本地镜像（可选）
cleanup_local() {
    if [[ "$CLEANUP_LOCAL" == "true" ]]; then
        log "Cleaning up local images..."
        
        docker rmi "$ACR_REGISTRY/$IMAGE_NAME:$VERSION" || true
        docker rmi "$ACR_REGISTRY/$IMAGE_NAME:latest" || true
        
        success "Local images cleaned up"
    fi
}

# 显示使用说明
show_usage() {
    cat << EOF
Usage: $0 [VERSION]

Build and push SDC Generator Docker image to ACR

Arguments:
  VERSION     Image version tag (default: latest)

Environment Variables:
  ACR_REGISTRY    ACR registry URL (required)
  ACR_USERNAME    ACR username (optional if using Azure CLI)
  ACR_PASSWORD    ACR password (optional if using Azure CLI)
  CLEANUP_LOCAL   Set to 'true' to remove local images after push

Examples:
  $0                    # Build and push with 'latest' tag
  $0 v1.0.0            # Build and push with 'v1.0.0' tag
  
  # With environment variables
  ACR_REGISTRY=myregistry.azurecr.io $0 v1.0.0

EOF
}

# 主函数
main() {
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        show_usage
        exit 0
    fi
    
    log "Starting SDC Generator Docker image build and push process..."
    log "Version: $VERSION"
    log "Registry: $ACR_REGISTRY"
    
    # 执行所有步骤
    check_environment
    check_files
    check_docker
    build_image
    test_image
    login_acr
    push_image
    cleanup_local
    
    success "SDC Generator Docker image build and push completed successfully!"
    
    log "Next steps:"
    echo "  1. Update your deployment configuration to use: $ACR_REGISTRY/$IMAGE_NAME:$VERSION"
    echo "  2. Update the Tool table in database with the new image URL"
    echo "  3. Test the deployment in your staging environment"
}

# 运行主函数
main "$@"
