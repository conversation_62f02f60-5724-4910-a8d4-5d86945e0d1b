# SDC高效生成页面全面修改任务清单

## 项目概述

本项目旨在根据用户需求全面修改SDC高效生成页面，实现真实生产场景的完整工具执行流程。项目包括页面重新设计、功能实现、工具执行流程完善、Docker化、测试等完整开发流程。

## 项目目标

1. **页面重新设计**：实现两个大框结构（需求输入框和数据输出框），橙色边框设计
2. **参数修改**：ModName（带历史记录下拉）和IsFlat（复选框，默认False）
3. **文件上传**：支持hier.yaml, vlog.v, dcont.xlsx三个文件上传
4. **工具执行**：完善SDC工具执行流程，实现Docker化
5. **权限控制**：实现缓存机制和权限检查
6. **数据输出**：实现下载按钮状态管理和通知机制
7. **测试部署**：制定测试计划，准备生产部署

## 技术架构

### 前端技术栈
- React 18 + TypeScript
- React Hook Form + Zod 验证
- Tailwind CSS + shadcn/ui
- Framer Motion 动画

### 后端技术栈
- Node.js + Express + TypeScript
- Prisma ORM + PostgreSQL
- Redis 队列管理
- 阿里云 OSS 文件存储

### 工具执行
- Docker 容器化
- Python toolWorker
- 阿里云 ACR 镜像仓库
- 阿里云 STS 临时凭证

## 详细任务规划

### 阶段1: 页面布局和基础功能修改

#### 1.1 重新设计SDC页面组件
**目标**：根据用户提供的设计图，重新设计SdcGeneratorPage组件

**技术实现**：
- 创建两个主要区域：SDC需求输入框和SDC数据输出框
- 使用Card组件实现橙色边框设计
- 实现响应式布局，支持移动端

**文件修改**：
- `frontend/src/pages/tools/SdcGeneratorPage.tsx`

#### 1.2 修改参数输入逻辑
**目标**：将原有参数修改为ModName和IsFlat

**技术实现**：
- ModName：文本输入框 + 下拉历史记录（使用localStorage存储）
- IsFlat：复选框，默认值False
- 使用React Hook Form管理表单状态

**数据结构**：
```typescript
interface SdcFormData {
  modName: string;
  isFlat: boolean;
}
```

#### 1.3 创建guidance页面系统
**目标**：实现SDC工具使用指南页面

**技术实现**：
- 创建`frontend/src/pages/tool_guidance/`目录
- 实现`SdcGuidancePage.tsx`组件
- 添加路由配置

**指南内容**：
1. ModName填入harden模块名字
2. IsFlat选项说明
3. 文件填写要求
4. 工具执行流程说明
5. 结果下载说明

#### 1.4 调整页面样式和颜色
**目标**：按照用户要求调整颜色方案

**颜色规范**：
- 框线：橙色 (#f97316)
- guidance和commit按钮：蓝色 (#3b82f6)
- 标题文字：蓝色粗体
- 其他文字：橙色

### 阶段2: 文件上传和缓存逻辑

#### 2.1 实现三个文件上传功能
**目标**：支持hier.yaml, vlog.v, dcont.xlsx三个文件的上传

**技术实现**：
- 使用多文件上传组件
- 文件类型验证和大小限制
- 拖拽上传支持

**文件验证规则**：
```typescript
const fileValidation = {
  'hier.yaml': { maxSize: '5MB', accept: '.yaml,.yml' },
  'vlog.v': { maxSize: '10MB', accept: '.v,.sv' },
  'dcont.xlsx': { maxSize: '5MB', accept: '.xlsx,.xls' }
};
```

#### 2.2 实现template下载功能
**目标**：为每个文件添加template按钮

**技术实现**：
- 从`stuff/tool_template/sdcgen/`目录提供模板文件
- 实现文件下载API接口
- 根据文件类型调用不同编辑器

**API设计**：
```
GET /api/v1/templates/sdcgen/:filename
```

#### 2.3 实现文件缓存机制
**目标**：实现文件上传的中间缓存机制

**技术方案**：
- 文件先上传到临时目录：`stuff/upload_cache/{userId}/{timestamp}/`
- 权限检查通过后移动到正式目录
- 权限检查失败后清理缓存（2分钟后自动清理）

**缓存清理策略**：
- 立即清理：权限检查失败
- 定时清理：2分钟后自动清理未处理的缓存

#### 2.4 完善权限检查中间件
**目标**：完善checkTaskExecutionPermission中间件

**检查项目**：
1. 用户登录状态验证
2. 用户邮箱验证状态
3. 订阅权限检查
4. 每日使用配额检查

### 阶段3: 工具执行流程完善

#### 3.1 完善SDC工具执行命令
**目标**：根据用户提供的SDC工具执行流程，完善toolWorker.py

**执行流程**：
1. 建立目录结构：`xonst sdcgen -gen_dir ./ -blocks <blk_name> -setup`
2. 放置配置文件到：`<ModName>/sdc/inputs/`
3. 检查输入信息：`xconst sdgen -gen_dir ./ -hier_yaml <hier_file> -chk_only -blocks <blk_name>`
4. 生成SDC：根据IsFlat参数选择命令
5. 检查SDC文件：根据IsFlat参数选择命令

#### 3.2 创建SDC工具Docker镜像
**目标**：创建SDC工具的Dockerfile

**Dockerfile设计**：
```dockerfile
FROM ubuntu:20.04
RUN apt-get update && apt-get install -y python3 python3-pip
COPY stuff/tools_collection/sdcgen/ /app/sdcgen/
COPY docker/sdc_entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh
ENTRYPOINT ["/app/entrypoint.sh"]
```

**执行脚本**：创建Shell脚本实现多条命令顺序执行

#### 3.3 优化任务调度和状态管理
**目标**：优化任务的调度逻辑

**改进点**：
- 任务状态实时更新
- 错误处理和重试机制
- 资源使用监控

#### 3.4 实现OSS文件管理
**目标**：完善OSS文件上传下载逻辑

**文件组织结构**：
```
OSS_BUCKET/
├── tasks/{taskId}/
│   ├── inputs/
│   │   ├── hier.yaml
│   │   ├── vlog.v
│   │   └── dcont.xlsx
│   ├── outputs/
│   │   └── result.zip
│   └── logs/
│       └── execution.log
```

### 阶段4: 数据输出和下载功能

#### 4.1 实现下载按钮状态管理
**目标**：实现Download Zip Data按钮的状态管理

**状态设计**：
- 初始状态：灰色背景，灰色文字，不可点击
- 完成状态：绿色背景，白色文字，可点击

#### 4.2 实现任务完成通知机制
**目标**：实现任务完成后的通知机制

**通知方式**：
- Toast通知："任务已经完成，请下载数据"
- 按钮状态变化
- 可选：浏览器通知

#### 4.3 完善结果文件下载
**目标**：确保生成的zip文件可以正常下载

**实现要点**：
- 生成包含所有结果文件的zip包
- 使用预签名URL安全下载
- 下载链接有效期管理

#### 4.4 统一日志系统
**目标**：统一工具执行的日志

**日志内容**：
- 权限检查记录
- 工具执行状态
- STS凭证使用
- 错误信息记录

### 阶段5: 测试和部署准备

#### 5.1 制定测试计划
**目标**：制定详细的测试计划

**测试范围**：
- 功能测试：文件上传、参数输入、工具执行
- 权限测试：登录验证、订阅检查
- 性能测试：并发处理、资源使用
- 安全测试：文件安全、权限控制

#### 5.2 实现本地测试环境
**目标**：搭建本地测试环境

**测试环境**：
- 使用本地目录模拟OSS存储
- 使用本地Redis和PostgreSQL
- 模拟Docker执行环境

#### 5.3 Docker镜像测试
**目标**：测试SDC工具Docker镜像

**测试内容**：
- 镜像构建测试
- 工具执行流程测试
- 文件输入输出测试

#### 5.4 生产部署准备
**目标**：准备生产部署所需的配置

**部署清单**：
- OSS存储桶配置
- ACR镜像仓库设置
- ECS实例配置
- 环境变量配置

## 风险评估和解决方案

### 技术风险
1. **Docker化复杂性**：SDC工具可能有复杂的依赖关系
   - 解决方案：分步骤测试，逐步完善Dockerfile

2. **文件缓存机制**：可能影响系统性能
   - 解决方案：实现异步清理，监控磁盘使用

3. **权限检查时机**：可能影响用户体验
   - 解决方案：优化检查逻辑，提供清晰的错误提示

### 业务风险
1. **用户体验**：新界面可能需要用户适应
   - 解决方案：提供详细的使用指南，保持向后兼容

2. **数据安全**：文件上传和存储的安全性
   - 解决方案：实现严格的权限控制和文件验证

## 成功标准

1. **功能完整性**：所有功能按需求实现
2. **性能指标**：页面加载时间 < 2秒，文件上传响应 < 5秒
3. **用户体验**：界面友好，操作流畅
4. **系统稳定性**：错误率 < 1%，可用性 > 99%
5. **安全性**：通过安全测试，无重大漏洞

## 下一步行动

1. **立即开始**：阶段1的页面布局修改
2. **并行开发**：文件上传功能和权限检查
3. **重点关注**：工具执行流程的Docker化
4. **持续测试**：每个阶段完成后进行测试验证

---

*本文档将随着项目进展持续更新，确保所有团队成员了解最新的开发状态和要求。*
