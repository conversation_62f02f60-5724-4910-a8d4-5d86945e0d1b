# UPF工具开发任务清单

## 📋 项目概述

基于已完成的SDC工具开发经验，开发UPF高效生成工具，确保代码复用、架构一致性和用户体验统一。

## 🎯 核心原则

1. **代码复用**: 最大化利用SDC工具已有代码逻辑
2. **架构一致**: 遵循相同的技术架构和设计模式
3. **用户体验**: 保持界面风格和交互逻辑一致
4. **生产就绪**: 确保本地测试和生产环境完全兼容

## 📝 详细任务分解

### 阶段1: SDC工具页面优化 (1-2天)

#### 1.1 UI布局调整 ⭐⭐
**目标**: 修改SDC工具页面布局和样式
**具体任务**:
- [ ] 移除所有template按钮，统一放到右上角
- [ ] 修改Guidance和Template按钮样式：白色背景，ChipCore商标颜色字体
- [ ] 去掉ModName后面的冒号
- [ ] 统一字体颜色：ModName、IsFlat、文件上传标签使用ChipCore商标颜色

**技术要点**:
```typescript
// 按钮样式统一
className="bg-white border-2 border-orange-500 text-orange-600 hover:bg-orange-50"
```

#### 1.2 Template下载功能 ⭐⭐⭐
**目标**: 实现模板文件下载功能
**具体任务**:
- [ ] 本地环境：从`stuff/tool_template/sdc_gen.zip`下载
- [ ] 生产环境：从ECS指定目录下载
- [ ] 添加下载API接口
- [ ] 测试下载功能正常

**技术要点**:
```typescript
// API路由
GET /api/v1/templates/:toolId/:filename
// 本地: 读取本地文件
// 生产: 从ECS目录读取
```

### 阶段2: 工具开发原则文档 (0.5天)

#### 2.1 经验总结文档 ⭐⭐
**目标**: 基于SDC工具开发经验，总结开发原则
**具体任务**:
- [ ] 分析SDC工具完整开发流程
- [ ] 总结页面设计模式
- [ ] 整理API设计规范
- [ ] 记录Docker镜像构建流程
- [ ] 文档化测试策略

**输出**: `docs/tool_dev_principle.md`

### 阶段3: 主页工具展示优化 (0.5天)

#### 3.1 交叉布局设计 ⭐⭐
**目标**: 改进主页工具展示区域
**具体任务**:
- [ ] SDC工具：文字左，图片右
- [ ] UPF工具：图片左，文字右
- [ ] 实现背景色交叉变化
- [ ] 统一字体科技质感

### 阶段4: UPF工具前端开发 (2-3天)

#### 4.1 UPF工具主页面 ⭐⭐⭐
**目标**: 开发UPF工具页面，完全遵循SDC页面风格
**具体任务**:
- [ ] 创建`UPFGeneratorPage.tsx`
- [ ] 实现4个文件上传：hier.yaml, pvlog.v, pobj.tcl, pcont.xlsx
- [ ] 添加参数设置：ModName(历史记录), Version(下拉), IsFlat(禁用)
- [ ] 复用SDC的表单验证逻辑
- [ ] 复用SDC的状态管理逻辑

**技术要点**:
```typescript
// 复用SDC的FormSchema，扩展字段
const upfFormSchema = sdcFormSchema.extend({
  version: z.string(),
  pobjTclFile: z.instanceof(File).optional(),
  pcontXlsxFile: z.instanceof(File).optional()
});
```

#### 4.2 UPF Guidance页面 ⭐⭐
**目标**: 开发UPF指导页面
**具体任务**:
- [ ] 创建`UPFGuidancePage.tsx`
- [ ] 按照截图内容实现指导信息
- [ ] 复用SDC Guidance的布局和样式
- [ ] 添加4个文件说明卡片

#### 4.3 路由和导航 ⭐
**目标**: 添加UPF工具路由
**具体任务**:
- [ ] 添加`/tools/upf-generator`路由
- [ ] 添加`/tools/guidance/upf-generator`路由
- [ ] 更新导航菜单

### 阶段5: UPF工具后端开发 (2-3天)

#### 5.1 数据库扩展 ⭐⭐
**目标**: 扩展数据库支持UPF工具
**具体任务**:
- [ ] 添加UPF工具到Tool表
- [ ] 验证Task表兼容性
- [ ] 更新数据库种子数据

**技术要点**:
```sql
INSERT INTO Tool (id, name, dockerImage, status, inputFileTypes, parameters)
VALUES ('upf-generator', 'UPF Generator', 'upf-generator:latest', 'ACTIVE', 
        '["hier.yaml","pvlog.v","pobj.tcl","pcont.xlsx"]',
        '{"modName":"string","version":"string","isFlat":"boolean"}');
```

#### 5.2 API接口扩展 ⭐⭐
**目标**: 扩展现有API支持UPF工具
**具体任务**:
- [ ] 验证`/api/v1/tasks`接口兼容性
- [ ] 添加UPF工具参数验证
- [ ] 添加UPF模板下载接口
- [ ] 更新文件类型验证

#### 5.3 Worker逻辑扩展 ⭐⭐⭐⭐
**目标**: 扩展Worker支持UPF工具执行
**具体任务**:
- [ ] 分析UPF工具执行流程
- [ ] 扩展`toolWorker.py`支持UPF工具
- [ ] 实现UPF工具多命令执行逻辑
- [ ] 添加UPF工具错误处理

**UPF执行流程**:
```python
def execute_upf_tool(task_id, mod_name, version, is_flat):
    # 1. 建立目录结构
    run_command(f"xonst upfgen -gen_dir ./ -blocks {mod_name} -setup")
    
    # 2. 复制输入文件到指定目录
    copy_input_files(f"{mod_name}/upf/inputs")
    
    # 3. 检查输入信息
    run_command(f"xconst upfgen -gen_dir ./ -hier_yaml hier.yaml -chk_only -blocks {mod_name}")
    
    # 4. 生成UPF
    if is_flat:
        run_command(f"xonst upfgen -gen_dir ./ -hier_yaml hier.yaml -blocks {mod_name} -upf-flat")
    else:
        run_command(f"xonst upfgen -gen_dir ./ -hier_yaml hier.yaml -blocks {mod_name} -upf")
    
    # 5. 检查UPF文件
    chk_cmd = f"xonst upfgen -gen_dir ./ -hier_yaml hier.yaml -blocks {mod_name} -chk_upf"
    if is_flat:
        chk_cmd += " -flat"
    run_command(chk_cmd)
```

### 阶段6: UPF工具Docker镜像 (1-2天)

#### 6.1 Dockerfile创建 ⭐⭐⭐
**目标**: 创建UPF工具Docker镜像
**具体任务**:
- [ ] 创建`scripts/docker_upf_generator_Dockerfile`
- [ ] 安装UPF工具依赖
- [ ] 创建多命令执行脚本
- [ ] 配置容器入口点

**技术要点**:
```dockerfile
# 创建执行脚本
COPY upf_execution_script.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/upf_execution_script.sh
CMD ["/usr/local/bin/upf_execution_script.sh"]
```

#### 6.2 执行脚本开发 ⭐⭐⭐
**目标**: 创建UPF工具执行脚本
**具体任务**:
- [ ] 创建`scripts/upf_execution_script.sh`
- [ ] 实现参数解析
- [ ] 实现多命令顺序执行
- [ ] 添加错误处理和日志

### 阶段7: 文件结构和存储 (1天)

#### 7.1 OSS目录结构 ⭐⭐
**目标**: 扩展OSS目录结构支持UPF工具
**具体任务**:
- [ ] 更新`DATABASE_AND_FILE_STRUCTURE.md`
- [ ] 添加UPF工具目录结构
- [ ] 验证STS凭证兼容性

**目录结构**:
```
OSS Buckets:
├── user-input/
│   └── {userId}/
│       └── {taskId}/
│           ├── hier.yaml
│           ├── pvlog.v
│           ├── pobj.tcl
│           └── pcont.xlsx
├── job-results/
│   └── {userId}/
│       └── {taskId}/
│           └── upf_result.zip
└── job-logs/
    └── {userId}/
        └── {taskId}/
            └── execution.log
```

### 阶段8: 测试和验证 (2-3天)

#### 8.1 本地测试 ⭐⭐⭐
**目标**: 完成UPF工具本地测试
**具体任务**:
- [ ] 准备测试数据：`stuff/upload_data/upfgen/`
- [ ] 创建测试脚本：`test/upf_tool_test.js`
- [ ] 测试完整执行流程
- [ ] 验证文件上传下载

#### 8.2 生产环境测试 ⭐⭐⭐⭐
**目标**: 验证生产环境兼容性
**具体任务**:
- [ ] 构建和推送Docker镜像到ACR
- [ ] 测试ECS环境执行
- [ ] 验证OSS文件操作
- [ ] 性能和资源使用测试

### 阶段9: 文档和部署 (1天)

#### 9.1 文档完善 ⭐⭐
**目标**: 完善项目文档
**具体任务**:
- [ ] 更新`README.md`
- [ ] 创建UPF工具使用指南
- [ ] 更新部署文档

#### 9.2 部署准备 ⭐⭐
**目标**: 准备生产部署
**具体任务**:
- [ ] 验证环境变量配置
- [ ] 更新Docker Compose配置
- [ ] 创建部署检查清单

## 📊 工作量估算

| 阶段 | 预估工作量 | 复杂度 | 依赖关系 |
|------|------------|--------|----------|
| SDC页面优化 | 1-2天 | ⭐⭐ | 无 |
| 开发原则文档 | 0.5天 | ⭐⭐ | SDC完成 |
| 主页优化 | 0.5天 | ⭐⭐ | 无 |
| UPF前端开发 | 2-3天 | ⭐⭐⭐ | SDC页面完成 |
| UPF后端开发 | 2-3天 | ⭐⭐⭐⭐ | 前端完成 |
| Docker镜像 | 1-2天 | ⭐⭐⭐ | 后端完成 |
| 文件结构 | 1天 | ⭐⭐ | 后端完成 |
| 测试验证 | 2-3天 | ⭐⭐⭐⭐ | 所有开发完成 |
| 文档部署 | 1天 | ⭐⭐ | 测试完成 |
| **总计** | **11-16天** | **⭐⭐⭐⭐** | - |

## 🎯 关键成功因素

1. **代码复用率 > 80%**: 最大化利用SDC工具代码
2. **界面一致性**: 确保UPF和SDC工具界面风格完全一致
3. **功能完整性**: UPF工具功能不少于SDC工具
4. **性能要求**: 执行时间和资源使用与SDC工具相当
5. **测试覆盖**: 本地和生产环境测试覆盖率100%

## 🚨 风险点和缓解措施

| 风险点 | 影响 | 缓解措施 |
|--------|------|----------|
| UPF工具命令复杂 | 高 | 详细分析命令流程，分步实现 |
| Docker多命令执行 | 中 | 使用Shell脚本封装 |
| 文件类型增加 | 中 | 扩展现有验证逻辑 |
| 测试数据准备 | 低 | 提前准备标准测试文件 |

## 📈 质量标准

- **代码质量**: ESLint/Prettier检查通过
- **类型安全**: TypeScript严格模式
- **测试覆盖**: 核心功能100%覆盖
- **性能标准**: 页面加载<3秒，任务执行<10分钟
- **用户体验**: 界面响应<200ms，错误提示清晰

## 🔄 迭代计划

### MVP版本 (第1-2周)
- SDC页面优化
- UPF基础页面
- 核心执行流程

### 完整版本 (第3周)
- 完整功能实现
- 全面测试验证
- 文档完善

### 优化版本 (第4周)
- 性能优化
- 用户体验改进
- 生产部署
