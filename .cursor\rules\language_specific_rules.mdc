---
description: 
globs: 
alwaysApply: true
---
# 特定编程语言规则

## TypeScript规则（前端）

1. **类型定义**：
   - 为所有变量、参数和返回值定义明确的类型
   - 避免使用`any`类型，优先使用`unknown`或具体类型
   - 使用接口（Interface）定义对象结构
   - 使用类型别名（Type Alias）创建复杂类型
   - 导出所有公共API的类型定义

2. **类型断言**：
   - 尽量避免使用类型断言（Type Assertion）
   - 当必须使用断言时，优先选择`as`语法而非尖括号
   - 使用类型守卫（Type Guard）进行运行时类型检查
   - 实现自定义类型守卫函数

3. **泛型使用**：
   - 适当使用泛型增加代码的复用性
   - 为泛型参数提供有意义的名称（T, K, V等）
   - 使用泛型约束限制类型参数
   - 为复杂的泛型提供默认类型参数

4. **枚举与常量**：
   - 使用字符串枚举提高代码可读性
   - 为枚举提供有意义的名称和注释
   - 使用`as const`创建只读常量对象
   - 避免使用数字枚举

5. **配置文件**：
   - 保持一致的tsconfig.json配置
   - 启用strict模式
   - 根据项目需求配置合适的模块解析策略
   - 设置明确的lib和target选项

6. **代码优化**：
   - 利用TypeScript的结构类型系统
   - 使用索引签名和映射类型处理动态属性
   - 使用条件类型实现高级类型操作
   - 使用工具类型简化类型定义

## JavaScript规则（后端）

1. **语言特性**：
   - 使用ES Modules进行模块管理
   - 优先使用const声明变量，避免使用var
   - 合理使用异步/await而非回调
   - 使用解构赋值和扩展运算符简化代码
   - 使用箭头函数处理简单函数和回调

2. **错误处理**：
   - 使用try/catch捕获异步操作的异常
   - 创建自定义错误类继承Error
   - 提供有意义的错误消息和状态码
   - 使用Promise.catch处理异步错误

3. **模块组织**：
   - 每个文件应导出单一功能
   - 使用默认导出（default export）导出主要功能
   - 使用命名导出（named export）导出辅助函数
   - 避免循环依赖

4. **异步处理**：
   - 使用async/await处理异步流程
   - 避免回调地狱，使用Promise链
   - 使用Promise.all并行处理多个异步操作
   - 正确处理Promise的拒绝（rejection）

5. **安全最佳实践**：
   - 使用内容安全策略防止XSS攻击
   - 实现CSRF保护机制
   - 避免使用eval()和new Function()
   - 验证并净化用户输入

6. **性能考量**：
   - 避免使用阻塞操作
   - 使用内置方法操作集合（如map, filter）
   - 优化正则表达式
   - 适当使用缓存提高性能

## SQL规则（数据库查询）

1. **查询格式**：
   - 使用大写SQL关键字
   - 使用清晰的缩进和格式
   - 使用别名（alias）提高可读性
   - 每个主要子句应单独成行

2. **查询优化**：
   - 只选择需要的列，避免使用SELECT *
   - 使用WHERE子句限制结果集
   - 合理使用索引提高查询效率
   - 避免在WHERE子句中对列使用函数

3. **连接操作**：
   - 优先使用INNER JOIN而非WHERE条件连接
   - 指定明确的连接条件
   - 使用适当的连接类型（LEFT JOIN, RIGHT JOIN等）
   - 避免使用交叉连接（CROSS JOIN）

4. **事务处理**：
   - 使用事务保证数据完整性
   - 保持事务简短
   - 避免长时间持有事务锁
   - 正确处理事务回滚

## HTML/CSS规则（前端标记与样式）

1. **HTML结构**：
   - 使用语义化HTML元素
   - 保持DOM结构清晰简洁
   - 为交互元素添加适当的ARIA属性
   - 确保HTML结构的可访问性

2. **Tailwind CSS使用**：
   - 遵循Tailwind CSS的命名约定
   - 使用@apply指令组合常用样式
   - 扩展主题时遵循设计系统
   - 避免内联自定义样式

3. **响应式设计**：
   - 使用Tailwind的响应式前缀实现响应式设计
   - 遵循移动优先原则
   - 确保界面在各种设备上的可用性
   - 适当使用容器查询解决复杂布局问题

4. **性能优化**：
   - 减少CSS选择器复杂度
   - 避免使用!important
   - 实现渐进增强策略
   - 合理使用CSS动画，考虑性能影响