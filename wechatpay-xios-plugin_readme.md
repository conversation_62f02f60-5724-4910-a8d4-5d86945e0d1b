微信支付 OpenAPI SDK
Promise based and chained WeChatPay OpenAPI SDK for NodeJS

      

系统要求
NodeJs >= 12

安装
$ npm install wechatpay-axios-plugin

初始化
const { Wechatpay } = require('wechatpay-axios-plugin');
const { readFileSync } = require('fs');

// 商户号，支持「普通商户/特约商户」或「服务商商户」
const merchantId = '190000****';

// 「商户API证书」的「证书序列号」
const merchantCertificateSerial = '3775B6A45ACD588826D15E583A95F5DD********';

// 「商户API私钥」`file://`协议的本地文件绝对路径
const merchantPrivateKeyFilePath = 'file:///path/to/merchant/apiclient_key.pem';

// APIv3 的「平台证书」接入模式 {{{
// 「平台证书」的「证书序列号」
// 可以从「平台证书」文件解析，也可以在 商户平台 -> 账户中心 -> API安全 查询到
// const platformCertificateSerial = '7132D72A03E93CDDF8C03BBD1F37EEDF********';

// 「平台证书」`file://`协议的本地文件绝对路径
// 「平台证书」文件可由内置的CLI工具下载到
// const platformCertificateFilePath = 'file:///path/to/wechatpay/certificate.pem';
// }}}

// APIv3 的「微信支付公钥」接入模式 {{{
// 「微信支付公钥」`file://`协议的本地文件绝对路径
// 需要在 商户平台 -> 账户中心 -> API安全 下载
const platformPublicKeyFilePath = 'file:///path/to/wechatpay/publickey.pem';

// 「微信支付公钥」的「微信支付公钥ID」
// 需要在 商户平台 -> 账户中心 -> API安全 查询
const platformPublicKeyId = 'PUB_KEY_ID_01142321349124100000000000********';
// }}}

// 构造一个 APIv2 & APIv3 客户端实例
const wxpay = new Wechatpay({
  mchid: merchantId,
  serial: merchantCertificateSerial,
  privateKey: merchantPrivateKeyFilePath,
  // 根据商户号所能接入的APIv3模式(微信支付公钥/平台证书)按需配置certs对象内容
  certs: {
    // 「平台证书」 接入模式时，则填 platformCertificate* 配置项及配置行，多平台证书时配多行
    // [platformCertificateSerial]: platformCertificateFilePath,
    // 「微信支付公钥」 接入模式时，则填 platformPublicKey* 配置项及配置行，当前新商户只此模式
    [platformPublicKeyId]: platformPublicKeyFilePath,
  },
  // APIv2(密钥32字节)
  secret: 'your_merchant_secret_key_string',
  // 部分接口要求使用「商户API证书」的场景，需要额外配置如下{cert,key}或{pfx,passphrase}参数
  merchant: {
    cert: readFileSync('/path/to/merchant/apiclient_cert.pem'),
    key: readFileSync(merchantPrivateKeyFilePath.slice(7)),
    // 或者配置如下`passphrase`及`pfx`配置项
    // passphrase: 'your_merchant_id',
    // **注**: Node17.1开始使用OpenSSL3,老的p12文件需要额外格式转换
    // pfx: readFileSync('/your/merchant/cert/apiclient_cert.p12'),
  },
});
初始化字典说明如下：

mchid 为你的商户号，一般是10字节纯数字
serial 为你的商户证书序列号，一般是40字节字符串
privateKey 为你的商户API私钥，一般是通过官方证书生成工具生成的文件名是apiclient_key.pem文件，支持纯字符串或者文件流buffer格式
certs{[serial_number]:string} 为key/value键值对，键为平台证书序列号/微信支付公钥ID，值为平台证书/微信支付公钥pem格式的纯字符串或者文件流buffer格式
secret 为APIv2版的密钥，商户平台上设置的32字节字符串
merchant.cert 为你的商户证书,一般是文件名为apiclient_cert.pem文件，支持纯字符串或者文件流buffer格式
merchant.key 为你的商户API私钥，一般是通过官方证书生成工具生成的文件名是apiclient_key.pem文件，支持纯字符串或者文件流buffer格式
merchant.passphrase 一般为你的商户号
merchant.pfx 为你的商户PKCS12格式的证书，文件名一般为apiclient_cert.p12，支持二进制文件流buffer格式(注: Node17.1开始使用OpenSSL3,老的p12文件需要额外格式转换)
注： APIv2&APIv3以及Axios初始参数，均融合在一个型参上。

APIv3
Native下单
示例代码
wxpay.v3.pay.transactions.native
  .post({
    mchid: '1900006XXX',
    out_trade_no: 'native12177525012014070332333',
    appid: 'wxdace645e0bc2cXXX',
    description: 'Image形象店-深圳腾大-QQ公仔',
    notify_url: 'https://weixin.qq.com/',
    amount: {
      total: 1,
      currency: 'CNY'
    },
  })
  .then(({data: {code_url}}) => console.info(code_url))
  .catch(({response: {
    status,
    statusText,
    data
  } }) => console.error(status, statusText, data))
查询订单
示例代码
// _placeholder_ 语法糖会转换成 '{placeholder}' 格式
wxpay.v3.pay.transactions.id._transaction_id_
  .get({
    params: {
      mchid: '1230000109'
    },
    //当商户订单号有大写字符时，只能这样参数化传递
    transaction_id: '1217752501201407033233368018'
  })
  .then(({data}) => console.info(data))
  .catch(({response: {
    status,
    statusText,
    data
  } }) => console.error(status, statusText, data))
关闭订单
示例代码
// $placeholder$ 语法糖会转换成 '{placeholder}' 格式
wxpay.v3.pay.transactions.outTradeNo.$out_trade_no$.close
  .post({
    mchid: '1230000109'
  }, {
    //当商户订单号有大写字符时，只能这样参数化传递
    out_trade_no: 'P1217752501201407033233368018'
  })
  .then(({status, statusText}) => console.info(status, statusText))
  .catch(({response: {
    status,
    statusText,
    data
  } }) => console.error(status, statusText, data))
发起退款
示例代码

;(async () => {
  try {
    const res = await wxpay.v3.refund.domestic.refunds.post({
      transaction_id: '1217752501201407033233368018',
      out_refund_no: '1217752501201407033233368018',
      reason: '商品已售完',
      notify_url: 'https://weixin.qq.com',
      funds_account: 'AVAILABLE',
      amount: {
        refund: 888,
        from: [{
          account: 'AVAILABLE',
          amount: 444,
        }],
        total: 888,
        currency: 'CNY'
      },
    });
  } catch({response: {status, statusText, data}}) {
    console.error(status, statusText, data)
  }
})()
更多示例代码访问这里

APIv2
现金红包
示例代码
wxpay.v2.mmpaymkttransfers.sendredpack.post({
  nonce_str: Formatter.nonce(), // 自v0.9.0起可以无需声明
  mch_billno: '********201411111234567890',
  mch_id: '********',
  wxappid: 'wx8888888888888888',
  send_name: '鹅企支付',
  re_openid: 'oxTWIuGaIt6gTKsQRLau2M0yL16E',
  total_amount: '1000',
  total_num: '1',
  wishing: 'HAPPY BIRTHDAY',
  client_ip: '***********',
  act_name: '回馈活动',
  remark: '会员回馈活动',
  scene_id: 'PRODUCT_4',
})
.then(res => console.info(res.data))
.catch(({response: {status, statusText, data}}) => console.error(status, statusText, data))
企业付款到银行卡-获取RSA公钥
示例代码
const {Rsa} = require('wechatpay-axios-plugin')

wxpay.v2.risk.getpublickey.post({
  mch_id: '1900000109',
  sign_type: Hash.ALGO_MD5,
  nonce_str: Formatter.nonce(), // 自v0.9.0起可以无需声明
}, {
  baseURL: 'https://fraud.mch.weixin.qq.com/',
  // 声明请求是私有ssl协议，对应加载初始化的 merchant{key,cert} 参数
  security: true,
})
.then(res => {
  const b64 = res.data.pub_key.trim().split(/\r?\n/).slice(1, -1).join('')
  console.info(Rsa.fromPkcs1(b64, Rsa.KEY_TYPE_PUBLIC))
})
.catch(({response: {status, statusText, data}}) => console.error(status, statusText, data))
更多示例代码访问这里

企业微信
企业微信的企业支付，数据请求包需要额外的签名，仅需做如下简单扩展适配，即可支持；以下签名注入函数所需的两个参数agentId agentSecret来自企业微信工作台，以下为示例值。

const {Hash} = require('wechatpay-axios-plugin')
const agentId = '0' // 企业微信应用ID，0是企微内置的特殊应用
const agentSecret = Hash.keyObjectFrom('from_wework_agent_special_string') // 自v0.9.0可用